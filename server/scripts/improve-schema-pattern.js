#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function toPascalCase(str) {
  const camel = toCamelCase(str)
  return camel.charAt(0).toUpperCase() + camel.slice(1)
}

function createImprovedSchema(serviceName) {
  const camelName = toCamelCase(serviceName)
  const pascalName = toPascalCase(serviceName)
  
  return `// TypeBox schema for ${serviceName} service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ${camelName}Schema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type ${pascalName} = Static<typeof ${camelName}Schema>
export const ${camelName}Validator = getValidator(${camelName}Schema, dataValidator)
export const ${camelName}Resolver = resolve<${pascalName}, HookContext>({})
export const ${camelName}ExternalResolver = resolve<${pascalName}, HookContext>({})

// Schema for creating new data - exclude _id and auto-generated common fields
export const ${camelName}DataSchema = Type.Omit(${camelName}Schema, ['_id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy'])

export type ${pascalName}Data = Static<typeof ${camelName}DataSchema>
export const ${camelName}DataValidator = getValidator(${camelName}DataSchema, dataValidator)
export const ${camelName}DataResolver = resolve<${pascalName}Data, HookContext>({})

// Schema for updating existing data - make all fields optional
export const ${camelName}PatchSchema = Type.Partial(Type.Omit(${camelName}Schema, ['_id']))

export type ${pascalName}Patch = Static<typeof ${camelName}PatchSchema>
export const ${camelName}PatchValidator = getValidator(${camelName}PatchSchema, dataValidator)
export const ${camelName}PatchResolver = resolve<${pascalName}Patch, HookContext>({})

// Schema for allowed query properties
const ${camelName}QueryProperties = Type.Pick(${camelName}Schema, ['_id', 'name', 'type', 'active'])
export const ${camelName}QuerySchema = querySyntax(${camelName}QueryProperties)
export type ${pascalName}Query = Static<typeof ${camelName}QuerySchema>
export const ${camelName}QueryValidator = getValidator(${camelName}QuerySchema, queryValidator)
export const ${camelName}QueryResolver = resolve<${pascalName}Query, HookContext>({})
`
}

console.log('📋 Improved TypeBox Schema Pattern:')
console.log('✅ Main schema: All fields defined once')
console.log('✅ Data schema: Type.Omit(mainSchema, [auto-generated fields])')
console.log('✅ Patch schema: Type.Partial(Type.Omit(mainSchema, [_id]))')
console.log('✅ Query schema: Type.Pick(mainSchema, [queryable fields])')
console.log('')
console.log('🎯 Benefits:')
console.log('- No field duplication')
console.log('- Single source of truth')
console.log('- Automatic consistency')
console.log('- Easier maintenance')

export { createImprovedSchema }
