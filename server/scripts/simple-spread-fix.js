#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function fixService(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Skip if already using spread pattern
    if (content.includes('...Type.Omit(')) {
      console.log(`✅ ${serviceName}: Already using spread pattern`)
      return false
    }
    
    let updated = false
    
    // 1. Fix dataSchema - simple find and replace
    const dataSchemaPattern = `export const ${camelName}DataSchema = Type.Object({`
    const dataSchemaIndex = content.indexOf(dataSchemaPattern)
    
    if (dataSchemaIndex !== -1) {
      // Find the end of this Type.Object definition
      let braceCount = 0
      let endIndex = dataSchemaIndex + dataSchemaPattern.length
      
      for (let i = endIndex; i < content.length; i++) {
        if (content[i] === '{') braceCount++
        if (content[i] === '}') {
          braceCount--
          if (braceCount === -1) {
            // Found the closing brace of the Type.Object
            endIndex = content.indexOf('}, { additionalProperties: false })', i) + 35
            break
          }
        }
      }
      
      if (endIndex > dataSchemaIndex) {
        const newDataSchema = `export const ${camelName}DataSchema = Type.Object({
  ...Type.Omit(${camelName}Schema, ['_id']).properties
}, { additionalProperties: false })`
        
        content = content.substring(0, dataSchemaIndex) + newDataSchema + content.substring(endIndex)
        updated = true
      }
    }
    
    // 2. Fix patchSchema - only if it's Type.Partial(dataSchema)
    const simplePatchPattern = `export const ${camelName}PatchSchema = Type.Partial(${camelName}DataSchema)`
    const simplePatchIndex = content.indexOf(simplePatchPattern)
    
    if (simplePatchIndex !== -1) {
      const newPatchSchema = `export const ${camelName}PatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(${camelName}Schema, ['_id'])).properties
}, { additionalProperties: false })`
      
      content = content.substring(0, simplePatchIndex) + newPatchSchema + content.substring(simplePatchIndex + simplePatchPattern.length)
      updated = true
    }
    
    if (updated) {
      fs.writeFileSync(schemaPath, content)
      console.log(`✅ Fixed ${serviceName}`)
      return true
    } else {
      console.log(`⚠️  ${serviceName}: No simple patterns found (may have complex MongoDB operators)`)
      return false
    }
    
  } catch (error) {
    console.log(`❌ Error fixing ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })

console.log(`🚀 Applying simple spread pattern to ${services.length} services...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (fixService(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Fixed: ${successCount}/${services.length} services`)
console.log('🎯 Only simple patterns fixed - complex MongoDB operators left as-is')
console.log('💡 Manual fix needed for services with complex patch schemas')
