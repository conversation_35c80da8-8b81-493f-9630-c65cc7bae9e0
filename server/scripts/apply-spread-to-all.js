#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function applySpreadPattern(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Skip if already using spread pattern
    if (content.includes('...Type.Omit(')) {
      console.log(`✅ ${serviceName}: Already using spread pattern`)
      return false
    }
    
    // Replace dataSchema with spread pattern
    const dataSchemaStart = content.indexOf(`export const ${camelName}DataSchema = Type.Object({`)
    if (dataSchemaStart !== -1) {
      const dataSchemaEnd = content.indexOf('}, { additionalProperties: false })', dataSchemaStart) + 35
      
      if (dataSchemaEnd > dataSchemaStart) {
        const newDataSchema = `export const ${camelName}DataSchema = Type.Object({
  ...Type.Omit(${camelName}Schema, ['_id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: false })`
        
        content = content.substring(0, dataSchemaStart) + newDataSchema + content.substring(dataSchemaEnd)
      }
    }
    
    // Replace patchSchema with spread pattern (preserve MongoDB operators)
    const patchSchemaStart = content.indexOf(`export const ${camelName}PatchSchema = Type.Object({`)
    if (patchSchemaStart !== -1) {
      const patchSchemaEnd = content.indexOf('}, { additionalProperties: false })', patchSchemaStart) + 35
      
      if (patchSchemaEnd > patchSchemaStart) {
        const patchContent = content.substring(patchSchemaStart, patchSchemaEnd)
        
        // Extract MongoDB operators (lines containing $)
        const mongoOps = []
        const lines = patchContent.split('\n')
        let inMongoOp = false
        let currentOp = ''
        
        lines.forEach(line => {
          if (line.includes('$')) {
            inMongoOp = true
            currentOp = line
          } else if (inMongoOp) {
            currentOp += '\n' + line
            if (line.includes('})),') || line.includes('}, { additionalProperties: false }))')) {
              mongoOps.push(currentOp)
              inMongoOp = false
              currentOp = ''
            }
          }
        })
        
        let newPatchSchema = `export const ${camelName}PatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(${camelName}Schema, ['_id'])).properties`
        
        if (mongoOps.length > 0) {
          newPatchSchema += ',\n  // MongoDB operators\n  ' + mongoOps.join(',\n  ')
        }
        
        newPatchSchema += '\n}, { additionalProperties: false })'
        
        content = content.substring(0, patchSchemaStart) + newPatchSchema + content.substring(patchSchemaEnd)
      }
    }
    
    fs.writeFileSync(schemaPath, content)
    console.log(`✅ Applied spread pattern to ${serviceName}`)
    return true
    
  } catch (error) {
    console.log(`❌ Error processing ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })

console.log(`🚀 Applying spread pattern to ALL ${services.length} services...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (applySpreadPattern(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Pattern applied to: ${successCount}/${services.length} services`)
console.log('🎯 All services now use single source of truth pattern!')
