#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const corruptedServices = [
  'challenges', 'comps', 'drops', 'errs', 'fbs', 'flow-charts', 
  'gps', 'ims', 'issues', 'passkeys', 'pings', 'reqs'
]

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function toPascalCase(str) {
  const camel = toCamelCase(str)
  return camel.charAt(0).toUpperCase() + camel.slice(1)
}

function createSchema(serviceName) {
  const camelName = toCamelCase(serviceName)
  const pascalName = toPascalCase(serviceName)
  
  return `// TypeBox schema for ${serviceName} service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ${camelName}Schema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type ${pascalName} = Static<typeof ${camelName}Schema>
export const ${camelName}Validator = getValidator(${camelName}Schema, dataValidator)
export const ${camelName}Resolver = resolve<${pascalName}, HookContext>({})
export const ${camelName}ExternalResolver = resolve<${pascalName}, HookContext>({})

export const ${camelName}DataSchema = Type.Object({
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

export type ${pascalName}Data = Static<typeof ${camelName}DataSchema>
export const ${camelName}DataValidator = getValidator(${camelName}DataSchema, dataValidator)
export const ${camelName}DataResolver = resolve<${pascalName}Data, HookContext>({})

export const ${camelName}PatchSchema = Type.Partial(${camelName}DataSchema)
export type ${pascalName}Patch = Static<typeof ${camelName}PatchSchema>
export const ${camelName}PatchValidator = getValidator(${camelName}PatchSchema, dataValidator)
export const ${camelName}PatchResolver = resolve<${pascalName}Patch, HookContext>({})

const ${camelName}QueryProperties = Type.Pick(${camelName}Schema, ['_id', 'name', 'type', 'active'], { additionalProperties: false })
export const ${camelName}QuerySchema = querySyntax(${camelName}QueryProperties)
export type ${pascalName}Query = Static<typeof ${camelName}QuerySchema>
export const ${camelName}QueryValidator = getValidator(${camelName}QuerySchema, queryValidator)
export const ${camelName}QueryResolver = resolve<${pascalName}Query, HookContext>({})
`
}

corruptedServices.forEach(serviceName => {
  const schemaPath = path.join(__dirname, '..', 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(path.dirname(schemaPath))) {
    console.log(`⚠️  Service directory not found: ${serviceName}`)
    return
  }
  
  const schema = createSchema(serviceName)
  fs.writeFileSync(schemaPath, schema)
  console.log(`✅ Recreated schema for ${serviceName}`)
})

console.log('🎉 All corrupted schemas recreated!')
