#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function toPascalCase(str) {
  const camel = toCamelCase(str)
  return camel.charAt(0).toUpperCase() + camel.slice(1)
}

function updateSchemaFile(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    console.log(`⚠️  Schema file not found: ${serviceName}`)
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    
    const camelName = toCamelCase(serviceName)
    const pascalName = toPascalCase(serviceName)
    
    // Check if it already uses the improved pattern
    if (content.includes('Type.Omit(') && content.includes('Type.Partial(Type.Omit(')) {
      console.log(`✅ ${serviceName}: Already using improved pattern`)
      return false
    }
    
    // Find the data schema definition and replace it
    const dataSchemaRegex = new RegExp(
      `export const ${camelName}DataSchema = Type\\.Object\\({[\\s\\S]*?}\\s*,\\s*{[\\s\\S]*?}\\)`,
      'g'
    )
    
    if (dataSchemaRegex.test(content)) {
      content = content.replace(
        dataSchemaRegex,
        `export const ${camelName}DataSchema = Type.Omit(${camelName}Schema, ['_id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy'])`
      )
    }
    
    // Find the patch schema definition and replace it
    const patchSchemaRegex = new RegExp(
      `export const ${camelName}PatchSchema = Type\\.(Partial\\(${camelName}DataSchema\\)|Object\\({[\\s\\S]*?}\\s*,\\s*{[\\s\\S]*?}\\))`,
      'g'
    )
    
    if (patchSchemaRegex.test(content)) {
      content = content.replace(
        patchSchemaRegex,
        `export const ${camelName}PatchSchema = Type.Partial(Type.Omit(${camelName}Schema, ['_id']))`
      )
    }
    
    fs.writeFileSync(schemaPath, content)
    console.log(`✅ Updated ${serviceName} to use improved pattern`)
    return true
  } catch (error) {
    console.log(`❌ Error updating ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services that have schema files
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })

console.log(`🚀 Updating ${services.length} services to use improved schema pattern...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (updateSchemaFile(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Schema updates complete: ${successCount}/${services.length} services updated`)
console.log('\n🎯 Improved pattern benefits:')
console.log('- No field duplication')
console.log('- Single source of truth')
console.log('- Automatic consistency')
console.log('- Easier maintenance')
