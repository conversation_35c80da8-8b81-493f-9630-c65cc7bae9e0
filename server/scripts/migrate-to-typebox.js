#!/usr/bin/env node

/**
 * Migration script to help convert services from JSON Schema to TypeBox
 * This script helps automate the bulk conversion process
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// List of services to migrate
const servicesToMigrate = [
  'enrollments', 'plans', 'visits',
  'households', 'mbrs', 'logins', 'orgs', 'groups', 'networks',
  'markets', 'rates', 'bundles', 'contracts', 'uploads', 'teams',
  'leads', 'offers', 'shops', 'specs', 'threads', 'wallets',
  'bills', 'ledgers', 'expenses', 'funds', 'budgets', 'caps',
  'cares', 'conditions', 'meds', 'hosts', 'refs', 'reqs',
  'issues', 'errs', 'pings', 'ims', 'fbs', 'fb-res', 'drops',
  'challenges', 'passkeys', 'my-ip', 'gps', 'cams', 'cobras',
  'bank-accounts', 'bill-erasers', 'calendars', 'change-logs',
  'claim-payments', 'claim-reqs', 'comps', 'cross-sections',
  'doc-requests', 'doc-templates', 'funds-requests', 'grp-mbrs',
  'health-shares', 'junk-drawers', 'plan-docs', 'price-estimates',
  'sales-taxes', 'se-plans'
]

// Services already migrated
const migratedServices = ['ai-chats', 'practitioners', 'care-accounts', 'claims', 'procedures', 'providers']

// Function to update service import
function updateServiceImport(serviceName) {
  const servicePath = path.join(__dirname, '..', 'src', 'services', serviceName, `${serviceName}.ts`)
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️  Service file not found: ${servicePath}`)
    return false
  }
  
  try {
    let content = fs.readFileSync(servicePath, 'utf8')
    
    // Replace the schema import
    const oldImport = `} from './${serviceName}.schema.js'`
    const newImport = `} from './${serviceName}.typebox-schema.js'`
    
    if (content.includes(oldImport)) {
      content = content.replace(oldImport, newImport)
      fs.writeFileSync(servicePath, content)
      console.log(`✅ Updated import for ${serviceName}`)
      return true
    } else {
      console.log(`⚠️  Import pattern not found in ${serviceName}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error updating ${serviceName}: ${error.message}`)
    return false
  }
}

// Function to check if TypeBox schema exists
function hasTypeBoxSchema(serviceName) {
  const schemaPath = path.join(__dirname, '..', 'src', 'services', serviceName, `${serviceName}.typebox-schema.ts`)
  return fs.existsSync(schemaPath)
}

// Main migration function
function runMigration() {
  console.log('🚀 Starting TypeBox Migration\n')
  
  console.log('📊 Migration Status:')
  console.log(`✅ Already migrated: ${migratedServices.length} services`)
  console.log(`📋 Remaining to migrate: ${servicesToMigrate.length} services\n`)
  
  console.log('🔍 Checking for existing TypeBox schemas...')
  
  const readyToUpdate = []
  const needsSchemaCreation = []
  
  servicesToMigrate.forEach(serviceName => {
    if (hasTypeBoxSchema(serviceName)) {
      readyToUpdate.push(serviceName)
    } else {
      needsSchemaCreation.push(serviceName)
    }
  })
  
  console.log(`✅ Ready to update imports: ${readyToUpdate.length} services`)
  console.log(`📝 Need schema creation: ${needsSchemaCreation.length} services\n`)
  
  if (readyToUpdate.length > 0) {
    console.log('🔄 Updating service imports...')
    readyToUpdate.forEach(serviceName => {
      updateServiceImport(serviceName)
    })
  }
  
  if (needsSchemaCreation.length > 0) {
    console.log('\n📝 Services that need TypeBox schema creation:')
    needsSchemaCreation.forEach(serviceName => {
      console.log(`   - ${serviceName}`)
    })
    console.log('\nCreate TypeBox schemas for these services using the pattern from ai-chats, practitioners, care-accounts, and claims.')
  }
  
  console.log('\n🎉 Migration script complete!')
}

// Run the migration
runMigration()

export {
  servicesToMigrate,
  migratedServices,
  updateServiceImport,
  hasTypeBoxSchema,
  runMigration
}
