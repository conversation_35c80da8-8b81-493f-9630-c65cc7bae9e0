#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const servicesToFix = [
  'challenges', 'comps', 'drops', 'errs', 'fb-res', 'fbs', 'flow-charts', 
  'gps', 'ims', 'issues', 'passkeys', 'pings', 'reqs'
]

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function toPascalCase(str) {
  const camel = toCamelCase(str)
  return camel.charAt(0).toUpperCase() + camel.slice(1)
}

function fixSchemaFile(serviceName) {
  const schemaPath = path.join(__dirname, '..', 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    console.log(`⚠️  Schema file not found: ${serviceName}`)
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    
    const camelName = toCamelCase(serviceName)
    const pascalName = toPascalCase(serviceName)
    
    // Fix all the corrupted type names
    const replacements = [
      // Main type
      { pattern: /export type U[a-z]+/g, replacement: `export type ${pascalName}` },
      { pattern: /export type U[a-zA-Z]+Data/g, replacement: `export type ${pascalName}Data` },
      { pattern: /export type U[a-zA-Z]+Patch/g, replacement: `export type ${pascalName}Patch` },
      { pattern: /export type U[a-zA-Z]+Query/g, replacement: `export type ${pascalName}Query` },
      
      // Resolver references
      { pattern: /resolve<U[a-z]+,/g, replacement: `resolve<${pascalName},` },
      { pattern: /resolve<U[a-zA-Z]+Data,/g, replacement: `resolve<${pascalName}Data,` },
      { pattern: /resolve<U[a-zA-Z]+Patch,/g, replacement: `resolve<${pascalName}Patch,` },
      { pattern: /resolve<U[a-zA-Z]+Query,/g, replacement: `resolve<${pascalName}Query,` }
    ]
    
    let updated = false
    replacements.forEach(({ pattern, replacement }) => {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement)
        updated = true
      }
    })
    
    if (updated) {
      fs.writeFileSync(schemaPath, content)
      console.log(`✅ Fixed ${serviceName}`)
      return true
    } else {
      console.log(`⚠️  No changes needed for ${serviceName}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error fixing ${serviceName}: ${error.message}`)
    return false
  }
}

// Fix all services
console.log('🚀 Starting schema naming fixes...\n')

let successCount = 0
servicesToFix.forEach(serviceName => {
  if (fixSchemaFile(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Naming fixes complete: ${successCount}/${servicesToFix.length} services fixed`)

export { fixSchemaFile }
