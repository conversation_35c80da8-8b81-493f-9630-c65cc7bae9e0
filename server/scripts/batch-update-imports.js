#!/usr/bin/env node

/**
 * Batch update script to convert service imports from JSON Schema to TypeBox
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Services to update
const servicesToUpdate = [
  'enrollments', 'plans', 'visits', 'households', 'mbrs', 'logins', 'orgs', 'groups',
  'networks', 'markets', 'rates', 'bundles', 'contracts', 'uploads'
]

function updateServiceImport(serviceName) {
  const servicePath = path.join(__dirname, '..', 'src', 'services', serviceName, `${serviceName}.ts`)
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️  Service file not found: ${servicePath}`)
    return false
  }
  
  try {
    let content = fs.readFileSync(servicePath, 'utf8')
    
    // Replace the schema import patterns
    const patterns = [
      { old: `} from './${serviceName}.schema.js'`, new: `} from './${serviceName}.typebox-schema.js'` },
      { old: `export * from './${serviceName}.schema.js'`, new: `export * from './${serviceName}.typebox-schema.js'` }
    ]
    
    let updated = false
    patterns.forEach(pattern => {
      if (content.includes(pattern.old)) {
        content = content.replace(pattern.old, pattern.new)
        updated = true
      }
    })
    
    if (updated) {
      fs.writeFileSync(servicePath, content)
      console.log(`✅ Updated import for ${serviceName}`)
      return true
    } else {
      console.log(`⚠️  No import pattern found in ${serviceName}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error updating ${serviceName}: ${error.message}`)
    return false
  }
}

// Run batch update
console.log('🚀 Starting batch import update...\n')

let successCount = 0
let totalCount = servicesToUpdate.length

servicesToUpdate.forEach(serviceName => {
  if (updateServiceImport(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Batch update complete: ${successCount}/${totalCount} services updated`)

export { updateServiceImport }
