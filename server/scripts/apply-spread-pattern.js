#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function toPascalCase(str) {
  const camel = toCamelCase(str)
  return camel.charAt(0).toUpperCase() + camel.slice(1)
}

// Apply the spread pattern to a single service
function applySpreadPattern(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Check if already using spread pattern
    if (content.includes('...Type.Omit(') || content.includes('...Type.Partial(Type.Omit(')) {
      console.log(`✅ ${serviceName}: Already using spread pattern`)
      return false
    }
    
    // For dataSchema: Replace with spread of main schema (excluding auto-generated fields)
    const dataSchemaPattern = new RegExp(
      `export const ${camelName}DataSchema = Type\\.Object\\({[\\s\\S]*?}\\s*,\\s*{[\\s\\S]*?}\\)`,
      'g'
    )
    
    if (dataSchemaPattern.test(content)) {
      content = content.replace(
        dataSchemaPattern,
        `export const ${camelName}DataSchema = Type.Object({
  ...Type.Omit(${camelName}Schema, ['_id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']).properties
  // No unique fields for data schema - all fields come from main schema
}, { additionalProperties: false })`
      )
    }
    
    // For patchSchema: Find unique MongoDB operators and preserve them
    const patchSchemaMatch = content.match(
      new RegExp(`export const ${camelName}PatchSchema = Type\\.Object\\({([\\s\\S]*?)}\\s*,\\s*{[\\s\\S]*?}\\)`)
    )
    
    if (patchSchemaMatch) {
      const patchContent = patchSchemaMatch[1]
      
      // Extract MongoDB operators (fields starting with $)
      const mongoOperators = []
      const operatorMatches = patchContent.match(/\$\w+:[^,}]+(?:,|\s*})/g) || []
      
      operatorMatches.forEach(match => {
        // Clean up the match and extract the full operator definition
        const operatorName = match.match(/\$\w+/)[0]
        if (patchContent.includes(`${operatorName}: Type.Optional(Type.Object({`)) {
          // Complex operator with object definition
          const start = patchContent.indexOf(`${operatorName}: Type.Optional(Type.Object({`)
          const end = patchContent.indexOf('})), ', start) + 4
          if (end > start) {
            mongoOperators.push(patchContent.substring(start, end))
          }
        } else {
          // Simple operator
          mongoOperators.push(match.replace(/,$/, ''))
        }
      })
      
      let newPatchSchema = `export const ${camelName}PatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(${camelName}Schema, ['_id'])).properties`
      
      if (mongoOperators.length > 0) {
        newPatchSchema += `,
  // Unique fields for patch operations (MongoDB operators not in main schema)
  ${mongoOperators.join(',\n  ')}`
      }
      
      newPatchSchema += `
}, { additionalProperties: false })`
      
      content = content.replace(patchSchemaMatch[0], newPatchSchema)
    }
    
    fs.writeFileSync(schemaPath, content)
    console.log(`✅ Applied spread pattern to ${serviceName}`)
    return true
    
  } catch (error) {
    console.log(`❌ Error processing ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services that have schema files
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })
  .filter(service => service !== 'logins') // Skip logins since we already fixed it

console.log(`🚀 Applying spread pattern to ${services.length} services...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (applySpreadPattern(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Spread pattern applied: ${successCount}/${services.length} services updated`)
console.log('\n🎯 Benefits of spread pattern:')
console.log('- No field duplication')
console.log('- Single source of truth')
console.log('- Automatic field inheritance')
console.log('- Only unique fields explicitly defined')
