#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function extractSchemaKeys(schemaContent) {
  // Extract keys from Type.Object({ ... }) definition
  const match = schemaContent.match(/Type\.Object\(\{([^}]+(?:\{[^}]*\}[^}]*)*)\}/)
  if (!match) return []
  
  const content = match[1]
  const keys = []
  
  // Simple key extraction - look for property names followed by colon
  const keyMatches = content.match(/^\s*(\w+):/gm) || []
  keyMatches.forEach(match => {
    const key = match.trim().replace(':', '')
    if (key && !key.startsWith('...')) {
      keys.push(key)
    }
  })
  
  return keys
}

function applySpreadPattern(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Skip if already using spread pattern
    if (content.includes('...Type.Omit(')) {
      console.log(`✅ ${serviceName}: Already using spread pattern`)
      return false
    }
    
    // Extract main schema keys
    const mainSchemaMatch = content.match(new RegExp(`export const ${camelName}Schema = (Type\\.Object\\({[\\s\\S]*?}\\s*,\\s*{[\\s\\S]*?}\\))`))
    if (!mainSchemaMatch) {
      console.log(`⚠️  ${serviceName}: Could not find main schema`)
      return false
    }
    
    const mainSchemaKeys = extractSchemaKeys(mainSchemaMatch[1])
    
    // Process dataSchema
    const dataSchemaMatch = content.match(new RegExp(`(export const ${camelName}DataSchema = Type\\.Object\\({[\\s\\S]*?}\\s*,\\s*{[\\s\\S]*?}\\))`))
    if (dataSchemaMatch) {
      const dataSchemaKeys = extractSchemaKeys(dataSchemaMatch[1])
      const uniqueDataKeys = dataSchemaKeys.filter(key => !mainSchemaKeys.includes(key))
      
      let newDataSchema = `export const ${camelName}DataSchema = Type.Object({
  ...Type.Omit(${camelName}Schema, ['_id']).properties`
      
      if (uniqueDataKeys.length > 0) {
        newDataSchema += `,
  // Unique fields for data creation
  ${uniqueDataKeys.map(key => `${key}: Type.Optional(Type.Any())`).join(',\n  ')}`
      }
      
      newDataSchema += `
}, { additionalProperties: false })`
      
      content = content.replace(dataSchemaMatch[0], newDataSchema)
    }
    
    // Process patchSchema
    const patchSchemaMatch = content.match(new RegExp(`(export const ${camelName}PatchSchema = Type\\.[\\s\\S]*?}\\s*,\\s*{[\\s\\S]*?}\\))`))
    if (patchSchemaMatch) {
      const patchSchemaKeys = extractSchemaKeys(patchSchemaMatch[1])
      const uniquePatchKeys = patchSchemaKeys.filter(key => !mainSchemaKeys.includes(key))
      
      let newPatchSchema = `export const ${camelName}PatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(${camelName}Schema, ['_id'])).properties`
      
      if (uniquePatchKeys.length > 0) {
        // Extract the actual MongoDB operator definitions from original content
        const mongoOps = []
        uniquePatchKeys.forEach(key => {
          if (key.startsWith('$')) {
            const keyPattern = new RegExp(`${key}:\\s*Type\\.Optional\\([\\s\\S]*?\\)(?:\\s*,|\\s*})`);
            const match = patchSchemaMatch[0].match(keyPattern);
            if (match) {
              mongoOps.push(`  ${match[0].replace(/,$/, '')}`)
            }
          }
        })
        
        if (mongoOps.length > 0) {
          newPatchSchema += `,
  // MongoDB operators
${mongoOps.join(',\n')}`
        }
      }
      
      newPatchSchema += `
}, { additionalProperties: false })`
      
      content = content.replace(patchSchemaMatch[0], newPatchSchema)
    }
    
    fs.writeFileSync(schemaPath, content)
    console.log(`✅ Applied spread pattern to ${serviceName}`)
    return true
    
  } catch (error) {
    console.log(`❌ Error processing ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })
  .filter(service => service !== 'calendars') // Skip calendars since we already fixed it

console.log(`🚀 Applying spread pattern to ${services.length} services (only omitting _id)...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (applySpreadPattern(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Pattern applied: ${successCount}/${services.length} services updated`)
console.log('🎯 Data schema: Only omits _id (allows setting timestamps for migrations)')
console.log('🎯 Patch schema: Only omits _id (allows updating all fields)')
