// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js';

// Main data model schema
export const junkDrawersSchema = {
  $id: 'JunkDrawers',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'drawer', 'itemName', 'itemId'],
  properties: {
    _id: ObjectIdSchema(),
    drawer: {type: 'string'},
    itemName: { type: 'string' },
    itemId: { type: 'string'},
    data: {},
    ...commonFields.properties
  }
} as const
export type JunkDrawers = FromSchema<typeof junkDrawersSchema>
export const junkDrawersValidator = getValidator(junkDrawersSchema, dataValidator)
export const junkDrawersResolver = resolve<JunkDrawers, HookContext>({})

export const junkDrawersExternalResolver = resolve<JunkDrawers, HookContext>({})

// Schema for creating new data
export const junkDrawersDataSchema = {
  $id: 'JunkDrawersData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...junkDrawersSchema.properties
  }
} as const
export type JunkDrawersData = FromSchema<typeof junkDrawersDataSchema>
export const junkDrawersDataValidator = getValidator(junkDrawersDataSchema, dataValidator)
export const junkDrawersDataResolver = resolve<JunkDrawersData, HookContext>({})

// Schema for updating existing data
export const junkDrawersPatchSchema = {
  $id: 'JunkDrawersPatch',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...junkDrawersSchema.properties,
    ...commonPatch(junkDrawersSchema.properties).properties
  }
} as const
export type JunkDrawersPatch = FromSchema<typeof junkDrawersPatchSchema>
export const junkDrawersPatchValidator = getValidator(junkDrawersPatchSchema, dataValidator)
export const junkDrawersPatchResolver = resolve<JunkDrawersPatch, HookContext>({})

// Schema for allowed query properties
export const junkDrawersQuerySchema = {
  $id: 'JunkDrawersQuery',
  type: 'object',
  additionalProperties: true,
  properties: {
    ...querySyntax(junkDrawersSchema.properties),
    ...commonQueries.properties,
    itemName: {},
    data: {},
    pTypes: {}
  }
} as const
export type JunkDrawersQuery = FromSchema<typeof junkDrawersQuerySchema>
export const junkDrawersQueryValidator = getValidator(junkDrawersQuerySchema, queryValidator)
export const junkDrawersQueryResolver = resolve<JunkDrawersQuery, HookContext>({})
