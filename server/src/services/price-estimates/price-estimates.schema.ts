// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema} from '../../utils/common/schemas.js';

export const priceKeys = {
    type: 'object',
    additionalProperties: false,
    properties: {
        medicare: {type: 'number'},
        cash: {type: 'number'}
    }
} as const

export const estimateBodySchema = {
    type: 'object',
    additionalProperties: true,
    properties: {
        ...priceKeys.properties,
        medicare_low: {type: 'number'},
        medicare_high: {type: 'number'},
        description: {type: 'string'},
        cash_low: {type: 'number'},
        cash_high: {type: 'number'},
        source: {type: 'string'},
        estimatedAt: {}
    }
} as const
// Main data model schema
export const priceEstimatesSchema = {
    $id: 'PriceEstimates',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'code', 'locationCode'],
    properties: {
        _id: ObjectIdSchema(),
        ...estimateBodySchema.properties,
        alts: {type: 'array', items: estimateBodySchema},
        code: {type: 'string'},
        rxcui: { type: 'string'},
        locationCode: {type: 'string'},
        listPrice: { type: 'number' },
        carrier: { type: 'string' },
        files: { type: 'array', items: imageSchema },
        session: {type: 'string'},
        zip_code: {type: 'string'},
        state: { type: 'string' },
        ...commonFields.properties
    }
} as const
export type PriceEstimates = FromSchema<typeof priceEstimatesSchema>
export const priceEstimatesValidator = getValidator(priceEstimatesSchema, dataValidator)
export const priceEstimatesResolver = resolve<PriceEstimates, HookContext>({})

export const priceEstimatesExternalResolver = resolve<PriceEstimates, HookContext>({})

// Schema for creating new data
export const priceEstimatesDataSchema = {
    $id: 'PriceEstimatesData',
    type: 'object',
    additionalProperties: false,
    required: ['code', 'locationCode'],
    properties: {
        ...priceEstimatesSchema.properties
    }
} as const
export type PriceEstimatesData = FromSchema<typeof priceEstimatesDataSchema>
export const priceEstimatesDataValidator = getValidator(priceEstimatesDataSchema, dataValidator)
export const priceEstimatesDataResolver = resolve<PriceEstimatesData, HookContext>({})

const pushPull = [
    {path: 'alts', type: estimateBodySchema},
]
// Schema for updating existing data
export const priceEstimatesPatchSchema = {
    $id: 'PriceEstimatesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...priceEstimatesSchema.properties,
        ...commonPatch(priceEstimatesSchema.properties).properties,
        $addToSet: addToSet(pushPull)
    }
} as const
export type PriceEstimatesPatch = FromSchema<typeof priceEstimatesPatchSchema>
export const priceEstimatesPatchValidator = getValidator(priceEstimatesPatchSchema, dataValidator)
export const priceEstimatesPatchResolver = resolve<PriceEstimatesPatch, HookContext>({})

// Schema for allowed query properties
export const priceEstimatesQuerySchema = {
    $id: 'PriceEstimatesQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(priceEstimatesSchema.properties)
    }
} as const
export type PriceEstimatesQuery = FromSchema<typeof priceEstimatesQuerySchema>
export const priceEstimatesQueryValidator = getValidator(priceEstimatesQuerySchema, queryValidator)
export const priceEstimatesQueryResolver = resolve<PriceEstimatesQuery, HookContext>({})
