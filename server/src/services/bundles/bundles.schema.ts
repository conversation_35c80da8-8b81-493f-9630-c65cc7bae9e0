// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, pull} from '../../utils/common/schemas.js';

// Main data model schema
export const bundlesSchema = {
    $id: 'Bundles',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name', 'provider'],
    properties: {
        _id: ObjectIdSchema(),
        provider: ObjectIdSchema(),
        public: { type: 'boolean' },
        plans: { type: 'array', items: ObjectIdSchema() },
        name: {type: 'string'},
        description: {type: 'string'},
        price: { type: 'number' },
        locations: { type: 'array', items: {type: 'string'} }, //stringified lng|lat from provider locations
        prices: { type: 'array', items: ObjectIdSchema() },
        cats: {type: 'array', items: ObjectIdSchema()},
        networks: {type: 'array', items: ObjectIdSchema()},
        managers: {type: 'array', items: ObjectIdSchema()},
        writers: {type: 'array', items: ObjectIdSchema() },
        ...commonFields.properties
    }
} as const
export type Bundles = FromSchema<typeof bundlesSchema>
export const bundlesValidator = getValidator(bundlesSchema, dataValidator)
export const bundlesResolver = resolve<Bundles, HookContext>({})

export const bundlesExternalResolver = resolve<Bundles, HookContext>({})

// Schema for creating new data
export const bundlesDataSchema = {
    $id: 'BundlesData',
    type: 'object',
    additionalProperties: false,
    required: ['name', 'provider'],
    properties: {
        ...bundlesSchema.properties
    }
} as const
export type BundlesData = FromSchema<typeof bundlesDataSchema>
export const bundlesDataValidator = getValidator(bundlesDataSchema, dataValidator)
export const bundlesDataResolver = resolve<BundlesData, HookContext>({})

const pushPull = [
    { path: 'prices', type: ObjectIdSchema() },
    { path: 'networks', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() },
    { path: 'cats', type: ObjectIdSchema() },
    { path: 'plans', type: ObjectIdSchema() },
]
// Schema for updating existing data
export const bundlesPatchSchema = {
    $id: 'BundlesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...bundlesSchema.properties,
        ...commonPatch(bundlesSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type BundlesPatch = FromSchema<typeof bundlesPatchSchema>
export const bundlesPatchValidator = getValidator(bundlesPatchSchema, dataValidator)
export const bundlesPatchResolver = resolve<BundlesPatch, HookContext>({})

// Schema for allowed query properties
export const bundlesQuerySchema = {
    $id: 'BundlesQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(bundlesSchema.properties),
        name: {}
    }
} as const
export type BundlesQuery = FromSchema<typeof bundlesQuerySchema>
export const bundlesQueryValidator = getValidator(bundlesQuerySchema, queryValidator)
export const bundlesQueryResolver = resolve<BundlesQuery, HookContext>({})
