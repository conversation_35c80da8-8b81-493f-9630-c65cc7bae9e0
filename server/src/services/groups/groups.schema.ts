// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, addToSet, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const groupsSchema = {
    $id: 'Groups',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'org', 'name', 'key'],
    properties: {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        key: {type: 'string'},
        name: {type: 'string'},
        description: {type: 'string'},
        applyUcan: {type: 'string'}, //ucan token
        healthPlans: { type: 'array', items: ObjectIdSchema() },
        planClass: { type: 'boolean' },
        memberCount: {type: 'number'},
        memberCountAt: {},
        ...commonFields.properties
    }
} as const
export type Groups = FromSchema<typeof groupsSchema>
export const groupsValidator = getValidator(groupsSchema, dataValidator)
export const groupsResolver = resolve<Groups, HookContext>({})

export const groupsExternalResolver = resolve<Groups, HookContext>({})

// Schema for creating new data
export const groupsDataSchema = {
    $id: 'GroupsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...groupsSchema.properties
    }
} as const
export type GroupsData = FromSchema<typeof groupsDataSchema>
export const groupsDataValidator = getValidator(groupsDataSchema, dataValidator)
export const groupsDataResolver = resolve<GroupsData, HookContext>({})


// Schema for updating existing data
export const groupsPatchSchema = {
    $id: 'GroupsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...groupsSchema.properties,
        ...commonPatch(groupsSchema.properties).properties,
        $addToSet: addToSet([{ path: 'members', type: ObjectIdSchema() }] ),
        $pull: {
            type: 'object',
            properties: {
                members: { anyOf: [ ObjectIdSchema(), { type: 'array', items: ObjectIdSchema() }] }
            }
        }
    }
} as const
export type GroupsPatch = FromSchema<typeof groupsPatchSchema>
export const groupsPatchValidator = getValidator(groupsPatchSchema, dataValidator)
export const groupsPatchResolver = resolve<GroupsPatch, HookContext>({})

// Schema for allowed query properties
export const groupsQuerySchema = {
    $id: 'GroupsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(groupsSchema.properties),
        name: {}
        // groups: {}
    }
} as const
export type GroupsQuery = FromSchema<typeof groupsQuerySchema>
export const groupsQueryValidator = getValidator(groupsQuerySchema, queryValidator)
export const groupsQueryResolver = resolve<GroupsQuery, HookContext>({})
