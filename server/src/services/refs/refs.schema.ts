// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    imageSchema,
    commonFields,
    commonQueries,
    phoneSchema,
    existsQuery,
    operatorQuery, commonPatch
} from "../../utils/common/schemas.js";

import {sendTo} from '../ims/ims.schema.js';

// Main data model schema
export const refsSchema = {
    $id: 'Refs',
    type: 'object',
    additionalProperties: true,
    required: ['_id', 'name', 'person'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        avatar: imageSchema,
        person: ObjectIdSchema(),
        org: ObjectIdSchema(),
        disabled: {type: 'string'},
        disabledBy: ObjectIdSchema(),
        disabledAt: {},
        isHost: ObjectIdSchema(),
        showBy: {type: 'boolean'},
        npn: {type: 'string'},
        sendTo,
        approved: {type: 'boolean'},
        approvedAt: {},
        approvedBy: ObjectIdSchema(),
        leads: {type: 'array', items: ObjectIdSchema()},
        tags: {type: 'array', items: {type: 'string'}},
        phone: phoneSchema,
        email: {type: 'string'},
        calendar: ObjectIdSchema(),
        calendars: {type: 'array', items: ObjectIdSchema()},
        contract: ObjectIdSchema(),
        status: {type: 'string', enum: ['invited', 'req', 'canceled', 'active']},
        teams: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        calendar: ObjectIdSchema(),
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type Refs = FromSchema<typeof refsSchema>
export const refsValidator = getValidator(refsSchema, dataValidator)
export const refsResolver = resolve<Refs, HookContext>({})

export const refsExternalResolver = resolve<Refs, HookContext>({})

// Schema for creating new data
export const refsDataSchema = {
    $id: 'RefsData',
    type: 'object',
    additionalProperties: true,
    required: ['name', 'person'],
    properties: {
        ...refsSchema.properties
    }
} as const
export type RefsData = FromSchema<typeof refsDataSchema>
export const refsDataValidator = getValidator(refsDataSchema, dataValidator)
export const refsDataResolver = resolve<RefsData, HookContext>({})

// Schema for updating existing data
export const refsPatchSchema = {
    $id: 'RefsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...refsSchema.properties,
        ...commonPatch(refsSchema.properties).properties
    }
} as const
export type RefsPatch = FromSchema<typeof refsPatchSchema>
export const refsPatchValidator = getValidator(refsPatchSchema, dataValidator)
export const refsPatchResolver = resolve<RefsPatch, HookContext>({})

// Schema for allowed query properties
export const refsQuerySchema = {
    $id: 'RefsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({
            ...commonQueries.properties,
            ...refsSchema.properties
        }),
        host: operatorQuery(ObjectIdSchema(), [existsQuery()]),
        approvedAt: {},
        name: {},
        email: {},
        phone: {}
    }
} as const
export type RefsQuery = FromSchema<typeof refsQuerySchema>
export const refsQueryValidator = getValidator(refsQuerySchema, queryValidator)
export const refsQueryResolver = resolve<RefsQuery, HookContext>({})
