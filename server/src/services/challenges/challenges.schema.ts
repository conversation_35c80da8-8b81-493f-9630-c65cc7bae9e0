// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';
import {challengeKinds} from '../passkeys/passkeys.schema.js';

// Main data model schema
export const challengesSchema = {
  $id: 'Challenges',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    kind: { enum: [...challengeKinds] },

    // may be absent for usernameless auth
    login: ObjectIdSchema(),

    // the raw challenge; NEVER expose externally (strip in external resolver)
    challenge: { type: 'string', description: 'Opaque WebAuthn challenge (base64url or raw)' },

    // bind challenge to the client connection if using sockets
    connectionId: { type: 'string', description: 'Feathers socket connection id' },

    expiresAt: { type: 'number', description: 'epoch ms; short TTL (e.g., 2–5 minutes)' },
    ...commonFields.properties
  }
} as const
export type Challenges = FromSchema<typeof challengesSchema>
export const challengesValidator = getValidator(challengesSchema, dataValidator)
export const challengesResolver = resolve<Challenges, HookContext>({})

export const challengesExternalResolver = resolve<Challenges, HookContext>({})

// Schema for creating new data
export const challengesDataSchema = {
  $id: 'ChallengesData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...challengesSchema.properties
  }
} as const
export type ChallengesData = FromSchema<typeof challengesDataSchema>
export const challengesDataValidator = getValidator(challengesDataSchema, dataValidator)
export const challengesDataResolver = resolve<ChallengesData, HookContext>({})

// Schema for updating existing data
export const challengesPatchSchema = {
  $id: 'ChallengesPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...challengesSchema.properties,
    ...commonPatch(challengesSchema.properties).properties
  }
} as const
export type ChallengesPatch = FromSchema<typeof challengesPatchSchema>
export const challengesPatchValidator = getValidator(challengesPatchSchema, dataValidator)
export const challengesPatchResolver = resolve<ChallengesPatch, HookContext>({})

// Schema for allowed query properties
export const challengesQuerySchema = {
  $id: 'ChallengesQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(challengesSchema.properties)
  }
} as const
export type ChallengesQuery = FromSchema<typeof challengesQuerySchema>
export const challengesQueryValidator = getValidator(challengesQuerySchema, queryValidator)
export const challengesQueryResolver = resolve<ChallengesQuery, HookContext>({})
