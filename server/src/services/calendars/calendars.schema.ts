// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {rRuleSchema, commonFields, commonPatch} from '../../utils/common/schemas.js';

const daySchedule = {
    type: 'object',
    properties: {
        all: {type: 'boolean'},
        times: {
            type: 'array',
            items: {
                type: 'object', properties: {
                    start: {type: 'number'}, //min: 1, max: 2400},
                    end: {type: 'number'}//min: 1, max: 2400}
                }
            }
        }
    }
} as const

const scheduleSchema = {
    type: 'object',
    properties: {
        days: {
            type: 'object', properties: {
                '0': daySchedule,
                '1': daySchedule,
                '2': daySchedule,
                '3': daySchedule,
                '4': daySchedule,
                '5': daySchedule,
                '6': daySchedule,
            }
        },
        blackoutDates: {
            type: 'array', items: {
                type: 'object', properties: {
                    start: {type: 'string'},
                    end: {type: 'string'},
                    recurrence: rRuleSchema
                }
            }
        }
    }
} as const

const notifySchema = {
    type: 'object',
    properties: {
        active: {type: 'boolean'},
        contactPath: {type: 'string'},
        contact: {type: 'string'}
    }
} as const;

const notificationsSchema = {
    type: 'object',
    properties: {
        before: {type: 'number'},
        sms: notifySchema,
        email: notifySchema,
        internal: notifySchema
    }
} as const;
// Main data model schema
export const calendarsSchema = {
    $id: 'Calendars',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        ownerDefault: {type: 'boolean'},
        owner: ObjectIdSchema(),
        ownerService: {type: 'string'},
        editors: {type: 'array', items: ObjectIdSchema()},
        archived: {type: 'array', items: ObjectIdSchema()},
        past: {type: 'array', items: ObjectIdSchema()},
        future: {type: 'array', items: ObjectIdSchema()},
        notify: notificationsSchema,
        schedule: scheduleSchema,
        tokens: {
            type: 'object',
            properties: {
                google: {type: 'string'}
            }
        },
        ...commonFields.properties
    }
} as const
export type Calendars = FromSchema<typeof calendarsSchema>
export const calendarsValidator = getValidator(calendarsSchema, dataValidator)
export const calendarsResolver = resolve<Calendars, HookContext>({})

export const calendarsExternalResolver = resolve<Calendars, HookContext>({})

// Schema for creating new data
export const calendarsDataSchema = {
    $id: 'CalendarsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...calendarsSchema.properties
    }
} as const
export type CalendarsData = FromSchema<typeof calendarsDataSchema>
export const calendarsDataValidator = getValidator(calendarsDataSchema, dataValidator)
export const calendarsDataResolver = resolve<CalendarsData, HookContext>({})

// Schema for updating existing data
export const calendarsPatchSchema = {
    $id: 'CalendarsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...calendarsSchema.properties,
        ...commonPatch(calendarsSchema.properties).properties
    }
} as const
export type CalendarsPatch = FromSchema<typeof calendarsPatchSchema>
export const calendarsPatchValidator = getValidator(calendarsPatchSchema, dataValidator)
export const calendarsPatchResolver = resolve<CalendarsPatch, HookContext>({})

// Schema for allowed query properties
export const calendarsQuerySchema = {
    $id: 'CalendarsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(calendarsSchema.properties)
    }
} as const
export type CalendarsQuery = FromSchema<typeof calendarsQuerySchema>
export const calendarsQueryValidator = getValidator(calendarsQuerySchema, queryValidator)
export const calendarsQueryResolver = resolve<CalendarsQuery, HookContext>({})
