// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const fbResSchema = {
  $id: 'FbRes',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    person: ObjectIdSchema(),
    form: ObjectIdSchema(),
    formData: {},
    lastField: {type: 'string'},
    ...commonFields.properties
  }
} as const
export type FbRes = FromSchema<typeof fbResSchema>
export const fbResValidator = getValidator(fbResSchema, dataValidator)
export const fbResResolver = resolve<FbRes, HookContext>({})

export const fbResExternalResolver = resolve<FbRes, HookContext>({})

// Schema for creating new data
export const fbResDataSchema = {
  $id: 'FbResData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...fbResSchema.properties
  }
} as const
export type FbResData = FromSchema<typeof fbResDataSchema>
export const fbResDataValidator = getValidator(fbResDataSchema, dataValidator)
export const fbResDataResolver = resolve<FbResData, HookContext>({})

// Schema for updating existing data
export const fbResPatchSchema = {
  $id: 'FbResPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...fbResSchema.properties,
    ...commonPatch(fbResSchema.properties).properties
  }
} as const
export type FbResPatch = FromSchema<typeof fbResPatchSchema>
export const fbResPatchValidator = getValidator(fbResPatchSchema, dataValidator)
export const fbResPatchResolver = resolve<FbResPatch, HookContext>({})

// Schema for allowed query properties
export const fbResQuerySchema = {
  $id: 'FbResQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(fbResSchema.properties)
  }
} as const
export type FbResQuery = FromSchema<typeof fbResQuerySchema>
export const fbResQueryValidator = getValidator(fbResQuerySchema, queryValidator)
export const fbResQueryResolver = resolve<FbResQuery, HookContext>({})
