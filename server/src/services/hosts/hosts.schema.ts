// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addressSchema,
    addToSet,
    commonFields,
    commonPatch,
    commonQueries,
    imageSchema,
    phoneSchema,
    pull, videoSchema
} from '../../utils/common/schemas.js';

export const guideEnum = ['care_director', 'plan_guide', 'compliance', 'finance', 'physician'];
// Main data model schema
export const hostsSchema = {
    $id: 'Hosts',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'org', 'dba'],
    properties: {
        _id: ObjectIdSchema(),
        appDefault: {type: 'boolean'},
        dba: {type: 'string'},
        description: {type: 'string'},
        org: ObjectIdSchema(),
        avatar: imageSchema,
        subdomain: {type: 'string'},
        allVideos: {type: 'array', items: ObjectIdSchema()}, //uploadIds
        phones: {type: 'array', items: phoneSchema},
        emails: {type: 'array', items: {type: 'string'}},
        locations: {type: 'array', items: addressSchema},
        refs: {type: 'array', items: ObjectIdSchema()},
        teams: {type: 'array', items: ObjectIdSchema()},
        npn: {type: 'string'},
        shopStatuses: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        label: {type: 'string'},
                        color: {type: 'string'}
                    }
                }
            }
        },
        publicSupport: ObjectIdSchema(), //team
        plans: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        team: ObjectIdSchema(),
                        payContract: ObjectIdSchema()
                    }
                }
            }
        },
        videos: {
            type: 'object',
            properties: {
                intro: {
                    type: 'object', patternProperties: {
                        "^.*$": videoSchema,
                    }
                },
            }
        },
        states: {
            type: 'object', patternProperties: {
                '[A-Z]{2}': {
                    type: 'object',
                    properties: {
                        state: {type: 'string'}, //AK, UT
                        counties: {
                            type: 'object', patternProperties: {
                                "^.*$": {
                                    type: 'object', properties: {
                                        all: {type: 'boolean'},
                                        cities: {type: 'array', items: {type: 'string'}}
                                    }
                                }
                            }
                        },
                        all: {type: 'boolean'}
                    }
                }
            }
        },
        roles: {
            type: 'array',
            items: {type: 'string', enum: guideEnum}
        },
        broker: {
            type: 'object', properties: {
                active: {type: 'boolean'},
                ichra: {type: 'boolean'}
            }
        },
        ...commonFields.properties
    }
} as const
export type Hosts = FromSchema<typeof hostsSchema>
export const hostsValidator = getValidator(hostsSchema, dataValidator)
export const hostsResolver = resolve<Hosts, HookContext>({
    subdomain: async (val) => {
        if (val) {
            if (['admin', 'console', 'host', 'app'].includes(val)) throw new Error('Forbidden subdomain')
            return val.trim().toLowerCase();
        }
        return val;
    }
})

export const hostsExternalResolver = resolve<Hosts, HookContext>({})

// Schema for creating new data
export const hostsDataSchema = {
    $id: 'HostsData',
    type: 'object',
    additionalProperties: false,
    required: ['dba', 'org'],
    properties: {
        ...hostsSchema.properties
    }
} as const
export type HostsData = FromSchema<typeof hostsDataSchema>
export const hostsDataValidator = getValidator(hostsDataSchema, dataValidator)
export const hostsDataResolver = resolve<HostsData, HookContext>({})

const pushPull = [
    {path: 'allVideos', type: ObjectIdSchema()},
    {path: 'phones', type: phoneSchema},
    {path: 'locations', type: addressSchema},
    {path: 'emails', type: {type: 'string'}}
]
// Schema for updating existing data
export const hostsPatchSchema = {
    $id: 'HostsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...hostsSchema.properties,
        ...commonPatch(hostsSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type HostsPatch = FromSchema<typeof hostsPatchSchema>
export const hostsPatchValidator = getValidator(hostsPatchSchema, dataValidator)
export const hostsPatchResolver = resolve<HostsPatch, HookContext>({})

// Schema for allowed query properties
export const hostsQuerySchema = {
    $id: 'HostsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({
            ...commonQueries.properties,
            ...hostsSchema.properties,
            dba: {},
            subdomain: {}
        }),

    }
} as const
export type HostsQuery = FromSchema<typeof hostsQuerySchema>
export const hostsQueryValidator = getValidator(hostsQuerySchema, queryValidator)
export const hostsQueryResolver = resolve<HostsQuery, HookContext>({})
