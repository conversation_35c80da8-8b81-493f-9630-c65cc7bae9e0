// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { sectionsSchema } from '../plan-docs/plan-docs.schema.js';
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

const subObj = {
  'core': ['general'],
  '125': ['POP', 'HSA', 'FSA', 'DCAP', 'CASH'],
  '105': ['QSEHRA', 'ICHRA', 'GCHRA', 'EBHRA'],
  'misc': []
}
import { planClass } from '../plan-docs/plan-docs.schema.js';
// Main data model schema
export const docTemplatesSchema = {
  $id: 'DocTemplates',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'name', 'sections'],
  properties: {
    _id: ObjectIdSchema(),
    name: { type: 'string' },
    description: { type: 'string' },
    class: planClass,
    subClass: { type: 'string'},
    sections: sectionsSchema,
    smb: { type: 'boolean' },
    ...commonFields.properties
  }
} as const
export type DocTemplates = FromSchema<typeof docTemplatesSchema>
export const docTemplatesValidator = getValidator(docTemplatesSchema, dataValidator)
export const docTemplatesResolver = resolve<DocTemplates, HookContext>({})

export const docTemplatesExternalResolver = resolve<DocTemplates, HookContext>({})

// Schema for creating new data
export const docTemplatesDataSchema = {
  $id: 'DocTemplatesData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...docTemplatesSchema.properties
  }
} as const
export type DocTemplatesData = FromSchema<typeof docTemplatesDataSchema>
export const docTemplatesDataValidator = getValidator(docTemplatesDataSchema, dataValidator)
export const docTemplatesDataResolver = resolve<DocTemplatesData, HookContext>({})

// Schema for updating existing data
export const docTemplatesPatchSchema = {
  $id: 'DocTemplatesPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...docTemplatesSchema.properties,
    ...commonPatch(docTemplatesSchema.properties).properties
  }
} as const
export type DocTemplatesPatch = FromSchema<typeof docTemplatesPatchSchema>
export const docTemplatesPatchValidator = getValidator(docTemplatesPatchSchema, dataValidator)
export const docTemplatesPatchResolver = resolve<DocTemplatesPatch, HookContext>({})

// Schema for allowed query properties
export const docTemplatesQuerySchema = {
  $id: 'DocTemplatesQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(docTemplatesSchema.properties)
  }
} as const
export type DocTemplatesQuery = FromSchema<typeof docTemplatesQuerySchema>
export const docTemplatesQueryValidator = getValidator(docTemplatesQuerySchema, queryValidator)
export const docTemplatesQueryResolver = resolve<DocTemplatesQuery, HookContext>({})
