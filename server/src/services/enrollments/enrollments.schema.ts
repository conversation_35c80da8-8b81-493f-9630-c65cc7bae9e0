// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addressSchema, commonFields, commonPatch, imageSchema} from '../../utils/common/schemas.js';
import {relationships} from '../households/households.schema.js';
import {paids} from '../claims/utils/index.js'
import {coverageTypeEnum} from '../coverages/coverages.schema.js';

const cafeEnrollSchema = {
    type: 'object',
    properties: {
        optOut: {},
        amount: {type: 'number'},
        updateHistory: {
            type: 'array',
            items: {
                type: 'object',
                properties: {amount: {type: 'number'}, optOut: {}, date: {}}
            }
        },
        acknowledgements: {
            type: 'array',
            items: {
                type: 'object', properties: {
                    text: {type: 'string'},
                    priority: {type: 'number'},//0: Do not allow 1: warn 2: inform
                    key: {type: 'string'},
                    date: {},
                    ip: {type: 'string'},
                    user_agent: {type: 'string'}
                }
            }
        }
    }
} as const

const contribution = {
    type: 'object',
    properties: {
        preTax: {type: 'number'},
        postTax: {type: 'number'},
        total: {type: 'number'},
        def: {type: 'number'}
    }
} as const

export const contributions = {
    type: 'object',
    properties: {
        lastAutoSet: {},
        lastManualSet: {},
        employer: {
            type: 'object', properties: {
                cafe: {type: 'number'},
                coverages: {type: 'number'}
            }
        },
        employee: contribution,
        needed: contribution,
        byPlan: {
            type: 'object',
            patternProperties: {
                "^.*$": {type: 'number'}
            }
        },
        byCoverage: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        employer: {type: 'number'},
                        employee: {type: 'number'}
                    }
                }
            }
        }
    }
} as const
// Main data model schema
export const enrollmentsSchema = {
    $id: 'Enrollments',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'org', 'group', 'person', 'plan', 'version', 'close', 'idempotency_key'],
    properties: {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        person: ObjectIdSchema(),
        group: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        idempotency_key: {type: 'string'},
        name: {type: 'string'},
        description: {type: 'string'},
        version: {type: 'string'},
        planYear: {type: 'string'},
        address: {type: 'object', required: ['postal', 'region'], properties: {...addressSchema.properties}},
        county: {
            type: 'object',
            properties: {
                fips: {type: 'string'},
                name: {type: 'string'},
                stateCode: {type: 'string'}
            }
        },
        spec: ObjectIdSchema(),
        status: {type: 'string', enum: ['not_started', 'open', 'review', 'complete', 'closed']},
        statusNote: { type: 'string' },
        optOut: {},
        open: {type: 'string'},
        close: {},
        terminated: {type: 'boolean'},
        terminatedAt: {},
        terminatedBy: ObjectIdSchema(),
        enrolledAt: {},
        ichra: {type: 'boolean'},
        shop: ObjectIdSchema(),
        type: {type: 'string', enum: ['single', 'family']},
        householdIncome: {type: 'number'},
        enrolled: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        age: {type: 'number'},
                        dob: {type: 'string'},
                        ssn: {type: 'string'},
                        firstName: {type: 'string'},
                        lastName: {type: 'string'},
                        gender: {type: 'string'},
                        relation: {type: 'string', enum: relationships},
                        zip: {type: 'string'},
                        point: {type: 'array', items: {type: 'number'}},
                        monthsSinceSmoked: {type: 'number'},
                        dependent: {type: 'boolean'},
                        disabled: {type: 'boolean'},
                        annualIncome: {type: 'number'},
                        incarcerated: {type: 'boolean'}
                    }
                }
            }
        },
        cafe: {
            type: 'object',
            properties: {
                hsa: cafeEnrollSchema,
                fsa: cafeEnrollSchema,
                dcp: cafeEnrollSchema,
                pop: cafeEnrollSchema,
                def: cafeEnrollSchema,
                cash: cafeEnrollSchema
            }
        },

        lastClaimCoverage: ObjectIdSchema(),
        claimPayments: {type: 'array', items: ObjectIdSchema()},
        patientClaims: {
            type: 'object',
            patternProperties: {
                //participant_id
                "^.*$": {
                    type: 'object',
                    patternProperties: {
                        //coverage_id
                        "^.*$": {
                            type: 'object',
                            properties: {
                                ...paids.properties
                            }
                        }
                    }
                }
            }
        },
        coverageClaims: {
            type: 'object',
            patternProperties: {
                //participant_id
                "^.*$": {
                    type: 'object',
                    properties: {
                        ...paids.properties
                    }
                }
            }
        },
        coverages: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        ichra: {type: 'boolean'},
                        shop: {type: 'boolean'},
                        recurs: {type: 'number'},
                        card: ObjectIdSchema(),
                        confirmedBy: ObjectIdSchema(),
                        confirmedAt: {},
                        confirmData: {
                            type: 'object', properties: {
                                aptc: {type: 'number'},
                                policy_id: {type: 'string'},
                                premium: {type: 'number'},
                                income: { type: 'number' }
                            }
                        },
                        files: {
                            type: 'object', patternProperties: {
                                "^.*$": imageSchema
                            }
                        },
                        participants: {type: 'array', items: ObjectIdSchema()},
                        premium: {type: 'number'},
                        participants_last: {type: 'number'},
                        coverageType: { type: 'string', enum:coverageTypeEnum },
                        postTax: {type: 'boolean'},
                        /**ICHRA DATA*/
                        policy: {type: 'string'},
                        fullPolicy: {},
                        ptc: {type: 'number'},
                        individual_coverage: ObjectIdSchema(),
                        fullCoverage: {},
                        optOut: {},
                        optOutDisclosure: {type: 'string'},
                        providers: {type: 'array', items: ObjectIdSchema()},
                        practitioners: {type: 'array', items: ObjectIdSchema()},
                        status: {type: 'number', enum: [1, 2, 3, 4]}, //1:started, 2:ready, 3:complete, 4:problem
                        type: {type: 'string', enum: ['individual', 'family']},
                        slcsp: {
                            type: 'object', properties: {
                                id: {type: 'string'},
                                name: {type: 'string'},
                                premium: {type: 'number'}
                            }
                        },
                        aptc: {
                            type: 'object', properties: {
                                attest: {type: 'string'},
                                income: {type: 'number'},
                                aptc: {type: 'number'},
                                hardship_exemption: {type: 'boolean'},
                                in_coverage_gap: {type: 'boolean'},
                                is_medicaid_chip: {type: 'boolean'}
                            }
                        },
                        annualSpend: {type: 'number'},
                        rxSpend: {type: 'number'},
                        issuerBlacklist: {type: 'array', items: {type: 'string'}},
                        typeBlacklist: {type: 'array', items: {type: 'string'}},
                        conditions: {type: 'array', items: ObjectIdSchema()},
                        procedures: {type: 'array', items: ObjectIdSchema()},
                        meds: {type: 'array', items: ObjectIdSchema()}
                    }
                }
            }
        },
        contributions,
        ...commonFields.properties
    }
} as const
export type Enrollments = FromSchema<typeof enrollmentsSchema>
export const enrollmentsValidator = getValidator(enrollmentsSchema, dataValidator)
export const enrollmentsResolver = resolve<Enrollments, HookContext>({
    planYear: async (val, data) => {
        return data.version.split('_')[0];
    }
})

export const enrollmentsExternalResolver = resolve<Enrollments, HookContext>({})

// Schema for creating new data
export const enrollmentsDataSchema = {
    $id: 'EnrollmentsData',
    type: 'object',
    additionalProperties: false,
    required: ['org', 'person', 'group', 'plan', 'version', 'close'],
    properties: {
        ...enrollmentsSchema.properties
    }
} as const
export type EnrollmentsData = FromSchema<typeof enrollmentsDataSchema>
export const enrollmentsDataValidator = getValidator(enrollmentsDataSchema, dataValidator)
export const enrollmentsDataResolver = resolve<EnrollmentsData, HookContext>({
    status: async (val) => {
        if (!val) return 'not_started';
        return val;
    },
    close: async (val) => {
        if (!val) return new Date(new Date().getTime() * 1000 * 60 * 60 * 24 * 30);
        return val;
    },
    cafe: async (val: any) => {
        if (val) {
            for (const k in val) {
                if (val[k].optOut) val[k].amount = 0;
            }
        }
        return val;
    }
})

// Schema for updating existing data
export const enrollmentsPatchSchema = {
    $id: 'EnrollmentsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...enrollmentsSchema.properties,
        ...commonPatch(enrollmentsSchema.properties).properties,
        $addToSet: {},
        $pull: {}
    }
} as const
export type EnrollmentsPatch = FromSchema<typeof enrollmentsPatchSchema>
export const enrollmentsPatchValidator = getValidator(enrollmentsPatchSchema, dataValidator)
export const enrollmentsPatchResolver = resolve<EnrollmentsPatch, HookContext>({})

// Schema for allowed query properties
export const enrollmentsQuerySchema = {
    $id: 'EnrollmentsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(enrollmentsSchema.properties)
    }
} as const
export type EnrollmentsQuery = FromSchema<typeof enrollmentsQuerySchema>
export const enrollmentsQueryValidator = getValidator(enrollmentsQuerySchema, queryValidator)
export const enrollmentsQueryResolver = resolve<EnrollmentsQuery, HookContext>({})
