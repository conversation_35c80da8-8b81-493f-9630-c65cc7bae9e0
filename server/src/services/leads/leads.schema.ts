// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const leadsSchema = {
  $id: 'Leads',
  type: 'object',
  additionalProperties: true,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    ...commonFields.properties
  }
} as const
export type Leads = FromSchema<typeof leadsSchema>
export const leadsValidator = getValidator(leadsSchema, dataValidator)
export const leadsResolver = resolve<Leads, HookContext>({})

export const leadsExternalResolver = resolve<Leads, HookContext>({})

// Schema for creating new data
export const leadsDataSchema = {
  $id: 'LeadsData',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...leadsSchema.properties
  }
} as const
export type LeadsData = FromSchema<typeof leadsDataSchema>
export const leadsDataValidator = getValidator(leadsDataSchema, dataValidator)
export const leadsDataResolver = resolve<LeadsData, HookContext>({})

// Schema for updating existing data
export const leadsPatchSchema = {
  $id: 'LeadsPatch',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...leadsSchema.properties,
    ...commonPatch(leadsSchema.properties).properties
  }
} as const
export type LeadsPatch = FromSchema<typeof leadsPatchSchema>
export const leadsPatchValidator = getValidator(leadsPatchSchema, dataValidator)
export const leadsPatchResolver = resolve<LeadsPatch, HookContext>({})

// Schema for allowed query properties
export const leadsQuerySchema = {
  $id: 'LeadsQuery',
  type: 'object',
  additionalProperties: true,
  properties: {
    ...querySyntax(leadsSchema.properties)
  }
} as const
export type LeadsQuery = FromSchema<typeof leadsQuerySchema>
export const leadsQueryValidator = getValidator(leadsQuerySchema, queryValidator)
export const leadsQueryResolver = resolve<LeadsQuery, HookContext>({})
