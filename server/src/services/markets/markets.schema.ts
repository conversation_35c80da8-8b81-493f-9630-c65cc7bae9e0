// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, geoJsonSchema} from '../../utils/common/schemas.js';

// Main data model schema
export const marketsSchema = {
    $id: 'Markets',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        hosts: { type: 'array', items: ObjectIdSchema() },
        owners: { type: 'array', items: ObjectIdSchema() }, //Hosts
        managers: {type: 'array', items: ObjectIdSchema()}, //Hosts
        geo: geoJsonSchema,
        locations: {
            type: 'object', patternProperties: {
                "[A-Z]{2}": {
                    type: 'object',
                    properties: {
                        cities: {type: 'array', items: { type: 'string'}},
                        zips: {type: 'array', items: { type: 'string'}},
                        counties: {type: 'array', items: { type: 'string'}}
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type Markets = FromSchema<typeof marketsSchema>
export const marketsValidator = getValidator(marketsSchema, dataValidator)
export const marketsResolver = resolve<Markets, HookContext>({})

export const marketsExternalResolver = resolve<Markets, HookContext>({})

// Schema for creating new data
export const marketsDataSchema = {
    $id: 'MarketsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...marketsSchema.properties
    }
} as const
export type MarketsData = FromSchema<typeof marketsDataSchema>
export const marketsDataValidator = getValidator(marketsDataSchema, dataValidator)
export const marketsDataResolver = resolve<MarketsData, HookContext>({})

// Schema for updating existing data
export const marketsPatchSchema = {
    $id: 'MarketsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...marketsSchema.properties,
        ...commonPatch(marketsSchema.properties).properties
    }
} as const
export type MarketsPatch = FromSchema<typeof marketsPatchSchema>
export const marketsPatchValidator = getValidator(marketsPatchSchema, dataValidator)
export const marketsPatchResolver = resolve<MarketsPatch, HookContext>({})

// Schema for allowed query properties
export const marketsQuerySchema = {
    $id: 'MarketsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(marketsSchema.properties)
    }
} as const
export type MarketsQuery = FromSchema<typeof marketsQuerySchema>
export const marketsQueryValidator = getValidator(marketsQuerySchema, queryValidator)
export const marketsQueryResolver = resolve<MarketsQuery, HookContext>({})
