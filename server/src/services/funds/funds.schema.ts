// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const fundsSchema = {
    $id: 'Funds',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name', 'state'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        state: {type: 'string'},
        board: {type: 'array', items: ObjectIdSchema()},
        nominees: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        id: ObjectIdSchema(),
                        status: {type: 'number', enum: [0, 1, 2, 3, 4, 5]},//['invited', 'accepted', 'active', 'rejected', 'inactive']
                        statusUpdates: {
                            type: 'array', items: {
                                type: 'object', properties: {
                                    from: { type: 'number' },
                                    to: { type: 'number' },
                                    at: {},
                                    by: ObjectIdSchema()
                                }
                            }
                        }
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type Funds = FromSchema<typeof fundsSchema>
export const fundsValidator = getValidator(fundsSchema, dataValidator)
export const fundsResolver = resolve<Funds, HookContext>({})

export const fundsExternalResolver = resolve<Funds, HookContext>({})

// Schema for creating new data
export const fundsDataSchema = {
    $id: 'FundsData',
    type: 'object',
    additionalProperties: false,
    required: ['name', 'state'],
    properties: {
        ...fundsSchema.properties
    }
} as const
export type FundsData = FromSchema<typeof fundsDataSchema>
export const fundsDataValidator = getValidator(fundsDataSchema, dataValidator)
export const fundsDataResolver = resolve<FundsData, HookContext>({})

// Schema for updating existing data
export const fundsPatchSchema = {
    $id: 'FundsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...fundsSchema.properties,
        ...commonPatch(fundsSchema.properties).properties
    }
} as const
export type FundsPatch = FromSchema<typeof fundsPatchSchema>
export const fundsPatchValidator = getValidator(fundsPatchSchema, dataValidator)
export const fundsPatchResolver = resolve<FundsPatch, HookContext>({})

// Schema for allowed query properties
export const fundsQuerySchema = {
    $id: 'FundsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(fundsSchema.properties)
    }
} as const
export type FundsQuery = FromSchema<typeof fundsQuerySchema>
export const fundsQueryValidator = getValidator(fundsQuerySchema, queryValidator)
export const fundsQueryResolver = resolve<FundsQuery, HookContext>({})
