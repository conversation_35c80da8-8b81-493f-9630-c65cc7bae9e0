// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, imageSchema, phoneSchema} from '../../utils/common/schemas.js';
// Main data model schema

export const eeFactsSchema = {
    type: 'object',
    properties: {
        gender: { type: 'string', enum: ['male', 'female'] },
        wage: { type: 'number' },
        income: {type: 'number'},
        hh_income: {type: 'number'},
        hourly: { type: 'boolean' },
        hours: { type: 'number' },
        married: {type: 'string'},
        deps: {type: 'number'},
        zip: {type: 'string'},
        state: { type: 'string' },
        smoker: { type: 'boolean' },
        spouseAge: { type: 'string' },
        spouseDob: {type: 'string'},
        age: {type: 'number'},
        dob: {type: 'string'}
    }
} as const;

export const eeSchema = {
    type: 'object',
    properties: {
        w9: { type: 'boolean' },
        role: { type: 'string' },
        errorAdding: { type: 'boolean' },
        addError: { type: 'string' },
        camsError: { type: 'boolean' },
        uid: {type: 'string'},
        name: {type: 'string'},
        email: {type: 'string'},
        lastName: {type: 'string'},
        firstName: {type: 'string'},
        person: ObjectIdSchema(),
        ...eeFactsSchema.properties,
        updatedAt: {},
        ptc: {type: 'number'},
        ptcUpdatedAt: {},
        countyfips: { type: 'string' },
        lastFacts: eeFactsSchema,
    }
} as const

export const docRequestsSchema = {
    $id: 'DocRequests',
    type: 'object',
    additionalProperties: true,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        types: {type: 'array', items: {type: 'string'}},
        orgName: {type: 'string'},
        orgAvatar: { type: 'string' },
        org: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        fingerprint: {type: 'string'},
        refName: {type: 'string'},
        doc: {type: 'string'},
        eeCount: {type: 'number'},
        fteCount: {type: 'number'},
        email: {type: 'string'},
        phone: phoneSchema,
        name: {type: 'string'},
        optIn: {type: 'boolean'},
        status: { type: 'string', enum: ['complete', 'partial']},
        states: {type: 'array', items: {type: 'string'}},
        docType: {type: 'string', enum: ['smb']},
        employees: {
            type: 'array', items: eeSchema
        },
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },
        ...commonFields.properties
    }
} as const
export type DocRequests = FromSchema<typeof docRequestsSchema>
export const docRequestsValidator = getValidator(docRequestsSchema, dataValidator)
export const docRequestsResolver = resolve<DocRequests, HookContext>({})

export const docRequestsExternalResolver = resolve<DocRequests, HookContext>({})

// Schema for creating new data
export const docRequestsDataSchema = {
    $id: 'DocRequestsData',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...docRequestsSchema.properties
    }
} as const
export type DocRequestsData = FromSchema<typeof docRequestsDataSchema>
export const docRequestsDataValidator = getValidator(docRequestsDataSchema, dataValidator)
export const docRequestsDataResolver = resolve<DocRequestsData, HookContext>({})

// Schema for updating existing data
export const docRequestsPatchSchema = {
    $id: 'DocRequestsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...docRequestsSchema.properties,
        ...commonPatch(docRequestsSchema.properties).properties
    }
} as const
export type DocRequestsPatch = FromSchema<typeof docRequestsPatchSchema>
export const docRequestsPatchValidator = getValidator(docRequestsPatchSchema, dataValidator)
export const docRequestsPatchResolver = resolve<DocRequestsPatch, HookContext>({})

// Schema for allowed query properties
export const docRequestsQuerySchema = {
    $id: 'DocRequestsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(docRequestsSchema.properties)
    }
} as const
export type DocRequestsQuery = FromSchema<typeof docRequestsQuerySchema>
export const docRequestsQueryValidator = getValidator(docRequestsQuerySchema, queryValidator)
export const docRequestsQueryResolver = resolve<DocRequestsQuery, HookContext>({})
