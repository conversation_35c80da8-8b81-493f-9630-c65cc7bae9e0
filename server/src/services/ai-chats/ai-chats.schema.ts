// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {addToSet, commonFields, commonPatch} from '../../utils/common/schemas.js';

const chatHistorySchema = {
  type: 'array',
  items: {
    type: 'object', properties: {
      session: {type: 'string'},
      createdAt: {},
      subject: {type: 'string', enum: ['plan_docs', 'contracts', 'coverages', 'medical', 'shops']},
      question: {type: 'string'},
      annotations: {type: 'array', items: {}},
      answer: {type: 'string'},
    }
  }
} as const
// Main data model schema
export const aiChatsSchema = {
  $id: 'AiChats',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'subject', 'chatName', 'person'],
  properties: {
    _id: ObjectIdSchema(),
    subject: ObjectIdSchema(),
    chatName: {type: 'string'},
    chatId: { type: 'string' },
    person: ObjectIdSchema(),
    chats: {type: 'array', items: chatHistorySchema},
    ...commonFields.properties
  }
} as const
export type AiChats = FromSchema<typeof aiChatsSchema>
export const aiChatsValidator = getValidator(aiChatsSchema, dataValidator)
export const aiChatsResolver = resolve<AiChats, HookContext>({})

export const aiChatsExternalResolver = resolve<AiChats, HookContext>({})

// Schema for creating new data
export const aiChatsDataSchema = {
  $id: 'AiChatsData',
  type: 'object',
  additionalProperties: false,
  required: ['subject', 'chatName', 'person'],
  properties: {
    ...aiChatsSchema.properties
  }
} as const
export type AiChatsData = FromSchema<typeof aiChatsDataSchema>
export const aiChatsDataValidator = getValidator(aiChatsDataSchema, dataValidator)
export const aiChatsDataResolver = resolve<AiChatsData, HookContext>({})

const pushPull = [
  {path: 'ai_chats', type: chatHistorySchema}
]
// Schema for updating existing data
export const aiChatsPatchSchema = {
  $id: 'AiChatsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...commonPatch(aiChatsSchema.properties).properties,
    ...aiChatsSchema.properties,
    $push: addToSet(pushPull)
  }
} as const
export type AiChatsPatch = FromSchema<typeof aiChatsPatchSchema>
export const aiChatsPatchValidator = getValidator(aiChatsPatchSchema, dataValidator)
export const aiChatsPatchResolver = resolve<AiChatsPatch, HookContext>({})

// Schema for allowed query properties
export const aiChatsQuerySchema = {
  $id: 'AiChatsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(aiChatsSchema.properties)
  }
} as const
export type AiChatsQuery = FromSchema<typeof aiChatsQuerySchema>
export const aiChatsQueryValidator = getValidator(aiChatsQuerySchema, queryValidator)
export const aiChatsQueryResolver = resolve<AiChatsQuery, HookContext>({})
