// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const cobrasSchema = {
  $id: 'Cobras',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'enrollment', 'participant', 'household', 'deadline'],
  properties: {
    _id: ObjectIdSchema(),
    enrollment: ObjectIdSchema(),
    participant: ObjectIdSchema(),
    household: ObjectIdSchema(),
    spec: ObjectIdSchema(),
    event_type: { type: 'string', enum: ['job', 'death', 'divorce', 'medicare', 'dependent']},
    deadline: { type: 'string' },
    end_date: { type: 'string' },
    optOut: { type: 'boolean' },
    coverages: {
      type: 'object',
      patternProperties: {
        "^.*$": {
          type: 'object',
          properties: {
            recurs: {type: 'number'},
            participants: {type: 'array', items: ObjectIdSchema()}
          }
        }
      }
    },
    ...commonFields.properties
  }
} as const
export type Cobras = FromSchema<typeof cobrasSchema>
export const cobrasValidator = getValidator(cobrasSchema, dataValidator)
export const cobrasResolver = resolve<Cobras, HookContext>({})

export const cobrasExternalResolver = resolve<Cobras, HookContext>({})

// Schema for creating new data
export const cobrasDataSchema = {
  $id: 'CobrasData',
  type: 'object',
  additionalProperties: false,
  required: ['enrollment', 'participant', 'household', 'deadline'],
  properties: {
    ...cobrasSchema.properties
  }
} as const
export type CobrasData = FromSchema<typeof cobrasDataSchema>
export const cobrasDataValidator = getValidator(cobrasDataSchema, dataValidator)
export const cobrasDataResolver = resolve<CobrasData, HookContext>({})

// Schema for updating existing data
export const cobrasPatchSchema = {
  $id: 'CobrasPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...cobrasSchema.properties,
    ...commonPatch(cobrasSchema.properties).properties
  }
} as const
export type CobrasPatch = FromSchema<typeof cobrasPatchSchema>
export const cobrasPatchValidator = getValidator(cobrasPatchSchema, dataValidator)
export const cobrasPatchResolver = resolve<CobrasPatch, HookContext>({})

// Schema for allowed query properties
export const cobrasQuerySchema = {
  $id: 'CobrasQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(cobrasSchema.properties)
  }
} as const
export type CobrasQuery = FromSchema<typeof cobrasQuerySchema>
export const cobrasQueryValidator = getValidator(cobrasQuerySchema, queryValidator)
export const cobrasQueryResolver = resolve<CobrasQuery, HookContext>({})
