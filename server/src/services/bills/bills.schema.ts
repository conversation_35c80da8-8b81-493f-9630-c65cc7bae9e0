// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addressSchema,
    commonFields, commonPatch,
    imageSchema,
    phoneSchema,
    rRuleSchema,
    taxSchema
} from '../../utils/common/schemas.js';

const lineItem = {
    type: 'object',
    properties: {
        title: {type: 'string'},
        description: {type: 'string'},
        amount: {type: 'number'},
        code: { type: 'string' },
        itemId: ObjectIdSchema(),
        itemService: {type: 'string'},
        qty: {type: 'number'},
        settings: {
            type: 'object',
            properties: {
                tax: taxSchema
            }
        }
    }
} as const
// Main data model schema
export const billsSchema = {
    $id: 'Bills',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        to: ObjectIdSchema(),
        toName: { type: 'string' },
        toModel: {type: 'string', enum: ['orgs', 'ppls']},
        toEmail: {type: 'string'},
        toAddress: addressSchema,
        toPhone: phoneSchema,
        from: ObjectIdSchema(),
        fromName: { type: 'string' },
        formModel: {type: 'string', enum: ['orgs', 'ppls']},
        fromEmail: {type: 'string'},
        fromAddress: addressSchema,
        fromPhone: phoneSchema,
        logo: imageSchema,
        billDate: {},
        dueDate: {},
        currency: {type: 'string', enum: ['usd']},
        lastTaxTotal: {type: 'number'},
        lineItems: {type: 'array', items: lineItem },
        threads: {type: 'array', items: ObjectIdSchema()},
        settings: {
            type: 'object',
            properties: {
                tax: taxSchema
            }
        },
        payments: { type: 'array', items: ObjectIdSchema() },
        recurrence: rRuleSchema,
        paymentLink: { type: 'string' },
        status: { type: 'string', enum: ['open', 'paid', 'closed']},
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },
        ...commonFields.properties
    }
} as const
export type Bills = FromSchema<typeof billsSchema>
export const billsValidator = getValidator(billsSchema, dataValidator)
export const billsResolver = resolve<Bills, HookContext>({})

export const billsExternalResolver = resolve<Bills, HookContext>({})

// Schema for creating new data
export const billsDataSchema = {
    $id: 'BillsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...billsSchema.properties
    }
} as const
export type BillsData = FromSchema<typeof billsDataSchema>
export const billsDataValidator = getValidator(billsDataSchema, dataValidator)
export const billsDataResolver = resolve<BillsData, HookContext>({})

// Schema for updating existing data
export const billsPatchSchema = {
    $id: 'BillsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...billsSchema.properties,
        ...commonPatch(billsSchema.properties).properties
    }
} as const
export type BillsPatch = FromSchema<typeof billsPatchSchema>
export const billsPatchValidator = getValidator(billsPatchSchema, dataValidator)
export const billsPatchResolver = resolve<BillsPatch, HookContext>({})

// Schema for allowed query properties
export const billsQuerySchema = {
    $id: 'BillsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(billsSchema.properties)
    }
} as const
export type BillsQuery = FromSchema<typeof billsQuerySchema>
export const billsQueryValidator = getValidator(billsQuerySchema, queryValidator)
export const billsQueryResolver = resolve<BillsQuery, HookContext>({})
