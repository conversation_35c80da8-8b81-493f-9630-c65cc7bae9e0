// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js';

const anyOfList = [{type: 'string'}, {type: 'number'}, {type: 'boolean'}] as const;
const recursiveAnyOf = ({anyOf: [...anyOfList, {type: 'array', items: {anyOf: anyOfList}}]}) as const
// Main data model schema
export const specsSchema = {
    $id: 'Specs',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'org', 'plan', 'planYear'],
    properties: {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        planYear: { type: 'string' },
        enrollment: ObjectIdSchema(),
        approvedBy: ObjectIdSchema(),
        approvedAt: {},
        event: {type: 'string', enum: ['household', 'employment', 'plan', 'hsa']},
        description: {type: 'string'},
        status: {type: 'string', enum: ['approved', 'rejected', 'pending', 'archived']},
        message: {type: 'string'},
        thread: ObjectIdSchema(),
        changedAt: {},
        changes: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        newVal: recursiveAnyOf,
                        oldVal: recursiveAnyOf
                    }
                }
            }
        },
        specialEnrollment: {type: 'boolean'},
        ...commonFields.properties
    }
} as const
export type Specs = FromSchema<typeof specsSchema>
export const specsValidator = getValidator(specsSchema, dataValidator)
export const specsResolver = resolve<Specs, HookContext>({})

export const specsExternalResolver = resolve<Specs, HookContext>({})

// Schema for creating new data
export const specsDataSchema = {
    $id: 'SpecsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...specsSchema.properties
    }
} as const
export type SpecsData = FromSchema<typeof specsDataSchema>
export const specsDataValidator = getValidator(specsDataSchema, dataValidator)
export const specsDataResolver = resolve<SpecsData, HookContext>({
    status: async (val) => {
        return val || 'pending';
    }
})

// Schema for updating existing data
export const specsPatchSchema = {
    $id: 'SpecsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...specsSchema.properties,
        ...commonPatch(specsSchema.properties).properties
    }
} as const
export type SpecsPatch = FromSchema<typeof specsPatchSchema>
export const specsPatchValidator = getValidator(specsPatchSchema, dataValidator)
export const specsPatchResolver = resolve<SpecsPatch, HookContext>({})

// Schema for allowed query properties
export const specsQuerySchema = {
    $id: 'SpecsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(specsSchema.properties),
        ...commonQueries.properties
    }
} as const
export type SpecsQuery = FromSchema<typeof specsQuerySchema>
export const specsQueryValidator = getValidator(specsQuerySchema, queryValidator)
export const specsQueryResolver = resolve<SpecsQuery, HookContext>({})
