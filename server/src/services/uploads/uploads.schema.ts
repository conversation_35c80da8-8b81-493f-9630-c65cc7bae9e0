// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, addToSet, pull, commonQueries, commonPatch} from '../../utils/common/schemas.js';

export const usageSchema = {
    type: 'object', properties: {
        subject: ObjectIdSchema(),
        subjectModel: {type: 'string'},
        subjectPath: {type: 'string'},
        subjectArray: {type: 'boolean'}
    }
} as const
// Main data model schema
export const uploadsSchema = {
    $id: 'Uploads',
    type: 'object',
    additionalProperties: true,
    required: ['_id', 'url', 'fileId', 'storage'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        url: {type: 'string'},
        cid: {type: 'string'},
        originalName: {type: 'string'},
        originalname: {type: 'string'},
        expires: {type: 'number'}, //seconds for links to last until expiring
        usageVerified: {type: 'string'},
        usage: {
            type: 'array', items: usageSchema
        },
        info: {
            type: 'object', properties: {
                name: {type: 'string'},
                size: {type: 'number'},
                type: {type: 'string'},
                lastModifiedDate: {}
            }
        },
        session: { type: 'string' },
        temp: { type: 'boolean' },
        video: { type: 'boolean' },
        fileId: {type: 'string'},
        status: { type: 'string' },
        bucket: { anyOf: [{type: 'string'}, { type: 'number'}]},
        storage: {type: 'string'},
        uploadChannel: {type: 'string'},
        tags: { type: 'array', items: {type: 'string'}},
        ...commonFields.properties
    }
} as const
export type Uploads = FromSchema<typeof uploadsSchema>
export const uploadsValidator = getValidator(uploadsSchema, dataValidator)
export const uploadsResolver = resolve<Uploads, HookContext>({})
export const uploadsExternalResolver = resolve<Uploads, HookContext>({})

// Schema for creating new data
export const uploadsDataSchema = {
    $id: 'UploadsData',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...uploadsSchema.properties
    }
} as const
export type UploadsData = FromSchema<typeof uploadsDataSchema>
export const uploadsDataValidator = getValidator(uploadsDataSchema, dataValidator)
export const uploadsDataResolver = resolve<UploadsData, HookContext>({})

// Schema for updating existing data
export const uploadsPatchSchema = {
    $id: 'UploadsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...uploadsSchema.properties,
        $addToSet: addToSet([{path: 'usage', type: usageSchema }, { path: 'tags', type: { type: 'string'}}]),
        $pull: pull([{ path: 'usage', type: usageSchema }, { path: 'tags', type: {type: 'string'}}]),
        ...commonPatch(uploadsSchema.properties).properties
    }
} as const
export type UploadsPatch = FromSchema<typeof uploadsPatchSchema>
export const uploadsPatchValidator = getValidator(uploadsPatchSchema, dataValidator)
export const uploadsPatchResolver = resolve<UploadsPatch, HookContext>({})

// Schema for allowed query properties
export const uploadsQuerySchema = {
    $id: 'UploadsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({
            ...uploadsSchema.properties,
            ...commonQueries.properties
        }),
        'createdBy.login': ObjectIdSchema(),
        'info.type': {},
        'info.name': {},
        info: {}
    }
} as const
export type UploadsQuery = FromSchema<typeof uploadsQuerySchema>
export const uploadsQueryValidator = getValidator(uploadsQuerySchema, queryValidator)
export const uploadsQueryResolver = resolve<UploadsQuery, HookContext>({})
