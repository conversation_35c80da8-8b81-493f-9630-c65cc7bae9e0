// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonPatch, commonFields, imageSchema, commonQueries, videoSchema} from '../../utils/common/schemas.js';
import {vectorStore} from '../shops/shops.schema.js';
// Main data model schema
export const healthSharesSchema = {
    $id: 'HealthShares',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        description: {type: 'string'},
        aka: {type: 'array', items: {type: 'string'}},
        logo: imageSchema,
        cc_video: videoSchema,
        video: videoSchema,
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },
        products: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        name: {type: 'string'},
                        guidelines: imageSchema,
                        vectorStore
                    }
                }
            }
        },
        financials: {
            type: 'object', patternProperties: {
                "^\d{4}$": {
                    $comment: 'Key is the year',
                    type: 'object',
                    properties: {
                        total_revenue: {type: 'number'},
                        sharing_expense: {type: 'number'},
                        admin_expense: {type: 'number'},
                        net_assets_start: {type: 'number'},
                        net_assets: {type: 'number'},
                        cash_on_hand: {type: 'number'},
                        highest_paid_executive: {type: 'number'}
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type HealthShares = FromSchema<typeof healthSharesSchema>
export const healthSharesValidator = getValidator(healthSharesSchema, dataValidator)
export const healthSharesResolver = resolve<HealthShares, HookContext>({})

export const healthSharesExternalResolver = resolve<HealthShares, HookContext>({})

// Schema for creating new data
export const healthSharesDataSchema = {
    $id: 'HealthSharesData',
    type: 'object',
    additionalProperties: false,
    required: ['name'],
    properties: {
        ...healthSharesSchema.properties
    }
} as const
export type HealthSharesData = FromSchema<typeof healthSharesDataSchema>
export const healthSharesDataValidator = getValidator(healthSharesDataSchema, dataValidator)
export const healthSharesDataResolver = resolve<HealthSharesData, HookContext>({})

// Schema for updating existing data
export const healthSharesPatchSchema = {
    $id: 'HealthSharesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...healthSharesSchema.properties,
        ...commonPatch(healthSharesSchema.properties).properties
    }
} as const
export type HealthSharesPatch = FromSchema<typeof healthSharesPatchSchema>
export const healthSharesPatchValidator = getValidator(healthSharesPatchSchema, dataValidator)
export const healthSharesPatchResolver = resolve<HealthSharesPatch, HookContext>({})

// Schema for allowed query properties
export const healthSharesQuerySchema = {
    $id: 'HealthSharesQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({
            ...healthSharesSchema.properties,
            name: {},
            aka: {}
        }),
        ...commonQueries.properties,
    }
} as const
export type HealthSharesQuery = FromSchema<typeof healthSharesQuerySchema>
export const healthSharesQueryValidator = getValidator(healthSharesQuerySchema, queryValidator)
export const healthSharesQueryResolver = resolve<HealthSharesQuery, HookContext>({})
