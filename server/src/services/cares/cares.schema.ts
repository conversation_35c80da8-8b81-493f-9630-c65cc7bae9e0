// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, imageSchema, commonPatch, commonQueries, pull} from '../../utils/common/schemas.js';

export const exes = {
    type: 'object',
    properties: {
        id: ObjectIdSchema(),
        name: {type: 'string'},
        medical_name: {type: 'string'},
        code: {type: 'string'},
        standard: {type: 'string'},
        notes: {type: 'string'},
        loggedAt: {},
        loggedBy: ObjectIdSchema()
    }
} as const;
import { paids } from '../claims/utils/index.js'

// Main data model schema
export const caresSchema = {
    $id: 'Cares',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'person', 'plan', 'patient'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        org: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        status: {type: 'number', enum: [0, 1, 2, 3, 4, 5]},
        planPriority: {type: 'number', enum: [0, 1, 2, 3, 4, 5]},
        patientPriority: {type: 'number', enum: [0, 1, 2, 3, 4, 5]},
        providerPriority: {type: 'number', enum: [0, 1, 2, 3, 4, 5]},
        initDate: {},
        name: { type: 'string' },
        targetDate: {},
        lastVisit: {},
        visits: { type: 'array', items: ObjectIdSchema() },
        providers: {type: 'array', items: ObjectIdSchema()},
        practitioners: {type: 'array', items: ObjectIdSchema()},
        parent: ObjectIdSchema(),
        children: {type: 'array', items: ObjectIdSchema()},
        patient: ObjectIdSchema(),
        related: {type: 'array', items: ObjectIdSchema()},
        conditions: { type: 'array', items: exes },
        preventive: { type: 'boolean' },
        threads: {type: 'array', items: ObjectIdSchema()},
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },
        total: {type: 'number'},
        subtotal: {type: 'number'},
        ...paids.properties,
        balance: {type: 'number'},
        balanceSyncedAt: {},
        ...commonFields.properties
    }
} as const
export type Cares = FromSchema<typeof caresSchema>
export const caresValidator = getValidator(caresSchema, dataValidator)
export const caresResolver = resolve<Cares, HookContext>({
    status: async (val) => {
        if (!val && val !== 0) return 0;
        return val;
    },
    patientPriority: async (val) => {
        if (!val && val !== 0) return 0;
        return val;
    }
})

export const caresExternalResolver = resolve<Cares, HookContext>({})

// Schema for creating new data
export const caresDataSchema = {
    $id: 'CaresData',
    type: 'object',
    additionalProperties: false,
    required: ['person', 'plan', 'patient'],
    properties: {
        ...caresSchema.properties
    }
} as const
export type CaresData = FromSchema<typeof caresDataSchema>
export const caresDataValidator = getValidator(caresDataSchema, dataValidator)
export const caresDataResolver = resolve<CaresData, HookContext>({})

const pushPull = [{path: 'children', type: ObjectIdSchema()}, { path: 'conditions', type: exes }]
// Schema for updating existing data
export const caresPatchSchema = {
    $id: 'CaresPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...caresSchema.properties,
        ...commonPatch(caresSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type CaresPatch = FromSchema<typeof caresPatchSchema>
export const caresPatchValidator = getValidator(caresPatchSchema, dataValidator)
export const caresPatchResolver = resolve<CaresPatch, HookContext>({})

// Schema for allowed query properties
export const caresQuerySchema = {
    $id: 'CaresQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(caresSchema.properties),
        ...commonQueries.properties,
        $addToSet: addToSet([{path: 'visits', type: ObjectIdSchema()},{path: 'providers', type: ObjectIdSchema()}, { path: 'providers', type: ObjectIdSchema() }, { path: 'practitioners', type: ObjectIdSchema() }, { path: 'related', type: ObjectIdSchema() }, { path: 'children', type: ObjectIdSchema() }]),
        $pull: pull([{path: 'visits', type: ObjectIdSchema()},{path: 'providers', type: ObjectIdSchema()}, { path: 'providers', type: ObjectIdSchema() }, { path: 'practitioners', type: ObjectIdSchema() }, { path: 'related', type: ObjectIdSchema() }, { path: 'children', type: ObjectIdSchema() }]),
        visits: {},
        initDate: {},
        targetDate: {},
        name: {}
    }
} as const
export type CaresQuery = FromSchema<typeof caresQuerySchema>
export const caresQueryValidator = getValidator(caresQuerySchema, queryValidator)
export const caresQueryResolver = resolve<CaresQuery, HookContext>({})
