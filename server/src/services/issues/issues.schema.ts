// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js'

const assignedSchema = {
    type: 'object',
    properties: {id: ObjectIdSchema(), at: {}, notes: {type: 'string'}}
} as const
// Main data model schema
export const issuesSchema = {
    $id: 'Issues',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'by'],
    properties: {
        _id: ObjectIdSchema(),
        service: {type: 'string'},
        record: ObjectIdSchema(),
        type: {type: 'string', enum: ['content', 'complaint', 'dispute']},
        category: {type: 'string'},
        message: {type: 'string'},
        by: ObjectIdSchema(),
        org: ObjectIdSchema(),
        threads: {type: 'array', items: ObjectIdSchema()},
        status: {type: 'string', enum: ['unopened', 'assigned', 'resolved', 'escalated', 'dropped']},
        transaction: { type: 'string' },
        resolvedAt: {},
        channel: {type: 'string', enum: ['app', 'email', 'phone', 'mail']},
        assigned: ObjectIdSchema(),
        assignedAt: {},
        assignedHistory: {type: 'array', items: assignedSchema},
        treasuryComplaint: {type: 'boolean'},
        treasuryData: {
            type: 'object', properties: {
                date_input: {type: 'string'},
                date_received: {type: 'string'},
                user_name: {type: 'string'},
                received_by: {type: 'string'},
                account_ID: {type: 'string'},
                complaint_classification: {type: 'string', enum: ['operational', 'executive']},
                complaint_category: {
                    type: 'string',
                    enum: ['Privacy or Security', 'Legal or Regulatory', 'Product or Service']
                },
                complaint_sub_category: {type: 'string'},
                complaint_description: {type: 'string'},
                alleges_UDAP_or_discrimination: {type: 'boolean'},
                user_stage: {type: 'string', enum: ['prospect', 'onboarding', 'active']},
                systemic_issue_identified: {type: 'boolean'},
                //not always required
                date_resolved: {type: 'string'},
                redress_reqd: {type: 'boolean'},
                description_of_corrective_action: {type: 'string'},
                internal_links: {type: 'array', items: {type: 'string'}},
                user_correspondence_links: {type: 'array', items: {type: 'string'}},
                reason_exceeded_15_days: {type: 'string'}
            }
        },
        ...commonFields.properties
    }
} as const
export type Issues = FromSchema<typeof issuesSchema>
export const issuesValidator = getValidator(issuesSchema, dataValidator)
export const issuesResolver = resolve<Issues, HookContext>({
    status: async (val) => {
        if(!val) return 'unopened'
        return val;
    },
    channel: async (val) => {
        if(!val) return 'app'
        return val;
    }
})

export const issuesExternalResolver = resolve<Issues, HookContext>({})

// Schema for creating new data
export const issuesDataSchema = {
    $id: 'IssuesData',
    type: 'object',
    additionalProperties: false,
    required: ['by'],
    properties: {
        ...issuesSchema.properties
    }
} as const
export type IssuesData = FromSchema<typeof issuesDataSchema>
export const issuesDataValidator = getValidator(issuesDataSchema, dataValidator)
export const issuesDataResolver = resolve<IssuesData, HookContext>({})

// Schema for updating existing data
export const issuesPatchSchema = {
    $id: 'IssuesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...issuesSchema.properties,
        ...commonPatch(issuesSchema.properties).properties,
        $addToSet: addToSet([{path: 'assignedHistory', type: assignedSchema}])
    }
} as const
export type IssuesPatch = FromSchema<typeof issuesPatchSchema>
export const issuesPatchValidator = getValidator(issuesPatchSchema, dataValidator)
export const issuesPatchResolver = resolve<IssuesPatch, HookContext>({})

// Schema for allowed query properties
export const issuesQuerySchema = {
    $id: 'IssuesQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({...issuesSchema.properties, ...commonQueries.properties})
    }
} as const
export type IssuesQuery = FromSchema<typeof issuesQuerySchema>
export const issuesQueryValidator = getValidator(issuesQuerySchema, queryValidator)
export const issuesQueryResolver = resolve<IssuesQuery, HookContext>({})
