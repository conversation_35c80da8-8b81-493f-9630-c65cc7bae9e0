// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

export const ipSchema = {
    $id: 'IP',
    type: 'object',
    additionalProperties: true,
    properties: {
        ip: {type: 'string'},
        range: {type: 'array', items: {type: 'number'}},
        country: {type: 'string'},
        region: {type: 'string'},
        eu: {type: 'string'},
        timezone: {type: 'string'},
        city: {type: 'string'},
        lngLat: {type: 'array', items: {type: 'number'}},
        ll: {type: 'array', items: {type: 'number'}},
        metro: {type: 'number'},
        area: {type: 'number'}
    }
} as const;

export type IP = FromSchema<typeof ipSchema>

export const visitSchema = {
    $id: 'Visit',
    type: 'object',
    additionalProperties: true,
    properties: {
        date: {},
        domain: { type: 'string' },
        ipInfo: {type: 'object', additionalProperties: true, properties: {...ipSchema.properties}},
        incognito: {type: 'boolean'}
    }
} as const;
export type Visit = FromSchema<typeof visitSchema>

// Main data model schema
export const fingerprintsSchema = {
    $id: 'Fingerprints',
    type: 'object',
    additionalProperties: true,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        turnstile: {
            type: 'object',
            additionalProperties: true,
            properties: {
                success: {type: 'boolean'},
                challenge_ts: {type: 'string'},
                hostname: {type: 'string'},
                'error-codes': {type: 'array'},
                action: {type: 'string'},
                cdata: { type: 'string'},
                metadata: { type: 'object' }
            }
        },
        visitorId: {type: 'string'},
        visits: {
            type: 'array',
            items: {type: 'object', additionalProperties: true, properties: {...visitSchema.properties}}
        },
        name: { type: 'string' },
        manufacturer: { type: 'string' },
        product: { type: 'string' },
        osName: { type: 'string' },
        screenWidth: {},
        touch: { type: 'boolean' },
        incognito: { type: 'boolean' },
        type: { type: 'string' },
        ua: { type: 'string' },
        ipInfo: {
            type: 'object',
            additionalProperties: true,
            properties: {...ipSchema.properties}
        },
        ...commonFields.properties

    }
} as const

export type Fingerprints = FromSchema<typeof fingerprintsSchema>
export const fingerprintsValidator = getValidator(fingerprintsSchema, dataValidator)
export const fingerprintsResolver = resolve<Fingerprints, HookContext>({})

export const fingerprintsExternalResolver = resolve<Fingerprints, HookContext>({})

// Schema for creating new data
export const fingerprintsDataSchema = {
    $id: 'FingerprintsData',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...fingerprintsSchema.properties
    }
} as const
export type FingerprintsData = FromSchema<typeof fingerprintsDataSchema>
export const fingerprintsDataValidator = getValidator(fingerprintsDataSchema, dataValidator)
export const fingerprintsDataResolver = resolve<FingerprintsData, HookContext>({})

// Schema for updating existing data
export const fingerprintsPatchSchema = {
    $id: 'FingerprintsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...fingerprintsSchema.properties,
        ...commonPatch(fingerprintsSchema.properties).properties
    }
} as const
export type FingerprintsPatch = FromSchema<typeof fingerprintsPatchSchema>
export const fingerprintsPatchValidator = getValidator(fingerprintsPatchSchema, dataValidator)
export const fingerprintsPatchResolver = resolve<FingerprintsPatch, HookContext>({})

// Schema for allowed query properties
export const fingerprintsQuerySchema = {
    $id: 'FingerprintsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(fingerprintsSchema.properties)
    }
} as const
export type FingerprintsQuery = FromSchema<typeof fingerprintsQuerySchema>
export const fingerprintsQueryValidator = getValidator(fingerprintsQuerySchema, queryValidator)
export const fingerprintsQueryResolver = resolve<FingerprintsQuery, HookContext>({})
