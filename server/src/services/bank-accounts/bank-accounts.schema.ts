// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const bankAccountsSchema = {
    $id: 'BankAccounts',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'owner', 'type', 'accountNumber', 'routingNumber'],
    properties: {
        _id: ObjectIdSchema(),
        owner: ObjectIdSchema(),
        nickname: {type: 'string'},
        hsa: { type: 'boolean' },
        type: {type: 'string', enum: ['business', 'individual']},
        description: {type: 'string'},
        terms_accepted: {type: 'string'},
        business_rep: ObjectIdSchema(),
        accountNumber: {type: 'string'},
        last4: {type: 'string'},
        routingNumber: {type: 'string'},
        accountType: {type: 'string', enum: ['checking', 'savings']},
        bankName: {type: 'string'},
        canAch: {type: 'boolean'},
        canWire: {type: 'boolean'},
        verification: {
            type: 'object',
            properties: {
                exceptionDetails: {
                    type: 'object', properties: {
                        achReturnCode: {type: 'string'},
                        description: {type: 'string'},
                        rtpRejectionCode: {type: 'string'}
                    }
                },
                code: {type: 'string'},
                verified: { type: 'boolean' },
                status: {type: 'string', enum: ['new', 'sent-credit', 'failed', 'not-sent', 'verified', 'max-attempts-exceeded', 'expired', 'successful']},
                verificationMethod: {type: 'string', enum: ['instant', 'ach', 'micro']}
            }
        },
        mandate: {
            type: 'object',
            properties: {
                acceptedAt: {},
                ip: {type: 'string'},
                ua: {type: 'string'},
                copy: {type: 'string'}
            }
        },
        setupIntent: {type: 'string'},
        latestPaymentIntent: {type: 'string'},
        fca: {type: 'string'},
        moov_link_id: {type: 'string'},
        status: {type: 'string'},
        ...commonFields.properties
    }
} as const
export type BankAccounts = FromSchema<typeof bankAccountsSchema>
export const bankAccountsValidator = getValidator(bankAccountsSchema, dataValidator)
export const bankAccountsResolver = resolve<BankAccounts, HookContext>({
    accountType: async (val) => {
        if (!val) return 'checking';
        return val;
    }
})

export const bankAccountsExternalResolver = resolve<BankAccounts, HookContext>({})

// Schema for creating new data
export const bankAccountsDataSchema = {
    $id: 'BankAccountsData',
    type: 'object',
    additionalProperties: false,
    required: ['owner', 'accountNumber', 'routingNumber', 'bankName', 'type'],
    properties: {
        ...bankAccountsSchema.properties
    }
} as const
export type BankAccountsData = FromSchema<typeof bankAccountsDataSchema>
export const bankAccountsDataValidator = getValidator(bankAccountsDataSchema, dataValidator)
export const bankAccountsDataResolver = resolve<BankAccountsData, HookContext>({})

// Schema for updating existing data
export const bankAccountsPatchSchema = {
    $id: 'BankAccountsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...bankAccountsSchema.properties,
        ...commonPatch(bankAccountsSchema.properties).properties
    }
} as const
export type BankAccountsPatch = FromSchema<typeof bankAccountsPatchSchema>
export const bankAccountsPatchValidator = getValidator(bankAccountsPatchSchema, dataValidator)
export const bankAccountsPatchResolver = resolve<BankAccountsPatch, HookContext>({})

// Schema for allowed query properties
export const bankAccountsQuerySchema = {
    $id: 'BankAccountsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(bankAccountsSchema.properties)
    }
} as const
export type BankAccountsQuery = FromSchema<typeof bankAccountsQuerySchema>
export const bankAccountsQueryValidator = getValidator(bankAccountsQuerySchema, queryValidator)
export const bankAccountsQueryResolver = resolve<BankAccountsQuery, HookContext>({})
