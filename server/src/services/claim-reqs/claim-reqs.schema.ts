// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch, imageSchema} from '../../utils/common/schemas.js';
import {claimsSchema} from '../claims/claims.schema.js';

// Main data model schema
export const claimReqsSchema = {
  $id: 'ClaimReqs',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    plan: ObjectIdSchema(),
    org: ObjectIdSchema(),
    patient: ObjectIdSchema(),
    person: ObjectIdSchema(),
    care: ObjectIdSchema(),
    visit: ObjectIdSchema(),
    claim: ObjectIdSchema(),
    provider: ObjectIdSchema(),
    practitioner: ObjectIdSchema(),
    providerOrg: ObjectIdSchema(),
    threads: { type: 'array', items: ObjectIdSchema() },
    files: {
      type: 'object', patternProperties: {
        "^.*$": imageSchema
      }
    },
    status: { type: 'string', enum: ['unopened', 'pending', 'approved', 'rejected']},
    removeRequest: { type: 'boolean' },
    claimData: {type: 'object', properties: {...claimsSchema.properties}},
    ...commonFields.properties
  }
} as const
export type ClaimReqs = FromSchema<typeof claimReqsSchema>
export const claimReqsValidator = getValidator(claimReqsSchema, dataValidator)
export const claimReqsResolver = resolve<ClaimReqs, HookContext>({})

export const claimReqsExternalResolver = resolve<ClaimReqs, HookContext>({})

// Schema for creating new data
export const claimReqsDataSchema = {
  $id: 'ClaimReqsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...claimReqsSchema.properties
  }
} as const
export type ClaimReqsData = FromSchema<typeof claimReqsDataSchema>
export const claimReqsDataValidator = getValidator(claimReqsDataSchema, dataValidator)
export const claimReqsDataResolver = resolve<ClaimReqsData, HookContext>({
  status: async (val) => {
    if(!val) return 'unopened';
    return val
  }
})

// Schema for updating existing data
export const claimReqsPatchSchema = {
  $id: 'ClaimReqsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...claimReqsSchema.properties,
    ...commonPatch(claimReqsSchema.properties).properties
  }
} as const
export type ClaimReqsPatch = FromSchema<typeof claimReqsPatchSchema>
export const claimReqsPatchValidator = getValidator(claimReqsPatchSchema, dataValidator)
export const claimReqsPatchResolver = resolve<ClaimReqsPatch, HookContext>({})

// Schema for allowed query properties
export const claimReqsQuerySchema = {
  $id: 'ClaimReqsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(claimReqsSchema.properties)
  }
} as const
export type ClaimReqsQuery = FromSchema<typeof claimReqsQuerySchema>
export const claimReqsQueryValidator = getValidator(claimReqsQuerySchema, queryValidator)
export const claimReqsQueryResolver = resolve<ClaimReqsQuery, HookContext>({})
