// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {addToSet, commonFields, pull, commonQueries, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const dropsSchema = {
  $id: 'Drops',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    tags: { type: 'array', items: { type: 'string' }},
    type: { type: 'string', enum: ['question', 'answer']},
    archives: {
      type: 'object', properties: {
        title: {type: 'array', items: {type: 'string'}},
        body: {type: 'array', items: {type: 'string'}}
      }
    },
    topAnswer: ObjectIdSchema(),
    topScore: { type: 'number' },
    title: { type: 'string' },
    body: { type: 'string' },
    class: { type: 'string' }, //individual, business, provider
    anonymous: { type: 'boolean' },
    threads: { type: 'array', items: ObjectIdSchema() },
    voteCount: { type: 'number' },
    upVotes: { type: 'array', items: ObjectIdSchema() },
    downVotes: { type: 'array', items: ObjectIdSchema() },
    ...commonFields.properties

  }
} as const
export type Drops = FromSchema<typeof dropsSchema>
export const dropsValidator = getValidator(dropsSchema, dataValidator)
export const dropsResolver = resolve<Drops, HookContext>({})

export const dropsExternalResolver = resolve<Drops, HookContext>({})

// Schema for creating new data
export const dropsDataSchema = {
  $id: 'DropsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...dropsSchema.properties
  }
} as const
export type DropsData = FromSchema<typeof dropsDataSchema>
export const dropsDataValidator = getValidator(dropsDataSchema, dataValidator)
export const dropsDataResolver = resolve<DropsData, HookContext>({})

const pushPull = [{ path: 'threads', type: ObjectIdSchema() }]

// Schema for updating existing data
export const dropsPatchSchema = {
  $id: 'DropsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...dropsSchema.properties,
    ...commonPatch(dropsSchema.properties).properties,
    $addToSet: addToSet(pushPull),
    $pull: pull(pushPull)
  }
} as const
export type DropsPatch = FromSchema<typeof dropsPatchSchema>
export const dropsPatchValidator = getValidator(dropsPatchSchema, dataValidator)
export const dropsPatchResolver = resolve<DropsPatch, HookContext>({})

// Schema for allowed query properties
export const dropsQuerySchema = {
  $id: 'DropsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax({...dropsSchema.properties, ...commonQueries.properties}),
  }
} as const
export type DropsQuery = FromSchema<typeof dropsQuerySchema>
export const dropsQueryValidator = getValidator(dropsQuerySchema, queryValidator)
export const dropsQueryResolver = resolve<DropsQuery, HookContext>({})
