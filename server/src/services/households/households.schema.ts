// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addressSchema, addToSet, commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js';

export const relationships = [
    'self',
    'spouse',
    'child',
    'father',
    'mother',
    'grandfather',
    'grandmother',
    'grandson',
    'granddaughter',
    'son_in_law',
    'daughter_in_law',
    'uncle',
    'aunt',
    'nephew',
    'niece',
    'cousin',
    'guardian',
    'stepparent',
    'stepson',
    'stepdaughter',
    'adopted_child',
    'foster_child',
    'sister',
    'brother',
    'brother_in_law',
    'sister_in_law',
    'mother_in_law',
    'father_in_law',
    'ward',
    'sponsored_dependent',
    'dependent_minor_dependent',
    'ex_spouse',
    'court_appointed_guardian',
    'collateral_dependent',
    'life_partner',
    'annultant',
    'trustee',
    'other_relationship',
    'other_relative'
]

const enrollExtras = {
    type: 'object',
    properties: {
        monthsSinceSmoked: {type: 'number'},
        disabled: {type: 'boolean'},
        incarcerated: {type: 'boolean'},
        latino: { type: 'boolean' },
        native: { type: 'boolean' },
        pregnant: { type: 'boolean' },
        us_citizen: { type: 'boolean' },
        adl_assist: { type: 'boolean' },
        medicaid: { type: 'boolean' },
        medicaid_ineligible: { type: 'boolean' },
        outside_coverage: { type: 'boolean' },
        outside_coverage_end: { type: 'string' },
        job_coverage: { type: 'boolean' }
    }
} as const

import { setDefEstUnits } from '../comps/comps.schema.js';
// Main data model schema
export const householdsSchema = {
    $id: 'Households',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'person'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        filingAs: { type: 'string', enum: ['s', 'ms', 'hh', 'mj']},
        providers: { type: 'array', items: ObjectIdSchema()},
        practitioners: { type: 'array', items: ObjectIdSchema()},
        magi: { type: 'number' },
        qual_events: {
            type: 'object',
            patternProperties: {
                "^.*$": { type: 'string' } //even name key and date of occurrence
            }
        },
        incomes: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        name: {type: 'string'},
                        amount: {type: 'number'},
                        off: {type: 'boolean'},
                        interval: {type: 'string', enum: ['hour', 'day', 'week', 'month', 'quarter', 'year', 'once']},
                        class: {type: 'string', enum: ['ee', 'ic']},
                        estHours: {type: 'number'}
                    }
                }
            }
        },
        deductions: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        off: { type: 'boolean' },
                        amount: { type: 'number' },
                        atl: { type: 'boolean' }
                    }
                }
            }
        },
        ...enrollExtras.properties,
        members: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        relation: {type: 'string', enum: relationships},
                        dependent:  { type: 'boolean' },
                        annualIncome: {type: 'number'},
                        address: addressSchema,
                        ...enrollExtras.properties
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type Households = FromSchema<typeof householdsSchema>
export const householdsValidator = getValidator(householdsSchema, dataValidator)
export const householdsResolver = resolve<Households, HookContext>({
    properties: {}
})

export const householdsExternalResolver = resolve<Households, HookContext>({})

// Schema for creating new data
export const householdsDataSchema = {
    $id: 'HouseholdsData',
    type: 'object',
    additionalProperties: false,
    required: ['person'],
    properties: {
        ...householdsSchema.properties
    }
} as const
export type HouseholdsData = FromSchema<typeof householdsDataSchema>
export const householdsDataValidator = getValidator(householdsDataSchema, dataValidator)
export const householdsDataResolver = resolve<HouseholdsData, HookContext>({})

// Schema for updating existing data
export const householdsPatchSchema = {
    $id: 'HouseholdsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...householdsSchema.properties,
        ...commonPatch(householdsSchema.properties).properties,
        $addToSet: addToSet([{ path: 'providers', type: ObjectIdSchema()}, { path: 'practitioners', type: ObjectIdSchema()}])
    }
} as const
export type HouseholdsPatch = FromSchema<typeof householdsPatchSchema>
export const householdsPatchValidator = getValidator(householdsPatchSchema, dataValidator)
export const householdsPatchResolver = resolve<HouseholdsPatch, HookContext>({})

// Schema for allowed query properties
export const householdsQuerySchema = {
    $id: 'HouseholdsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({...householdsSchema.properties, ...commonQueries.properties, total_income: {}})
    }
} as const
export type HouseholdsQuery = FromSchema<typeof householdsQuerySchema>
export const householdsQueryValidator = getValidator(householdsQuerySchema, queryValidator)
export const householdsQueryResolver = resolve<HouseholdsQuery, HookContext>({})
