// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, pull} from '../../utils/common/schemas.js';


// Main data model schema
export const pricesSchema = {
    $id: 'Prices',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        provider: ObjectIdSchema(),
        bundle: ObjectIdSchema(),
        source: {type: 'string', enum: ['vision', 'bill', 'provider', 'dataset', 'va', 'upload', 'ptf']},
        description: {type: 'string'},
        notes: {type: 'string'},
        session: {type: 'string'},
        state: {type: 'string'}, //two letter US state code
        eraser: ObjectIdSchema(),
        name: {type: 'string'},
        providerName: {type: 'string'},
        price: {type: 'number'},
        uom: {type: 'string'},
        batch: {type: 'string'},
        uid: { type: 'string' },
        subject: ObjectIdSchema(),
        type: {type: 'string', enum: ['procedures', 'meds', 'other']},
        code: {type: 'string'},
        billing_code: {type: 'string'},
        carrier: {type: 'string'},
        ndcs: {type: 'array', items: {type: 'string'}},
        relatedCheckedAt: {},
        ndc: {type: 'string'},
        ndc10: { type: 'string' },
        ndc11: { type: 'string' },
        labeler: {type: 'string'}, //first 6 NDC 11
        product: { type: 'string' }, //4 digit product code
        package: { type: 'string' }, //2 digit package code
        rxcui: {type: 'string'},
        s_f: {type: 'string'},
        ...commonFields.properties
    }
} as const
export type Prices = FromSchema<typeof pricesSchema>
export const pricesValidator = getValidator(pricesSchema, dataValidator)
export const pricesResolver = resolve<Prices, HookContext>({
    type: async (val) => {
        if (!val) return 'procedures'
        return val
    }
})

export const pricesExternalResolver = resolve<Prices, HookContext>({})

// Schema for creating new data
export const pricesDataSchema = {
    $id: 'PricesData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...pricesSchema.properties
    }
} as const
export type PricesData = FromSchema<typeof pricesDataSchema>
export const pricesDataValidator = getValidator(pricesDataSchema, dataValidator)
export const pricesDataResolver = resolve<PricesData, HookContext>({})

const pushPull = [{ path: 'ndcs', type: { type: 'string' }}]
// Schema for updating existing data
export const pricesPatchSchema = {
    $id: 'PricesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...pricesSchema.properties,
        ...commonPatch(pricesSchema.properties).properties,
        $addToSet: addToSet(pushPull ),
        $push: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type PricesPatch = FromSchema<typeof pricesPatchSchema>
export const pricesPatchValidator = getValidator(pricesPatchSchema, dataValidator)
export const pricesPatchResolver = resolve<PricesPatch, HookContext>({})

// Schema for allowed query properties
export const pricesQuerySchema = {
    $id: 'PricesQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(pricesSchema.properties),
        name: {},
        description: {},
        s_f: {},
        price: {}
    }
} as const
export type PricesQuery = FromSchema<typeof pricesQuerySchema>
export const pricesQueryValidator = getValidator(pricesQuerySchema, queryValidator)
export const pricesQueryResolver = resolve<PricesQuery, HookContext>({})
