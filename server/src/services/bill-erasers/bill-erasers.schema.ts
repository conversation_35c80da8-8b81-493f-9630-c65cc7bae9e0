// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema, mandate, pull} from '../../utils/common/schemas.js';

// Main data model schema
export const billErasersSchema = {
    $id: 'BillErasers',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        provider: ObjectIdSchema(),
        session: {type: 'string'},
        rx: {type: 'boolean'},
        prefContact: {type: 'string', enum: ['phone', 'email']},
        providerName: {type: 'string'},
        notes: {type: 'string'},
        state: {type: 'string'},
        zip: {type: 'string'},
        msa: {type: 'string'},
        income: {type: 'number'},
        hhCount: {type: 'number'},
        carrier: {type: 'string'},
        assignedTo: {type: 'array', items: ObjectIdSchema()},
        threads: {type: 'array', items: ObjectIdSchema()},
        status: {type: 'number', enum: [1, 2, 3, 4, 5, 6]},
        disposition: {type: 'number', enum: [1, 2, 3, 4, 5]},
        files: {type: 'array', items: imageSchema},
        savePercent: {type: 'number'},
        mandate,
        personRelationship: {type: 'string'},
        hipaa: {type: 'string'},
        signature: {type: 'string'},
        original_price: {type: 'number'},
        est_price: {type: 'number'},
        prices: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        price: ObjectIdSchema(),
                        rev_code: { type: 'string' },
                        status: { type: 'string', enum: ['data', 'confirmed']},
                        threads: { type: 'array', items: ObjectIdSchema() }
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type BillErasers = FromSchema<typeof billErasersSchema>
export const billErasersValidator = getValidator(billErasersSchema, dataValidator)
export const billErasersResolver = resolve<BillErasers, HookContext>({
    status: async (val) => {
        return val || 1
    }
})

export const billErasersExternalResolver = resolve<BillErasers, HookContext>({})

// Schema for creating new data
export const billErasersDataSchema = {
    $id: 'BillErasersData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...billErasersSchema.properties
    }
} as const
export type BillErasersData = FromSchema<typeof billErasersDataSchema>
export const billErasersDataValidator = getValidator(billErasersDataSchema, dataValidator)
export const billErasersDataResolver = resolve<BillErasersData, HookContext>({})

const pushPull = [
    {path: 'files', type: imageSchema}
]
// Schema for updating existing data
export const billErasersPatchSchema = {
    $id: 'BillErasersPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...billErasersSchema.properties,
        ...commonPatch(billErasersSchema.properties).properties,
        $push: addToSet(pushPull),
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type BillErasersPatch = FromSchema<typeof billErasersPatchSchema>
export const billErasersPatchValidator = getValidator(billErasersPatchSchema, dataValidator)
export const billErasersPatchResolver = resolve<BillErasersPatch, HookContext>({})

// Schema for allowed query properties
export const billErasersQuerySchema = {
    $id: 'BillErasersQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(billErasersSchema.properties)
    }
} as const
export type BillErasersQuery = FromSchema<typeof billErasersQuerySchema>
export const billErasersQueryValidator = getValidator(billErasersQuerySchema, queryValidator)
export const billErasersQueryResolver = resolve<BillErasersQuery, HookContext>({})
