// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js';

// Main data model schema
export const conditionsSchema = {
    $id: 'Conditions',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        standard: {type: 'string'},
        code: {type: 'string'},
        link: {type: 'string'},
        chapter: {type: 'string'},
        name: {type: 'string'},
        description: {type: 'string'},
        type: {type: 'string'},
        procedures: { type: 'array', items: ObjectIdSchema() },
        procedureComments: { type: 'string' },
        ...commonFields.properties
    }
} as const
export type Conditions = FromSchema<typeof conditionsSchema>
export const conditionsValidator = getValidator(conditionsSchema, dataValidator)
export const conditionsResolver = resolve<Conditions, HookContext>({})

export const conditionsExternalResolver = resolve<Conditions, HookContext>({})

// Schema for creating new data
export const conditionsDataSchema = {
    $id: 'ConditionsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...conditionsSchema.properties
    }
} as const
export type ConditionsData = FromSchema<typeof conditionsDataSchema>
export const conditionsDataValidator = getValidator(conditionsDataSchema, dataValidator)
export const conditionsDataResolver = resolve<ConditionsData, HookContext>({})

// Schema for updating existing data
export const conditionsPatchSchema = {
    $id: 'ConditionsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...conditionsSchema.properties,
        ...commonPatch(conditionsSchema.properties).properties
    }
} as const
export type ConditionsPatch = FromSchema<typeof conditionsPatchSchema>
export const conditionsPatchValidator = getValidator(conditionsPatchSchema, dataValidator)
export const conditionsPatchResolver = resolve<ConditionsPatch, HookContext>({})

// Schema for allowed query properties
export const conditionsQuerySchema = {
    $id: 'ConditionsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...commonQueries.properties,
        ...querySyntax(conditionsSchema.properties),
        name: {},
        medical_name: {},
        code: {}
    }
} as const
export type ConditionsQuery = FromSchema<typeof conditionsQuerySchema>
export const conditionsQueryValidator = getValidator(conditionsQuerySchema, queryValidator)
export const conditionsQueryResolver = resolve<ConditionsQuery, HookContext>({})
