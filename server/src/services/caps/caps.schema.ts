// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js';

// Main data model schema
export const capsSchema = {
  $id: 'Caps',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'subject', 'did'],
  properties: {
    _id: ObjectIdSchema(),
    subject: ObjectIdSchema(),
    subjectService: { type: 'string' },
    did: { type: 'string' },
    caps: {
      type: 'object',
      patternProperties: {
        "^.*$": {
          type: 'object',
          properties: {
            description: { type: 'string' },
            ucan: { type: 'string' },
            logins: { type: 'array', items: ObjectIdSchema() }
          }
        }
      }
    },
    ...commonFields.properties
  }
} as const
export type Caps = FromSchema<typeof capsSchema>
export const capsValidator = getValidator(capsSchema, dataValidator)
export const capsResolver = resolve<Caps, HookContext>({})

export const capsExternalResolver = resolve<Caps, HookContext>({})

// Schema for creating new data
export const capsDataSchema = {
  $id: 'CapsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...capsSchema.properties
  }
} as const
export type CapsData = FromSchema<typeof capsDataSchema>
export const capsDataValidator = getValidator(capsDataSchema, dataValidator)
export const capsDataResolver = resolve<CapsData, HookContext>({})
// Schema for updating existing data
export const capsPatchSchema = {
  $id: 'CapsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...capsSchema.properties,
    ...commonPatch(capsSchema.properties).properties,
    $addToSet: {},
    $pull: {}
  }
} as const
export type CapsPatch = FromSchema<typeof capsPatchSchema>
export const capsPatchValidator = getValidator(capsPatchSchema, dataValidator)
export const capsPatchResolver = resolve<CapsPatch, HookContext>({})

// Schema for allowed query properties
export const capsQuerySchema = {
  $id: 'CapsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(capsSchema.properties),
    ...commonQueries.properties
  }
} as const
export type CapsQuery = FromSchema<typeof capsQuerySchema>
export const capsQueryValidator = getValidator(capsQuerySchema, queryValidator)
export const capsQueryResolver = resolve<CapsQuery, HookContext>({})
