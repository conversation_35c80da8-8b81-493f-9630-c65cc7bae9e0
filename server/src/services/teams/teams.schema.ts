// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema, phoneSchema, pull} from '../../utils/common/schemas.js';

// Main data model schema
export const teamsSchema = {
    $id: 'Teams',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        types: {type: 'array', items: {type: 'string', enum: ['sales', 'support']}},
        avatar: imageSchema,
        refs: {type: 'array', items: ObjectIdSchema()},
        invited: { type: 'array', items: ObjectIdSchema() },
        req: { type: 'array', items: ObjectIdSchema() },
        contract: ObjectIdSchema(),
        calendar: ObjectIdSchema(),
        phone: phoneSchema,
        sms: phoneSchema,
        email: {type: 'string'},
        priority: { type: 'array', items: ObjectIdSchema()},
        online: { type: 'array', items: ObjectIdSchema() },
        ...commonFields.properties,
    }
} as const
export type Teams = FromSchema<typeof teamsSchema>
export const teamsValidator = getValidator(teamsSchema, dataValidator)
export const teamsResolver = resolve<Teams, HookContext>({})

export const teamsExternalResolver = resolve<Teams, HookContext>({})

// Schema for creating new data
export const teamsDataSchema = {
    $id: 'TeamsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...teamsSchema.properties
    }
} as const
export type TeamsData = FromSchema<typeof teamsDataSchema>
export const teamsDataValidator = getValidator(teamsDataSchema, dataValidator)
export const teamsDataResolver = resolve<TeamsData, HookContext>({})

const pushPull = [
    { path: 'refs', type: ObjectIdSchema() }
]
// Schema for updating existing data
export const teamsPatchSchema = {
    $id: 'TeamsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...teamsSchema.properties,
        ...commonPatch(teamsSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull:  pull(pushPull)
    }
} as const
export type TeamsPatch = FromSchema<typeof teamsPatchSchema>
export const teamsPatchValidator = getValidator(teamsPatchSchema, dataValidator)
export const teamsPatchResolver = resolve<TeamsPatch, HookContext>({})

// Schema for allowed query properties
export const teamsQuerySchema = {
    $id: 'TeamsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(teamsSchema.properties)
    }
} as const
export type TeamsQuery = FromSchema<typeof teamsQuerySchema>
export const teamsQueryValidator = getValidator(teamsQuerySchema, queryValidator)
export const teamsQueryResolver = resolve<TeamsQuery, HookContext>({})
