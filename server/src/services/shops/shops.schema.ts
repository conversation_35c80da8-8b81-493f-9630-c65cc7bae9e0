// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, mandate, pull} from '../../utils/common/schemas.js';

const distSchema = {
    type: 'object',
    patternProperties: {
        "^.*$": {
            type: 'object',
            patternProperties: {
                "^.*$": {type: 'number'}
            }
        }
    }
} as const

const scoresSchema = {
    type: 'object',
    properties: {
        top1: {type: 'number'},
        top3: {type: 'number'},
        last: {type: 'number'},
        pr: {type: 'number'},
        pr_spend: {type: 'number'},
        pw: {type: 'number'},
        pw_spend: {type: 'number'},
        premium: {type: 'number'},
        average: {type: 'number'},
        median: {type: 'number'},
        aptc: {type: 'number'},
    }
} as const

export const placeSchema = {
    type: 'object',
    properties: {
        countyfips: {type: 'string'},
        zipcode: {type: 'string'},
        state: {type: 'string'}
    }
} as const
export const peopleSchema = {
    type: 'object', properties: {
        _id: ObjectIdSchema(),
        inactive: {type: 'boolean'},
        age: {type: 'number'},
        gender: {type: 'string', enum: ['male', 'female']},
        child: {type: 'boolean'},
        smoker: {type: 'boolean'},
        relation: {type: 'string'}
    }
} as const

export const vectorStore = {
    type: 'object', properties: {
        id: {type: 'string'},
        fileIds: {type: 'array', items: {type: 'string'}},
        updatedAt: {},
        resetId: { type: 'string' }
    }
} as const
// Main data model schema
export const shopsSchema = {
    $id: 'Shops',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        enrollment: ObjectIdSchema(),
        plan_coverage: ObjectIdSchema(),
        aiChatCount: {type: 'number'},
        vectorStore,
        resetId: {type: 'string'},
        limit_remaining: {type: 'number'},
        attest: {
            type: 'object',
            patternProperties: {
                "^.*$": mandate
            }
        },
        tax_rate: {type: 'number'},
        planYear: {type: 'string'},
        version: {type: 'string'},
        consent_to_changes: mandate,
        decline_changes: mandate,
        stats: {
            type: 'object', properties: {
                peopleEdited: {},
                inactive: {type: 'boolean'}, //Self inactive
                people: {type: 'array', items: peopleSchema},
                gender: {type: 'string', enum: ['male', 'female']},
                preEx: {type: 'boolean'},
                income: {type: 'number'},
                age: {type: 'number'},
                spend: {type: 'number'},
                ded: {type: 'number'},
                spouse: {type: 'boolean'},
                plus: {type: 'number'},
                household_size: {type: 'number'},
                smoker: {type: 'boolean'},
                risk: {type: 'number'},
                city: {type: 'string'},
                place: placeSchema
            }
        },
        mult: {type: 'number'},
        useAptc: {type: 'boolean'},
        skipAptc: {type: 'boolean'},
        compare_ids: { type: 'array', items: { type: 'string'} },
        spend_dist: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        spend: {type: 'number'},
                        count: {type: 'number'}
                    }
                }
            }
        },
        allSpend: {type: 'number'},
        byYear: {type: 'array', items: {type: 'number'}},
        issuers: {type: 'array', items: {type: 'string'}},
        aca_issuers: {type: 'array', items: {type: 'string'}},
        lastRun: {},
        worst10: {type: 'array', items: {type: 'number'}},
        distribution: distSchema,
        distribution_ptc: distSchema,
        aptc: {type: 'number'},
        slcsp: {type: 'number'},
        coverage_scores: scoresSchema,
        coverage_scores_ptc: scoresSchema,
        spend: {type: 'number'},
        simsCount: {type: 'number'},
        coverages: {type: 'array', items: {}},
        stage: {type: 'string', enum: ['shopping', 'review', 'sign', 'complete', 'closed']},
        status: {type: 'string'},
        policy: {type: 'string'},
        coverage: ObjectIdSchema(),
        team: ObjectIdSchema(),
        gps: ObjectIdSchema(),
        majorMedical: { type: 'string' },
        keepOld: { type: 'boolean' },
        cashInLieu: { type: 'boolean' },
        deduction: { type: 'number' }, //always converted to monthly
        interval: { type: 'string' }, //just to show it back the way they entered it
        choices: {
            type: 'object', patternProperties: {
                "^.*$": ObjectIdSchema()
            }
        },
        lastPremium: { type: 'number' },
        comments: {type: 'string'},
        fid: { type: 'string' },
        form: ObjectIdSchema(),
        anon: {type: 'boolean'},
        jobTitle: {type: 'string'},
        name: {type: 'string'},
        email: {type: 'string'},
        ...commonFields.properties
    }
} as const
export type Shops = FromSchema<typeof shopsSchema>
export const shopsValidator = getValidator(shopsSchema, dataValidator)
export const shopsResolver = resolve<Shops, HookContext>({
    stage: async (val) => {
        return val || 'shopping'
    },
    mult: async (val) => {
        if (!val) return 12;
        return val;
    }
})

export const shopsExternalResolver = resolve<Shops, HookContext>({})

// Schema for creating new data
export const shopsDataSchema = {
    $id: 'ShopsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...shopsSchema.properties
    }
} as const
export type ShopsData = FromSchema<typeof shopsDataSchema>
export const shopsDataValidator = getValidator(shopsDataSchema, dataValidator)
export const shopsDataResolver = resolve<ShopsData, HookContext>({})

const pushPull = [
    {path: 'coverages', type: ObjectIdSchema()}
]
// Schema for updating existing data
export const shopsPatchSchema = {
    $id: 'ShopsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...shopsSchema.properties,
        ...commonPatch(shopsSchema.properties).properties,
        $pull: pull(pushPull)
    }
} as const
export type ShopsPatch = FromSchema<typeof shopsPatchSchema>
export const shopsPatchValidator = getValidator(shopsPatchSchema, dataValidator)
export const shopsPatchResolver = resolve<ShopsPatch, HookContext>({})

// Schema for allowed query properties
export const shopsQuerySchema = {
    $id: 'ShopsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(shopsSchema.properties)
    }
} as const
export type ShopsQuery = FromSchema<typeof shopsQuerySchema>
export const shopsQueryValidator = getValidator(shopsQuerySchema, queryValidator)
export const shopsQueryResolver = resolve<ShopsQuery, HookContext>({})
