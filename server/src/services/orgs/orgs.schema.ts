// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    phoneSchema,
    commonFields,
    commonQueries,
    imageSchema,
    addressSchema, commonPatch
} from "../../utils/common/schemas.js";

export const payrollSettings = {
    type: 'object',
    properties: {
        frequency: {
            type: 'string',
            enum: ['daily', 'weekly', 'bi-weekly', 'semi-monthly', 'monthly', 'quarterly', 'annually', 'int']
        }
    }
} as const

export const ownSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        did: {type: 'string'},
        percent: {type: 'number'},
        id: ObjectIdSchema(), //ppls
        attribute: {type: 'array', items: ObjectIdSchema()},
        executive: {type: 'boolean'},
        director: {type: 'boolean'},
        position: {type: 'string'} //enum: ['shareholder', 'member', 'board', 'trustee', 'partner', 'owner', 'officer']
    }
} as const;


// Main data model schema
export const orgsSchema = {
    $id: 'Orgs',
    type: 'object',
    additionalProperties: true,
    required: ['_id', 'name'],
    properties: {
        _id: ObjectIdSchema(),
        bankAccounts: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        name: {type: 'string'},
                        id: ObjectIdSchema(),
                        default: {type: 'boolean'}
                    }
                }
            }
        },
        address: addressSchema,
        addresses: {type: 'array', items: addressSchema},
        affiliatedOrgs: {type: 'array', items: ObjectIdSchema()},
        asg: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        type: {type: 'string', enum: ['A', 'B']},
                        orgs: {
                            type: 'object', patternProperties: {
                                "^.*$": {
                                    type: 'object',
                                    properties: {
                                        type: {type: 'string', enum: ['A', 'B', 'FSO', 'M']}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        avatar: imageSchema,
        budgets: {type: 'array', items: ObjectIdSchema()},
        controls: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        identical: {type: 'number'},
                        common: {type: 'number'},
                        control: {type: 'boolean'},
                        orgs: {
                            type: 'object', patternProperties: {
                                "^.*$": {
                                    type: 'object',
                                    properties: {
                                        identical: {type: 'number'},
                                        owners: {type: 'array', items: ObjectIdSchema()},
                                        parent: {},
                                        total: {type: 'number'}
                                    }
                                }
                            }
                        },
                        brotherSister: {type: 'boolean'},
                        parentSub: {type: 'boolean'}
                    }
                }
            }
        },
        customers: {type: 'array', items: ObjectIdSchema()},
        coverImage: imageSchema,
        did: {type: 'string'},
        ein: {type: 'string'},
        email: {type: 'string'},
        emails: {type: 'array', items: {type: 'string'}},
        hostAccounts: {type: 'array', items: ObjectIdSchema()},
        industries: {type: 'array', items: {type: 'string'}},
        images: {type: 'array', items: imageSchema},
        groups: {
            type: 'object', patternProperties: {
                "^.*$": ObjectIdSchema()
            }
        },
        groupIds: {type: 'array', items: ObjectIdSchema()}, //same as groups but for querying purposes
        legalName: {type: 'string'},
        managementOrgs: {type: 'array', items: ObjectIdSchema()},
        name: {type: 'string'},
        plans: {type: 'array', items: ObjectIdSchema()},
        providerAccounts: {type: 'array', items: ObjectIdSchema()},
        owners: {
            type: 'array', items: {
                type: 'object',
                properties: {
                    ...ownSchema.properties,
                    idService: {type: 'string'},
                    name: {type: 'string'},
                    email: {type: 'string'},
                    phone: phoneSchema,
                    address: addressSchema
                }
            }
        },
        ownerSync: {type: 'boolean'},
        phone: phoneSchema,
        phones: {type: 'array', items: phoneSchema},
        payroll_settings: payrollSettings,
        public: {type: 'boolean'},
        ramp_vendor_id: { type: 'string'},
        refs: {type: 'array', items: ObjectIdSchema()},
        singleMember: {type: 'boolean'},
        memberCount: {type: 'number'},
        memberCountAt: {},
        structure: {
            type: 'string',
            enum: ['PARTNERSHIP', 'SOLE_PROPRIETOR', 'NONPROFIT', 'CORPORATION', 'S-CORP', 'LLC', 'LLP', 'OTHER']
        },
        // owns: {type: 'array', items: ownSchema},
        taxes: {
            type: 'object', properties: {
                incomeTaxForm: {type: 'string'} //enum: ['990', '1040', '1120', '1120S', '1065']
            }
        },
        taxStructure: {type: 'string'},
        threads: {type: 'array', items: ObjectIdSchema()},
        careAccounts: {type: 'array', items: ObjectIdSchema()},
        treasury: {
            type: 'object',
            properties: {
                customerId: {type: 'string'},
                id: {type: 'string'}, //account id
                business_profile: {
                    type: 'object', properties: {
                        support_email: {type: 'string'},
                        annual_revenue: {
                            type: 'object', properties: {
                                amount: {type: 'number'},//cents
                                currency: {type: 'string'},
                                fiscal_year_end: {type: 'string'}
                            }
                        },
                        estimated_worker_count: {type: 'number'},
                        mcc: {type: 'string'},
                        mcc_name: {type: 'string'},
                        naics: {type: 'string'},
                        sic: {type: 'string'},
                        product_description: {type: 'string'},
                        support_address: addressSchema,
                        support_url: {type: 'string'},
                        url: {type: 'string'},
                        representatives: {
                            type: 'array', items: {
                                type: 'object', properties: {
                                    isController: { type: 'boolean' }
                                }
                            }
                        }
                    }
                },

            }
        },
        website: {type: 'string'},
        ...commonFields.properties
    },
} as const
export type Orgs = FromSchema<typeof orgsSchema>
export const orgsValidator = getValidator(orgsSchema, dataValidator)
export const orgsResolver = resolve<Orgs, HookContext>({})

export const orgsExternalResolver = resolve<Orgs, HookContext>({});


// Schema for creating new data
export const orgsDataSchema = {
    $id: 'OrgsData',
    type: 'object',
    additionalProperties: true,
    required: ['name'],
    properties: {
        ...orgsSchema.properties
    }
} as const
export type OrgsData = FromSchema<typeof orgsDataSchema>
export const orgsDataValidator = getValidator(orgsDataSchema, dataValidator)
export const orgsDataResolver = resolve<OrgsData, HookContext>({})

// Schema for updating existing data
export const orgsPatchSchema = {
    $id: 'OrgsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...commonPatch(orgsSchema.properties).properties,
        ...orgsSchema.properties
    }
} as const
export type OrgsPatch = FromSchema<typeof orgsPatchSchema>
export const orgsPatchValidator = getValidator(orgsPatchSchema, dataValidator)
export const orgsPatchResolver = resolve<OrgsPatch, HookContext>({})

// Schema for allowed query properties
export const orgsQuerySchema = {
    $id: 'OrgsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({
            ...commonQueries.properties,
            ...orgsSchema.properties,
            owners: {
                anyOf: [orgsSchema.properties.owners, {
                    type: 'object',
                    properties: {$elemMatch: {}}
                }]
            },
        }),
        'owners.id': ObjectIdSchema(),
        name: {},
        email: {},
        phone: {},
        _owners: {},
        _attribute: {}
    }
} as const
export type OrgsQuery = FromSchema<typeof orgsQuerySchema>
export const orgsQueryValidator = getValidator(orgsQuerySchema, queryValidator)
export const orgsQueryResolver = resolve<OrgsQuery, HookContext>({})
