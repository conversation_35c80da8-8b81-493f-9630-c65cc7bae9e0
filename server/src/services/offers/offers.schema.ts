// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

export const feeEnum = ['alg', 'pepm', 'pmpm', 'flat'] as const
import { guideEnum } from '../hosts/hosts.schema.js';
// Main data model schema
export const offersSchema = {
  $id: 'Offers',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'plan', 'host', 'fee', 'feeType'],
  properties: {
    _id: ObjectIdSchema(),
    plan: ObjectIdSchema(),
    contract: ObjectIdSchema(),
    role: { type: 'string', enum: guideEnum },
    fee: {type: 'number'},
    feeType: { type: 'string', enum: feeEnum},
    threads: {type: 'array', items: ObjectIdSchema()},
    status: { type: 'string', enum: ['pending', 'rejected', 'active'] },
    ...commonFields.properties
  }
} as const
export type Offers = FromSchema<typeof offersSchema>
export const offersValidator = getValidator(offersSchema, dataValidator)
export const offersResolver = resolve<Offers, HookContext>({
  status: async (val) => {
    if(!val) return 'pending';
    return val;
  }
})

export const offersExternalResolver = resolve<Offers, HookContext>({})

// Schema for creating new data
export const offersDataSchema = {
  $id: 'OffersData',
  type: 'object',
  additionalProperties: false,
  required: ['plan', 'host', 'fee', 'feeType'],
  properties: {
    ...offersSchema.properties
  }
} as const
export type OffersData = FromSchema<typeof offersDataSchema>
export const offersDataValidator = getValidator(offersDataSchema, dataValidator)
export const offersDataResolver = resolve<OffersData, HookContext>({})

// Schema for updating existing data
export const offersPatchSchema = {
  $id: 'OffersPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...offersSchema.properties,
    ...commonPatch(offersSchema.properties).properties
  }
} as const
export type OffersPatch = FromSchema<typeof offersPatchSchema>
export const offersPatchValidator = getValidator(offersPatchSchema, dataValidator)
export const offersPatchResolver = resolve<OffersPatch, HookContext>({})

// Schema for allowed query properties
export const offersQuerySchema = {
  $id: 'OffersQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(offersSchema.properties)
  }
} as const
export type OffersQuery = FromSchema<typeof offersQuerySchema>
export const offersQueryValidator = getValidator(offersQuerySchema, queryValidator)
export const offersQueryResolver = resolve<OffersQuery, HookContext>({})
