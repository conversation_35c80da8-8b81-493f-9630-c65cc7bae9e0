// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch, commonQueries} from '../../utils/common/schemas.js';

export const sectionsSchema = {
  type: 'object',
  patternProperties: {
    "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {
      type: 'object',
      properties: {
        key: { type: 'string' },
        title: { type: 'string' },
        sections: {
          type: 'object',
          patternProperties: {
            "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {
              type: 'object',
              properties: {
                key: { type: 'string' },
                title: {type: 'string'},
                body: {type: 'string'}
              }
            }
          }
        }
      }
    }
  }
} as const

export const planClass = { type: 'string', enum: ['core', '125', '105', 'misc', 'spd']} as const
// Main data model schema
export const planDocsSchema = {
  $id: 'PlanDocs',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'plan', 'name'],
  properties: {
    _id: ObjectIdSchema(),
    plan: ObjectIdSchema(),
    smb: { type: 'boolean' },
    public: { type: 'boolean' },
    printCount: { type: 'number' },
    name: { type: 'string' },
    description: { type: 'string' },
    class: planClass,
    subClass: { type: 'string'},
    path: { type: 'string' },
    sectionsUpdatedAt: {},
    template: ObjectIdSchema(),
    sections: sectionsSchema,
    ...commonFields.properties
  }
} as const
export type PlanDocs = FromSchema<typeof planDocsSchema>
export const planDocsValidator = getValidator(planDocsSchema, dataValidator)
export const planDocsResolver = resolve<PlanDocs, HookContext>({})

export const planDocsExternalResolver = resolve<PlanDocs, HookContext>({})

// Schema for creating new data
export const planDocsDataSchema = {
  $id: 'PlanDocsData',
  type: 'object',
  additionalProperties: false,
  required: ['plan'],
  properties: {
    ...planDocsSchema.properties
  }
} as const
export type PlanDocsData = FromSchema<typeof planDocsDataSchema>
export const planDocsDataValidator = getValidator(planDocsDataSchema, dataValidator)
export const planDocsDataResolver = resolve<PlanDocsData, HookContext>({})

// Schema for updating existing data
export const planDocsPatchSchema = {
  $id: 'PlanDocsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...planDocsSchema.properties,
    ...commonPatch(planDocsSchema.properties).properties
  }
} as const
export type PlanDocsPatch = FromSchema<typeof planDocsPatchSchema>
export const planDocsPatchValidator = getValidator(planDocsPatchSchema, dataValidator)
export const planDocsPatchResolver = resolve<PlanDocsPatch, HookContext>({})

// Schema for allowed query properties
export const planDocsQuerySchema = {
  $id: 'PlanDocsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax({...planDocsSchema.properties, ...commonQueries.properties, name: {}, active: {}}),
  }
} as const
export type PlanDocsQuery = FromSchema<typeof planDocsQuerySchema>
export const planDocsQueryValidator = getValidator(planDocsQuerySchema, queryValidator)
export const planDocsQueryResolver = resolve<PlanDocsQuery, HookContext>({})
