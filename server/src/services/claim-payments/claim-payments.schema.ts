// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema, pull} from '../../utils/common/schemas.js';

const refund = {
    type: 'object',
    required: ['amount', 'confirmation'],
    properties: {
        ded: {type: 'number'},
        coins: {type: 'number'},
        copay: { type: 'number' },
        amount: {type: 'number'},
        refundedAt: {},
        status: {type: 'string', enum: ['pending', 'complete', 'cancelled']},
        confirmation: {type: 'string'}
    }
} as const
// Main data model schema
export const claimPaymentsSchema = {
    $id: 'ClaimPayments',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'amount', 'claim', 'org', 'person', 'plan', 'patient', 'provider'],
    properties: {
        _id: ObjectIdSchema(),
        claim: ObjectIdSchema(),
        visit: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        person: ObjectIdSchema(),
        org: ObjectIdSchema(),
        patient: ObjectIdSchema(),
        provider: ObjectIdSchema(),
        coverage: ObjectIdSchema(),
        enrollment: ObjectIdSchema(),
        preventive: { type: 'boolean' },

        allowedMethods: {type: 'array', items: {type: 'string', enum: ['card', 'ach', 'ca']}},
        method: {type: 'string', enum: ['card', 'ach', 'ca', 'ext']},
        checkoutSession: {type: 'string'},
        customerId: {type: 'string'},
        payment_method: {type: 'string'},
        transactionId: {type: 'string'},
        providerCareAccount: ObjectIdSchema(),
        careAccount: ObjectIdSchema(),
        card: ObjectIdSchema(),
        budget: ObjectIdSchema(),

        amount: {type: 'number'},
        ded: {type: 'number'},
        copay: {type: 'number'},
        coins: {type: 'number'},
        due: {type: 'string'},
        refunds: {type: 'array', items: refund},
        type: {type: 'string'},
        confirmation: {type: 'string'},
        confirmedAt: {},
        fullPayment: {type: 'boolean'},
        status: {type: 'string', enum: ['request', 'offer', 'pending', 'paid', 'cancelled', 'refunded', 'returned']},
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },

        ...commonFields.properties
    }
} as const
export type ClaimPayments = FromSchema<typeof claimPaymentsSchema>
export const claimPaymentsValidator = getValidator(claimPaymentsSchema, dataValidator)
export const claimPaymentsResolver = resolve<ClaimPayments, HookContext>({
    status: async (val) => {
        if (val === 'paid') return 'pending'
        return val;
    }
})

export const claimPaymentsExternalResolver = resolve<ClaimPayments, HookContext>({})

// Schema for creating new data
export const claimPaymentsDataSchema = {
    $id: 'ClaimPaymentsData',
    type: 'object',
    additionalProperties: false,
    required: ['claim', 'amount', 'person', 'org', 'plan', 'patient', 'provider'],
    properties: {
        ...claimPaymentsSchema.properties
    }
} as const
export type ClaimPaymentsData = FromSchema<typeof claimPaymentsDataSchema>
export const claimPaymentsDataValidator = getValidator(claimPaymentsDataSchema, dataValidator)
export const claimPaymentsDataResolver = resolve<ClaimPaymentsData, HookContext>({})

const pushPull = [
    {path: 'allowedMethods', type: {type: 'string', enum: ['card', 'ach', 'ca']}},
    {path: 'refunds', type: refund}
]
// Schema for updating existing data
export const claimPaymentsPatchSchema = {
    $id: 'ClaimPaymentsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...claimPaymentsSchema.properties,
        ...commonPatch(claimPaymentsSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type ClaimPaymentsPatch = FromSchema<typeof claimPaymentsPatchSchema>
export const claimPaymentsPatchValidator = getValidator(claimPaymentsPatchSchema, dataValidator)
export const claimPaymentsPatchResolver = resolve<ClaimPaymentsPatch, HookContext>({})

// Schema for allowed query properties
export const claimPaymentsQuerySchema = {
    $id: 'ClaimPaymentsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(claimPaymentsSchema.properties)
    }
} as const
export type ClaimPaymentsQuery = FromSchema<typeof claimPaymentsQuerySchema>
export const claimPaymentsQueryValidator = getValidator(claimPaymentsQuerySchema, queryValidator)
export const claimPaymentsQueryResolver = resolve<ClaimPaymentsQuery, HookContext>({})
