// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';
import {premiumSchema} from '../coverages/coverages.schema.js';
// Main data model schema
export const ratesSchema = {
    $id: 'Rates',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'coverage', 'state'],
    properties: {
        _id: ObjectIdSchema(),
        coverage: ObjectIdSchema(),
        state: {type: 'string'},
        stateKey: {type: 'string'},
        areas: {
            type: 'array', items: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    zips: { type: 'array', items: { type: 'string'} },
                    premium: premiumSchema
                }
            }
        },
        premium: premiumSchema,
        ...commonFields.properties
    }
} as const
export type Rates = FromSchema<typeof ratesSchema>
export const ratesValidator = getValidator(ratesSchema, dataValidator)
export const ratesResolver = resolve<Rates, HookContext>({
    properties: {
        state: async (val) => {
            return val?.toUpperCase();
        },
        stateKey: async (val, data) => {
            return `${data.coverage}:${data.state}`
        }
    }
})

export const ratesExternalResolver = resolve<Rates, HookContext>({})

// Schema for creating new data
export const ratesDataSchema = {
    $id: 'RatesData',
    type: 'object',
    additionalProperties: false,
    required: ['coverage'],
    properties: {
        ...ratesSchema.properties
    }
} as const
export type RatesData = FromSchema<typeof ratesDataSchema>
export const ratesDataValidator = getValidator(ratesDataSchema, dataValidator)
export const ratesDataResolver = resolve<RatesData, HookContext>({})

// Schema for updating existing data
export const ratesPatchSchema = {
    $id: 'RatesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...ratesSchema.properties,
        ...commonPatch(ratesSchema.properties).properties
    }
} as const
export type RatesPatch = FromSchema<typeof ratesPatchSchema>
export const ratesPatchValidator = getValidator(ratesPatchSchema, dataValidator)
export const ratesPatchResolver = resolve<RatesPatch, HookContext>({})

// Schema for allowed query properties
export const ratesQuerySchema = {
    $id: 'RatesQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(ratesSchema.properties)
    }
} as const
export type RatesQuery = FromSchema<typeof ratesQuerySchema>
export const ratesQueryValidator = getValidator(ratesQuerySchema, queryValidator)
export const ratesQueryResolver = resolve<RatesQuery, HookContext>({})
