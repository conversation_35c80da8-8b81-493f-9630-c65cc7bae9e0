// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'

import {
  commonFields,
  commonPatch,
  commonQueries,
  exists,
  geoJsonSchema,
  imageSchema
} from '../../utils/common/schemas.js'

export const compsCamsSchema = {
  type: 'object',
  properties: {
    org: ObjectIdSchema(),
    contract: ObjectIdSchema(),
    interval: { type: 'string', enum: ['hour', 'day', 'week', 'month', 'quarter', 'year', 'once'] },
    estHours: { type: 'number' },
    amount: { type: 'number' },
    terms: { type: 'string' },
    name: { type: 'string' },
    class: { type: 'string', enum: ['ee', 'ic'] },
    extras: {
      type: 'object',
      patternProperties: {
        '^.*$': {
          type: 'object',
          properties: {
            due: { type: 'string' },
            awarded: { type: 'string' },
            off: { type: 'boolean' },
            banks: { type: 'boolean' },
            type: { type: 'string', enum: ['percent', 'flat', 'units'] }, //percent, flat, units
            unit: { type: 'string', enum: ['hour', 'day', 'week', 'month', 'quarter', 'year', 'once'] },
            amount: { type: 'number' },
            interval: { type: 'string', enum: ['hour', 'day', 'week', 'month', 'quarter', 'year', 'once'] },
            terms: { type: 'string' },
            limit: { type: 'number' }
          }
        }
      }
    }
  }
} as const

export const setDefEstUnits = async (val) => {
  if (!val && val !== 0) return 40
  return val
}
// Main data model schema
export const compsSchema = {
  $id: 'Comps',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'amount', 'interval', 'org', 'name', 'key'],
  properties: {
    _id: ObjectIdSchema(),
    key: { type: 'string' },
    video: imageSchema,
    references: { type: 'array', items: ObjectIdSchema() },
    ad: { type: 'string' },
    geo: geoJsonSchema,
    stages: {
      type: 'object',
      patternProperties: {
        '^.*$': {
          type: 'object',
          properties: {
            label: { type: 'string' },
            color: { type: 'string' }
          }
        }
      }
    },
    contact: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        phone: { type: 'string' },
        email: { type: 'string' }
      }
    },
    access: { type: 'string', enum: ['public', 'private'] },
    ...compsCamsSchema.properties,
    ...commonFields.properties
  }
} as const
export type Comps = FromSchema<typeof compsSchema>
export const compsValidator = getValidator(compsSchema, dataValidator)
export const compsResolver = resolve<Comps, HookContext>({
  properties: {
    estHours: setDefEstUnits
  }
})

export const compsExternalResolver = resolve<Comps, HookContext>({})

// Schema for creating new data
export const compsDataSchema = {
  $id: 'CompsData',
  type: 'object',
  additionalProperties: false,
  required: ['amount', 'interval'],
  properties: {
    ...compsSchema.properties
  }
} as const
export type CompsData = FromSchema<typeof compsDataSchema>
export const compsDataValidator = getValidator(compsDataSchema, dataValidator)
export const compsDataResolver = resolve<CompsData, HookContext>({
  class: async (val) => {
    if (!val) return 'ee'
    return val
  }
})

// Schema for updating existing data
export const compsPatchSchema = {
  $id: 'CompsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...compsSchema.properties,
    ...commonPatch(compsSchema.properties).properties
  }
} as const
export type CompsPatch = FromSchema<typeof compsPatchSchema>
export const compsPatchValidator = getValidator(compsPatchSchema, dataValidator)
export const compsPatchResolver = resolve<CompsPatch, HookContext>({})

// Schema for allowed query properties
export const compsQuerySchema = {
  $id: 'CompsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax({
      ...compsSchema.properties,
      ...commonQueries.properties,
      name: {},
      ...exists(['extras'], compsSchema.properties)
    })
  }
} as const
export type CompsQuery = FromSchema<typeof compsQuerySchema>
export const compsQueryValidator = getValidator(compsQuerySchema, queryValidator)
export const compsQueryResolver = resolve<CompsQuery, HookContext>({})
