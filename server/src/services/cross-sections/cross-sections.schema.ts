// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {addToSet, commonFields, commonPatch, pull} from '../../utils/common/schemas.js';

const sections = {
  type: 'object',
  patternProperties: {
    "^.*$": {type: 'array', items: ObjectIdSchema()}
  }
} as const;
// Main data model schema
export const crossSectionsSchema = {
  $id: 'CrossSections',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'hackId', 'subject', 'sections' ],
  properties: {
    _id: ObjectIdSchema(),
    hackId: { type: 'string' },//name:service:id
    subject: ObjectIdSchema(),
    sections: sections,
    ...commonFields.properties
  }
} as const
export type CrossSections = FromSchema<typeof crossSectionsSchema>
export const crossSectionsValidator = getValidator(crossSectionsSchema, dataValidator)
export const crossSectionsResolver = resolve<CrossSections, HookContext>({
  subject: async (val, data) => {
    if(!val) return data.hackId.split(':')[2]
    return val;
  }
})

export const crossSectionsExternalResolver = resolve<CrossSections, HookContext>({})

// Schema for creating new data
export const crossSectionsDataSchema = {
  $id: 'CrossSectionsData',
  type: 'object',
  additionalProperties: false,
  required: ['hackId', 'subject', 'sections'],
  properties: {
    ...crossSectionsSchema.properties
  }
} as const
export type CrossSectionsData = FromSchema<typeof crossSectionsDataSchema>
export const crossSectionsDataValidator = getValidator(crossSectionsDataSchema, dataValidator)
export const crossSectionsDataResolver = resolve<CrossSectionsData, HookContext>({})

// Schema for updating existing data
export const crossSectionsPatchSchema = {
  $id: 'CrossSectionsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...crossSectionsSchema.properties,
    ...commonPatch(crossSectionsSchema.properties).properties,
    $addToSet: addToSet([{ path: 'sections', type: sections }] ),
    $pull: pull([{path: 'sections', type: sections}])

  }
} as const
export type CrossSectionsPatch = FromSchema<typeof crossSectionsPatchSchema>
export const crossSectionsPatchValidator = getValidator(crossSectionsPatchSchema, dataValidator)
export const crossSectionsPatchResolver = resolve<CrossSectionsPatch, HookContext>({})

// Schema for allowed query properties
export const crossSectionsQuerySchema = {
  $id: 'CrossSectionsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(crossSectionsSchema.properties)
  }
} as const
export type CrossSectionsQuery = FromSchema<typeof crossSectionsQuerySchema>
export const crossSectionsQueryValidator = getValidator(crossSectionsQuerySchema, queryValidator)
export const crossSectionsQueryResolver = resolve<CrossSectionsQuery, HookContext>({})
