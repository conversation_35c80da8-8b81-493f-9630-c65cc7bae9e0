// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addressSchema,
    commonFields,
    commonPatch,
    emailHandler,
    imageSchema,
    phoneSchema
} from '../../utils/common/schemas.js';

// Main data model schema
export const practitionersSchema = {
    $id: 'Practitioners',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'firstName', 'lastName'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        avatar: imageSchema,
        firstName: {type: 'string'},
        lastName: {type: 'string'},
        name: {type: 'string'},
        name_prefix: {type: 'string'},
        gender: {type: 'string'},
        credential: {type: 'string'},
        auto_created: { type: 'boolean' },
        credentials: {type: 'array', items: {type: 'string'}},
        phone: phoneSchema,
        soleProp: { type: 'string' },
        phones: { type: 'array', items: phoneSchema },
        email: {type: 'string'},
        npi_date: {},
        npi_update: {},
        npi_status: { type: 'string' },
        npi: {type: 'string'},
        license: {type: 'string'},
        licenses: { type: 'array', items: { type: 'object', additionalProperties: true, properties: { state: { type: 'string' }}}},
        cities: {type: 'array', items: {type: 'object', properties: { city: { type: 'string'}, state: { type: 'string' }}}},
        license_states: {type: 'array', items: {type: 'string'}},
        taxonomy1: {type: 'string'}, //physician, non-physician
        taxonomy2: {type: 'string'}, //class - Dentist, Chripractor,
        taxonomy3: {type: 'string'}, //specialization
        providers: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        id: ObjectIdSchema()
                    }
                }
            }
        },
        ...commonFields.properties
    }

} as const
export type Practitioners = FromSchema<typeof practitionersSchema>
export const practitionersValidator = getValidator(practitionersSchema, dataValidator)
export const practitionersResolver = resolve<Practitioners, HookContext>({
    name: async (val, data) => {
        return data.firstName + ' ' + data.lastName
    },
    email: emailHandler({throw: false}),
    gender: async (val) => {
        if (val === 'M') return 'male'
        if (val === 'F') return 'female'
        return val;
    }
})

export const practitionersExternalResolver = resolve<Practitioners, HookContext>({})

// Schema for creating new data
export const practitionersDataSchema = {
    $id: 'PractitionersData',
    type: 'object',
    additionalProperties: false,
    required: ['firstName', 'lastName'],
    properties: {
        ...practitionersSchema.properties
    }
} as const
export type PractitionersData = FromSchema<typeof practitionersDataSchema>
export const practitionersDataValidator = getValidator(practitionersDataSchema, dataValidator)
export const practitionersDataResolver = resolve<PractitionersData, HookContext>({
    npi: async (val, data) => {
        if(!val) return `*_${data.lastName}_${new Date().getTime()}`
    }
})

// Schema for updating existing data
export const practitionersPatchSchema = {
    $id: 'PractitionersPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...practitionersSchema.properties,
        ...commonPatch(practitionersSchema.properties).properties
    }
} as const
export type PractitionersPatch = FromSchema<typeof practitionersPatchSchema>
export const practitionersPatchValidator = getValidator(practitionersPatchSchema, dataValidator)
export const practitionersPatchResolver = resolve<PractitionersPatch, HookContext>({})

// Schema for allowed query properties
export const practitionersQuerySchema = {
    $id: 'PractitionersQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({
            ...practitionersSchema.properties,
            name: {},
            firstName: {},
            lastName: {}
        }),

    }
} as const
export type PractitionersQuery = FromSchema<typeof practitionersQuerySchema>
export const practitionersQueryValidator = getValidator(practitionersQuerySchema, queryValidator)
export const practitionersQueryResolver = resolve<PractitionersQuery, HookContext>({})
