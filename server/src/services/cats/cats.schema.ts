// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {imageSchema, commonFields, addToSet, pull, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const catsSchema = {
  $id: 'Cats',
  type: 'object',
  additionalProperties: true,
  required: ['_id', 'name'],
  properties: {
    _id: ObjectIdSchema(),
    avatar: imageSchema,
    org: ObjectIdSchema(),
    managers: { type: 'array', items: ObjectIdSchema() },
    writers: { type: 'array', items: ObjectIdSchema() },
    images: { type: 'array', items: imageSchema },
    name: { type: 'string' },
    description: { type: 'string' },
    code_regex: { type: 'string' },
    conditions: { type: 'array', items: ObjectIdSchema() },
    procedures: { type: 'array', items: ObjectIdSchema() },
    meds: { type: 'array', items: ObjectIdSchema() },
    ...commonFields.properties
  }
} as const
export type Cats = FromSchema<typeof catsSchema>
export const catsValidator = getValidator(catsSchema, dataValidator)
export const catsResolver = resolve<Cats, HookContext>({})

export const catsExternalResolver = resolve<Cats, HookContext>({})

// Schema for creating new data
export const catsDataSchema = {
  $id: 'CatsData',
  type: 'object',
  additionalProperties: true,
  required: ['name'],
  properties: {
    ...catsSchema.properties
  }
} as const
export type CatsData = FromSchema<typeof catsDataSchema>
export const catsDataValidator = getValidator(catsDataSchema, dataValidator)
export const catsDataResolver = resolve<CatsData, HookContext>({})

const pushPull = [
  {path: 'procedures', type: ObjectIdSchema()},
  {path: 'meds', type: ObjectIdSchema()},
  {path: 'conditions', type: ObjectIdSchema()},
  {path: 'managers', type: ObjectIdSchema()},
  {path: 'writers', type: ObjectIdSchema()},
]
// Schema for updating existing data
export const catsPatchSchema = {
  $id: 'CatsPatch',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...catsSchema.properties,
    ...commonPatch(catsSchema.properties).properties,
    $addToSet: addToSet(pushPull),
    $pull: pull(pushPull)
  }
} as const
export type CatsPatch = FromSchema<typeof catsPatchSchema>
export const catsPatchValidator = getValidator(catsPatchSchema, dataValidator)
export const catsPatchResolver = resolve<CatsPatch, HookContext>({})

// Schema for allowed query properties
export const catsQuerySchema = {
  $id: 'CatsQuery',
  type: 'object',
  additionalProperties: true,
  properties: {
    ...querySyntax(catsSchema.properties),
    name: {}
  }
} as const
export type CatsQuery = FromSchema<typeof catsQuerySchema>
export const catsQueryValidator = getValidator(catsQuerySchema, queryValidator)
export const catsQueryResolver = resolve<CatsQuery, HookContext>({})
