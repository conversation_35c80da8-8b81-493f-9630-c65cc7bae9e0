// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, pull} from '../../utils/common/schemas.js';

export const lastSync = {
    type: 'object', properties: {
        adjusted: {type: 'boolean'},
        balance: {type: 'number'},
        date: {},
        err: {type: 'string'},
        amount: { type: 'number' },
        recurs: { type: 'number' },
        adjust_spent: { type: 'number' },
        adjust_spent_pending: { type: 'number' },
        adjust_amount: {type: 'number'},
        adjust_assigned_amount: {type: 'number'},
        adjust_recurs: {type: 'number'},
        adjust_assigned_recurs: {type: 'number'},
        freeze: {type: 'boolean'},
        excess: { type: 'number' },
        by: ObjectIdSchema()
    }
} as const
// Main data model schema
export const careAccountsSchema = {
    $id: 'CareAccounts',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'owner'],
    properties: {
        _id: ObjectIdSchema(),
        amount: {type: 'number'},
        approvers: {type: 'array', items: ObjectIdSchema()},
        assigned_amount: {type: 'number'},
        assigned_recurs: {type: 'number'},
        budgets: {type: 'array', items: ObjectIdSchema()},
        connect_id: {type: 'string'}, //deprecated
        stripe_id: {type: 'string'}, //deprecated
        moov_id: {type: 'string'},//moov accountID - treasury.id in orgs
        wallet_id: {type: 'string'},
        last4: {type: 'string'},
        lastInc: { type: 'string' },
        lastSync,
        managers: {type: 'array', items: ObjectIdSchema()},
        members: {type: 'array', items: ObjectIdSchema()},
        ramp_whitelist: {type: 'array', items: {type: 'string'}},
        mcc_whitelist: {type: 'array', items: { type: 'string'}},
        mcc_blacklist: {type: 'array', items: { type: 'string'}},
        name: {type: 'string'},
        owner: ObjectIdSchema(),
        recurs: {type: 'number'},
        runSync: { type: 'string' },
        status: { type: 'string' },
        statusNote: { type: 'string' },
        syncHistory: {type: 'array', items: lastSync},
        ...commonFields.properties
    }
} as const
export type CareAccounts = FromSchema<typeof careAccountsSchema>
export const careAccountsValidator = getValidator(careAccountsSchema, dataValidator)
export const careAccountsResolver = resolve<CareAccounts, HookContext>({})

export const careAccountsExternalResolver = resolve<CareAccounts, HookContext>({})

const {assigned_amount, assigned_recurs, ...createPatchSchema} = careAccountsSchema.properties
// Schema for creating new data
export const careAccountsDataSchema = {
    $id: 'CareAccountsData',
    type: 'object',
    additionalProperties: false,
    required: ['owner'],
    properties: {
        ...createPatchSchema
    }
} as const
export type CareAccountsData = FromSchema<typeof careAccountsDataSchema>
export const careAccountsDataValidator = getValidator(careAccountsDataSchema, dataValidator)
export const careAccountsDataResolver = resolve<CareAccountsData, HookContext>({})

const listArgs = [
    {path: 'syncHistory', type: lastSync},
    {path: 'mcc_whitelist', type: { type: 'string' }},
    {path: 'mcc_blacklist', type: { type: 'string' }},
    {path: 'budgets', type: ObjectIdSchema()}
]

// Schema for updating existing data
export const careAccountsPatchSchema = {
    $id: 'CareAccountsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...createPatchSchema,
        ...commonPatch(careAccountsSchema.properties, [{path: 'syncHistory', type: lastSync }]).properties,
        $inc: {},
        $addToSet: addToSet(listArgs),
        $pull: pull(listArgs)
    }
} as const
export type CareAccountsPatch = FromSchema<typeof careAccountsPatchSchema>
export const careAccountsPatchValidator = getValidator(careAccountsPatchSchema, dataValidator)
export const careAccountsPatchResolver = resolve<CareAccountsPatch, HookContext>({
    owner: async (val) => {
        return undefined
    }
})

// Schema for allowed query properties
export const careAccountsQuerySchema = {
    $id: 'CareAccountsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(careAccountsSchema.properties),
        name: {}
    }
} as const
export type CareAccountsQuery = FromSchema<typeof careAccountsQuerySchema>
export const careAccountsQueryValidator = getValidator(careAccountsQuerySchema, queryValidator)
export const careAccountsQueryResolver = resolve<CareAccountsQuery, HookContext>({})
