// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const changeLogsSchema = {
  $id: 'ChangeLogs',
  type: 'object',
  additionalProperties: true,
  required: ['_id', 'recordId', 'service'],
  properties: {
    _id: ObjectIdSchema(),
    service: {type: 'string'},
    recordId: ObjectIdSchema(),
    //[key:string]: {
    //   history: [{
    //         data: oldVal,
    //         updatedAt: Date,
    //         updatedBy: {
    //                 login: id
    //             }
    //   }]
  // }
    ...commonFields.properties
  }
} as const
export type ChangeLogs = FromSchema<typeof changeLogsSchema>
export const changeLogsValidator = getValidator(changeLogsSchema, dataValidator)
export const changeLogsResolver = resolve<ChangeLogs, HookContext>({})

export const changeLogsExternalResolver = resolve<ChangeLogs, HookContext>({})

// Schema for creating new data
export const changeLogsDataSchema = {
  $id: 'ChangeLogsData',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...changeLogsSchema.properties
  }
} as const
export type ChangeLogsData = FromSchema<typeof changeLogsDataSchema>
export const changeLogsDataValidator = getValidator(changeLogsDataSchema, dataValidator)
export const changeLogsDataResolver = resolve<ChangeLogsData, HookContext>({})

// Schema for updating existing data
export const changeLogsPatchSchema = {
  $id: 'ChangeLogsPatch',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...changeLogsSchema.properties,
    ...commonPatch(changeLogsSchema.properties).properties
  }
} as const
export type ChangeLogsPatch = FromSchema<typeof changeLogsPatchSchema>
export const changeLogsPatchValidator = getValidator(changeLogsPatchSchema, dataValidator)
export const changeLogsPatchResolver = resolve<ChangeLogsPatch, HookContext>({})

// Schema for allowed query properties
export const changeLogsQuerySchema = {
  $id: 'ChangeLogsQuery',
  type: 'object',
  additionalProperties: true,
  properties: {
    ...querySyntax(changeLogsSchema.properties)
  }
} as const
export type ChangeLogsQuery = FromSchema<typeof changeLogsQuerySchema>
export const changeLogsQueryValidator = getValidator(changeLogsQuerySchema, queryValidator)
export const changeLogsQueryResolver = resolve<ChangeLogsQuery, HookContext>({})
