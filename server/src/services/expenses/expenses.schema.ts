// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {addToSet, commonFields, commonPatch, commonQueries, pull} from '../../utils/common/schemas.js';

const closedLimitSchema = {
  type: 'object', properties: {
    id: {type: 'string'},
    closedAt: {},
    memo: {type: 'string'}
  }
} as const
// Main data model schema
export const expensesSchema = {
  $id: 'Expenses',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'name', 'owner', 'budget', 'limit_owner'],
  properties: {
    _id: ObjectIdSchema(),
    amount: {type: 'number', $comment: 'spend limit'},
    max_transaction: {type: 'number', $comment: 'max transaction limit'},
    recurs: {type: 'number', $comment: 'monthly limit'},
    recur_start: { type: 'string' },
    next_reset: { type: 'string' },
    lock_date: { type: 'string' },
    spent: {type: 'number', $comment: 'amount spent'},
    spent_pending: {type: 'number', $comment: 'amount pending - moves to zero when spent confirmed'},
    singleUse: {type: 'boolean'},
    singleVendor: { type: 'boolean' },
    perTransactionLimit: {type: 'number'},
    approvers: {type: 'array', items: ObjectIdSchema()},
    budget: ObjectIdSchema(),
    limit_owner: ObjectIdSchema(), //Ppls id for limit_owner
    closedLimitIds: {type: 'array', items: {type: 'string'}},
    closedLimits: {
      type: 'array', items: closedLimitSchema
    },
    last4: {type: 'string'},
    lastSync: {},
    ramp_limit: { type: 'string' },
    managers: {type: 'array', items: ObjectIdSchema()},
    ramp_whitelist: {type: 'array', items: {type: 'number'}},
    ramp_blacklist: {type: 'array', items: {type: 'number'}},
    vendor_whitelist: { type: 'array', items: ObjectIdSchema(), $comment: 'orgs that a limit is limited to - must have a ramp_vendor_id' },
    vendor_blacklist: { type: 'array', items: ObjectIdSchema(), $comment: 'orgs that a limit is restricted for - must have a ramp_vendor_id' },
    mcc_whitelist: {type: 'array', items: {type: 'string'}},
    mcc_blacklist: {type: 'array', items: {type: 'string'}},
    members: {type: 'array', items: ObjectIdSchema()},
    name: {type: 'string'},
    owner: ObjectIdSchema(),
    preAuth: {type: 'number'},
    status: {type: 'string', enum: ['active', 'inactive', 'canceled', 'pending-verification']},
    users: {type: 'array', items: ObjectIdSchema()},
    ...commonFields.properties
  }
} as const
export type Expenses = FromSchema<typeof expensesSchema>
export const expensesValidator = getValidator(expensesSchema, dataValidator)
export const expensesResolver = resolve<Expenses, HookContext>({})

export const expensesExternalResolver = resolve<Expenses, HookContext>({})

const {amount, recurs, ...createPatchSchema} = expensesSchema.properties;

// Schema for creating new data
export const expensesDataSchema = {
  $id: 'ExpensesData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...createPatchSchema
  }
} as const
export type ExpensesData = FromSchema<typeof expensesDataSchema>
export const expensesDataValidator = getValidator(expensesDataSchema, dataValidator)
export const expensesDataResolver = resolve<ExpensesData, HookContext>({
  // members: async (val, data) => {
  //   return Array.from(new Set([...val || [], data.limit_owner]))
  // },
  status: async (val) => {
    if (val) return val;
    return 'active'
  }
})

const listArgs = [
  {path: 'mcc_whitelist', type: {type: 'string'}},
  {path: 'mcc_blacklist', type: {type: 'string'}},
  {path: 'closedLimits', type: closedLimitSchema},
  {path: 'closedLimitIds', type: {type: 'string'}}
]
// Schema for updating existing data
export const expensesPatchSchema = {
  $id: 'ExpensesPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...createPatchSchema,
    ...commonPatch(createPatchSchema).properties,
    $inc: {},
    $addToSet: addToSet(listArgs),
    $pull: pull(listArgs)
  }
} as const
export type ExpensesPatch = FromSchema<typeof expensesPatchSchema>
export const expensesPatchValidator = getValidator(expensesPatchSchema, dataValidator)
export const expensesPatchResolver = resolve<ExpensesPatch, HookContext>({})

// Schema for allowed query properties
export const expensesQuerySchema = {
  $id: 'ExpensesQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax({...expensesSchema.properties, ...commonQueries.properties}),
    name: {}
  }
} as const
export type ExpensesQuery = FromSchema<typeof expensesQuerySchema>
export const expensesQueryValidator = getValidator(expensesQuerySchema, queryValidator)
export const expensesQueryResolver = resolve<ExpensesQuery, HookContext>({})
