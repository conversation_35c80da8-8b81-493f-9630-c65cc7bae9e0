// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields} from '../../utils/common/schemas.js';

// Main data model schema
export const grpMbrsSchema = {
  $id: 'GrpMbrs',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'group', 'person', 'org', 'mbrId'],
  properties: {
    _id: ObjectIdSchema(),
    group: ObjectIdSchema(),
    person: ObjectIdSchema(),
    org: ObjectIdSchema(),
    mbrId: {type: 'string'},
    name: {type: 'string'},
    email: {type: 'string'},
    ...commonFields.properties
  }
} as const
export type GrpMbrs = FromSchema<typeof grpMbrsSchema>
export const grpMbrsValidator = getValidator(grpMbrsSchema, dataValidator)
export const grpMbrsResolver = resolve<GrpMbrs, HookContext>({})

export const grpMbrsExternalResolver = resolve<GrpMbrs, HookContext>({})

// Schema for creating new data
export const grpMbrsDataSchema = {
  $id: 'GrpMbrsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...grpMbrsSchema.properties
  }
} as const
export type GrpMbrsData = FromSchema<typeof grpMbrsDataSchema>
export const grpMbrsDataValidator = getValidator(grpMbrsDataSchema, dataValidator)
export const grpMbrsDataResolver = resolve<GrpMbrsData, HookContext>({})

// Schema for updating existing data
export const grpMbrsPatchSchema = {
  $id: 'GrpMbrsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...grpMbrsSchema.properties
  }
} as const
export type GrpMbrsPatch = FromSchema<typeof grpMbrsPatchSchema>
export const grpMbrsPatchValidator = getValidator(grpMbrsPatchSchema, dataValidator)
export const grpMbrsPatchResolver = resolve<GrpMbrsPatch, HookContext>({})

// Schema for allowed query properties
export const grpMbrsQuerySchema = {
  $id: 'GrpMbrsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(grpMbrsSchema.properties),
    name: {},
    email: {}
  }
} as const
export type GrpMbrsQuery = FromSchema<typeof grpMbrsQuerySchema>
export const grpMbrsQueryValidator = getValidator(grpMbrsQuerySchema, queryValidator)
export const grpMbrsQueryResolver = resolve<GrpMbrsQuery, HookContext>({})
