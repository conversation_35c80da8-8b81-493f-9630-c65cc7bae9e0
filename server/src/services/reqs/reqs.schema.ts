// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonQueries, commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const reqsSchema = {
  $id: 'Reqs',
  type: 'object',
  additionalProperties: true,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),

    fingerprint: { type: 'string' },
    refName: { type: 'string' },
    ...commonFields.properties
  }
} as const
export type Reqs = FromSchema<typeof reqsSchema>
export const reqsValidator = getValidator(reqsSchema, dataValidator)
export const reqsResolver = resolve<Reqs, HookContext>({})

export const reqsExternalResolver = resolve<Reqs, HookContext>({})

// Schema for creating new data
export const reqsDataSchema = {
  $id: 'ReqsData',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...reqsSchema.properties
  }
} as const
export type ReqsData = FromSchema<typeof reqsDataSchema>
export const reqsDataValidator = getValidator(reqsDataSchema, dataValidator)
export const reqsDataResolver = resolve<ReqsData, HookContext>({})

// Schema for updating existing data
export const reqsPatchSchema = {
  $id: 'ReqsPatch',
  type: 'object',
  additionalProperties: true,
  required: [],
  properties: {
    ...reqsSchema.properties,
    ...commonPatch(reqsSchema.properties).properties
  }
} as const
export type ReqsPatch = FromSchema<typeof reqsPatchSchema>
export const reqsPatchValidator = getValidator(reqsPatchSchema, dataValidator)
export const reqsPatchResolver = resolve<ReqsPatch, HookContext>({})

// Schema for allowed query properties
export const reqsQuerySchema = {
  $id: 'ReqsQuery',
  type: 'object',
  additionalProperties: true,
  properties: {
    ...querySyntax({...reqsSchema.properties, ...commonQueries.properties})
  }
} as const
export type ReqsQuery = FromSchema<typeof reqsQuerySchema>
export const reqsQueryValidator = getValidator(reqsQuerySchema, queryValidator)
export const reqsQueryResolver = resolve<ReqsQuery, HookContext>({})
