// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const fundsRequestsSchema = {
  $id: 'FundsRequests',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    ...commonFields.properties
  }
} as const
export type FundsRequests = FromSchema<typeof fundsRequestsSchema>
export const fundsRequestsValidator = getValidator(fundsRequestsSchema, dataValidator)
export const fundsRequestsResolver = resolve<FundsRequests, HookContext>({})

export const fundsRequestsExternalResolver = resolve<FundsRequests, HookContext>({})

// Schema for creating new data
export const fundsRequestsDataSchema = {
  $id: 'FundsRequestsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...fundsRequestsSchema.properties
  }
} as const
export type FundsRequestsData = FromSchema<typeof fundsRequestsDataSchema>
export const fundsRequestsDataValidator = getValidator(fundsRequestsDataSchema, dataValidator)
export const fundsRequestsDataResolver = resolve<FundsRequestsData, HookContext>({})

// Schema for updating existing data
export const fundsRequestsPatchSchema = {
  $id: 'FundsRequestsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...fundsRequestsSchema.properties,
    ...commonPatch(fundsRequestsSchema.properties).properties
  }
} as const
export type FundsRequestsPatch = FromSchema<typeof fundsRequestsPatchSchema>
export const fundsRequestsPatchValidator = getValidator(fundsRequestsPatchSchema, dataValidator)
export const fundsRequestsPatchResolver = resolve<FundsRequestsPatch, HookContext>({})

// Schema for allowed query properties
export const fundsRequestsQuerySchema = {
  $id: 'FundsRequestsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(fundsRequestsSchema.properties)
  }
} as const
export type FundsRequestsQuery = FromSchema<typeof fundsRequestsQuerySchema>
export const fundsRequestsQueryValidator = getValidator(fundsRequestsQuerySchema, queryValidator)
export const fundsRequestsQueryResolver = resolve<FundsRequestsQuery, HookContext>({})
