// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

export const challengeKinds = ['registration', 'authentication'] as const;
const base64urlPattern = '^[A-Za-z0-9_-]+$' as const;
const transportEnum = ['usb','nfc','ble','internal','hybrid','cable'] as const;

// Main data model schema
export const passkeysSchema = {
    $id: 'Passkeys',
    type: 'object',
    additionalProperties: false,
    required: [
        '_id',
        'login',
        'rpID',
        'credentialId',
        'publicKey',
        'signCount'
    ],
    properties: {
        ...commonFields.properties,
        _id: ObjectIdSchema(),
        login: ObjectIdSchema(),
        rpID: {type: 'string', description: 'Relying Party ID, e.g. example.com'},
        credentialId: {
            type: 'string',
            description: 'WebAuthnCredential.id (base64url)',
            pattern: base64urlPattern
        },
        publicKey: {
            type: 'string',
            description: 'WebAuthnCredential.publicKey encoded to base64url',
            pattern: base64urlPattern
        },
        signCount: {type: 'number', description: 'Verification counter (may not always increase)'},
        transports: {
            type: 'array',
            items: {type: 'string', enum: [...transportEnum]},
            description: 'Authenticator transport hints'
        },
        aaguid: {
            type: 'string',
            description: 'Authenticator AAGUID (UUID string if provided)'
        },
        // Modern backup metadata from v13 verification
        backupEligible: {type: 'boolean', description: 'Derived from credentialDeviceType === "multiDevice"'},
        backupState: {enum: ['enabled', 'disabled'], description: 'Derived from credentialBackedUp'},

        displayName: {type: 'string', description: 'User-visible label for the device'},
    }
} as const
export type Passkeys = FromSchema<typeof passkeysSchema>
export const passkeysValidator = getValidator(passkeysSchema, dataValidator)
export const passkeysResolver = resolve<Passkeys, HookContext>({})

export const passkeysExternalResolver = resolve<Passkeys, HookContext>({})

// Schema for creating new data
export const passkeysDataSchema = {
    $id: 'PasskeysData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...passkeysSchema.properties
    }
} as const
export type PasskeysData = FromSchema<typeof passkeysDataSchema>
export const passkeysDataValidator = getValidator(passkeysDataSchema, dataValidator)
export const passkeysDataResolver = resolve<PasskeysData, HookContext>({})

// Schema for updating existing data
export const passkeysPatchSchema = {
    $id: 'PasskeysPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...passkeysSchema.properties,
        ...commonPatch(passkeysSchema.properties).properties
    }
} as const
export type PasskeysPatch = FromSchema<typeof passkeysPatchSchema>
export const passkeysPatchValidator = getValidator(passkeysPatchSchema, dataValidator)
export const passkeysPatchResolver = resolve<PasskeysPatch, HookContext>({})

// Schema for allowed query properties
export const passkeysQuerySchema = {
    $id: 'PasskeysQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(passkeysSchema.properties)
    }
} as const
export type PasskeysQuery = FromSchema<typeof passkeysQuerySchema>
export const passkeysQueryValidator = getValidator(passkeysQuerySchema, queryValidator)
export const passkeysQueryResolver = resolve<PasskeysQuery, HookContext>({})
