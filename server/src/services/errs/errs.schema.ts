// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields} from '../../utils/common/schemas.js';

// Main data model schema
export const errsSchema = {
  $id: 'Errs',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    path: { type: 'string' },
    method: { type: 'string' },
    type: { type: 'string' },
    id: { type: 'string' },
    error: {},
    params: {},
    data: {},
    result: {},
    ...commonFields.properties
  }
} as const
export type Errs = FromSchema<typeof errsSchema>
export const errsValidator = getValidator(errsSchema, dataValidator)
export const errsResolver = resolve<Errs, HookContext>({})

export const errsExternalResolver = resolve<Errs, HookContext>({})

// Schema for creating new data
export const errsDataSchema = {
  $id: 'ErrsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...errsSchema.properties
  }
} as const
export type ErrsData = FromSchema<typeof errsDataSchema>
export const errsDataValidator = getValidator(errsDataSchema, dataValidator)
export const errsDataResolver = resolve<ErrsData, HookContext>({})

// Schema for updating existing data
export const errsPatchSchema = {
  $id: 'ErrsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...errsSchema.properties
  }
} as const
export type ErrsPatch = FromSchema<typeof errsPatchSchema>
export const errsPatchValidator = getValidator(errsPatchSchema, dataValidator)
export const errsPatchResolver = resolve<ErrsPatch, HookContext>({})

// Schema for allowed query properties
export const errsQuerySchema = {
  $id: 'ErrsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(errsSchema.properties)
  }
} as const
export type ErrsQuery = FromSchema<typeof errsQuerySchema>
export const errsQueryValidator = getValidator(errsQuerySchema, queryValidator)
export const errsQueryResolver = resolve<ErrsQuery, HookContext>({})
