// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addressSchema,
    commonFields,
    commonQueries,
    geoJsonFeature,
    imageSchema,
    addToSet,
    pull,
    phoneSchema, exists, serviceAddressSchema, commonPatch, videoSchema
} from '../../utils/common/schemas.js';

const daySchema = {
    type: 'object',
    properties: {
        hour: {type: 'number'},
        minute: {type: 'number'}
    }
} as const;
const openDay = {
    type: 'object',
    properties: {
        open: daySchema,
        close: daySchema
    }
} as const
const openHours = {
    type: 'object',
    patternProperties: {
        "^[0-6]$": openDay,
    }
} as const

// Main data model schema
export const providersSchema = {
    $id: 'Providers',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name'],
    properties: {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        avatar: imageSchema,
        name: {type: 'string'},
        legalName: {type: 'string'},
        address: addressSchema,
        addresses: {type: 'array', items: addressSchema},
        email: {type: 'string'},
        emails: {type: 'array', items: {type: 'string'}},
        phone: phoneSchema,
        phones: {type: 'array', items: phoneSchema},
        images: {type: 'array', items: imageSchema},
        npi: {type: 'string'},
        specialties: {type: 'array', items: {type: 'string'}},
        patientUpdate: { type: 'string' },
        primaryType: {type: 'string'}, //enum: ['doctor', 'dentist', 'pharmacy', 'physiotherapist', 'hospital']
        otherTypes: {
            type: 'array',
            items: {type: 'string'}//enum: ['doctor', 'dentist', 'pharmacy', 'physiotherapist', 'hospital']
        },
        priority: {type: 'number'},
        tags: {type: 'array', items: {type: 'string'}},
        licenses: {
            type: 'array', items: {
                type: 'object',
                properties: {
                    state: {type: 'string'},
                    license: {type: 'string'},
                    desc: {type: 'string'},
                    code: {type: 'string'},
                }
            }
        },
        allVideos: {type: 'array', items: ObjectIdSchema() }, //uploadIds
        videos: {
            type: 'object',
            properties: {
                general: {
                    type: 'object', patternProperties: {
                        "^.*$": videoSchema,
                    }
                },
                memberships: {
                    type: 'object', patternProperties: {
                        "^.*$": videoSchema,
                    }
                },
                bundles: {
                    type: 'object', patternProperties: {
                        "^.*$": videoSchema,
                    }
                }
            }
        },
        bundles: { type: 'array', items: ObjectIdSchema()},
        bundle_length: { type: 'number' },
        auto_created: {type: 'boolean'},
        npi_status: {type: 'string'},
        cities: {type: 'array', items: {type: 'object', properties: {city: {type: 'string'}, state: {type: 'string'}}}},
        typeDescription: {type: 'string'},
        autoGenerated: {type: 'boolean'},
        googlePlacesId: {type: 'string'},
        customerId: { type: 'string' },
        practitioners: {type: 'array', items: ObjectIdSchema()},
        geo: geoJsonFeature,//multipoint
        locations: {
            type: 'array',
            items: serviceAddressSchema
        },
        cats: {type: 'array', items: ObjectIdSchema()},
        googleRating: {type: 'number'},
        googleRatingCount: {type: 'number'},
        googleMapsUri: {type: 'string'},
        regularHours: openHours,
        websiteUri: {type: 'string'},
        payment_settings: {
            type: 'object',
            properties: {
                card_payments: { type: 'string' },
                ach_payments: { type: 'array', items: ObjectIdSchema() }, //account ids
                default_ach: ObjectIdSchema()

            }
        },
        ...commonFields.properties
    }
} as const
export type Providers = FromSchema<typeof providersSchema>
export const providersValidator = getValidator(providersSchema, dataValidator)
export const providersResolver = resolve<Providers, HookContext>({
    priority: async (val, data) => {
        if (!val && data.org) return 1;
        return val;
    },
    bundle_length: async (val, data) => {
        return data.bundles?.length || val;
    },
    typeDescription: async (val) => {
        if(!val) return ''
        return val;
    }
})

export const providersExternalResolver = resolve<Providers, HookContext>({

})

// Schema for creating new data
export const providersDataSchema = {
    $id: 'ProvidersData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...providersSchema.properties
    }
} as const
export type ProvidersData = FromSchema<typeof providersDataSchema>
export const providersDataValidator = getValidator(providersDataSchema, dataValidator)
export const providersDataResolver = resolve<ProvidersData, HookContext>({
    legalName: async (val, data) => {
        if (!val) return data.name;
        return val;
    },
    googlePlacesId: async (val, data) => {
        if (!val) return `*_${data.name}_${new Date().getTime()}`
        return val;
    }
})

const pushPull = [
    {path: 'geo', type: {}},
    {path: 'practitioners', type: ObjectIdSchema()},
    {path: 'locations', type: serviceAddressSchema},
    {path: 'allVideos', type: ObjectIdSchema()},
    {path: 'bundles', type: ObjectIdSchema()},
]
// Schema for updating existing data
export const providersPatchSchema = {
    $id: 'ProvidersPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...providersSchema.properties,
        ...commonPatch(providersSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type ProvidersPatch = FromSchema<typeof providersPatchSchema>
export const providersPatchValidator = getValidator(providersPatchSchema, dataValidator)
export const providersPatchResolver = resolve<ProvidersPatch, HookContext>({})

const queryAdders = {
    _location: {},
    geo: {},
    name: {},
    tags: {},
    'geo.geometry': {},
    ...exists(['org'], providersSchema.properties)
}
// Schema for allowed query properties
export const providersQuerySchema = {
    $id: 'ProvidersQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({...providersSchema.properties, ...queryAdders}),
        ...commonQueries.properties,
        name: {},
        legalName: {},
        text: {type: 'string'},
        'geo.geometry': {},

    }
} as const
export type ProvidersQuery = FromSchema<typeof providersQuerySchema>
export const providersQueryValidator = getValidator(providersQuerySchema, queryValidator)
export const providersQueryResolver = resolve<ProvidersQuery, HookContext>({})
