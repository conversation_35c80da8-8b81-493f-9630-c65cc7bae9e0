// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, commonQueries} from "../../utils/common/schemas.js";

// Main data model schema
export const threadsSchema = {
    $id: 'Threads',
    type: 'object',
    additionalProperties: true,
    required: ['_id', 'parent'],
    properties: {
        _id: ObjectIdSchema(),
        upVotes: {type: 'array', items: ObjectIdSchema()},
        downVotes: {type: 'array', items: ObjectIdSchema()},
        voteCount: {type: 'number'},
        body: {type: 'string'},
        name: { type: 'string' },
        did: {type: 'string'},
        archives: {
            type: 'object', properties: {
                body: {type: 'array', items: {type: 'string'}}
            }
        },
        owners: {
            type: 'array', items: {
                type: 'object', properties: {
                    did: {type: 'string'},
                    id: ObjectIdSchema(),
                    name: {type: 'string'},
                    email: {type: 'string'},
                    phone: {type: 'string'}
                }
            }
        },
        tags: {
            type: 'array', items: {
                type: 'object',
                properties: {
                    name: {type: 'string'},
                    id: ObjectIdSchema(),
                    service: {type: 'string'}
                }
            }
        },
        parent: {
            type: 'object', properties: {
                id: ObjectIdSchema(),
                service: {type: 'string'}
            },
            required: ['id', 'service']
        },
        threads: {
            type: 'array', items: ObjectIdSchema()
        },
        ...commonFields.properties
    }
} as const
export type Threads = FromSchema<typeof threadsSchema>
export const threadsValidator = getValidator(threadsSchema, dataValidator)
export const threadsResolver = resolve<Threads, HookContext>({})

export const threadsExternalResolver = resolve<Threads, HookContext>({})

// Schema for creating new data
export const threadsDataSchema = {
    $id: 'ThreadsData',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...threadsSchema.properties
    }
} as const
export type ThreadsData = FromSchema<typeof threadsDataSchema>
export const threadsDataValidator = getValidator(threadsDataSchema, dataValidator)
export const threadsDataResolver = resolve<ThreadsData, HookContext>({})

// Schema for updating existing data
export const threadsPatchSchema = {
    $id: 'ThreadsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...threadsSchema.properties,
        ...commonPatch(threadsSchema.properties).properties
    }
} as const
export type ThreadsPatch = FromSchema<typeof threadsPatchSchema>
export const threadsPatchValidator = getValidator(threadsPatchSchema, dataValidator)
export const threadsPatchResolver = resolve<ThreadsPatch, HookContext>({})

// Schema for allowed query properties
export const threadsQuerySchema = {
    $id: 'ThreadsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({...threadsSchema.properties, ...commonQueries.properties}),

    }
} as const
export type ThreadsQuery = FromSchema<typeof threadsQuerySchema>
export const threadsQueryValidator = getValidator(threadsQuerySchema, queryValidator)
export const threadsQueryResolver = resolve<ThreadsQuery, HookContext>({})
