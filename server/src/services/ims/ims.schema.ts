// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, phoneSchema, pull} from '../../utils/common/schemas.js';

export const sendTo = {type: 'string', enum: ['in-app', 'phone', 'email']} as const;
const pSchema = {
    type: 'object', properties: {
        login: ObjectIdSchema(),
        fp: {type: 'string'},
        name: {type: 'string'},
        email: {type: 'string'},
        phone: {type: 'string'},
        sendTo,
        lastAt: {},
        status: {type: 'number', enum: [0, 1, 2]},
        offline: {type: 'boolean'}
    }
} as const

const msgSchema = {
    type: 'object', properties: {
        id: { type: 'string' },
        sentAt: {},
        body: {type: 'string'},
        pid: {type: 'string'},
        source: {type: 'string', enum: ['in-app', 'phone', 'email']},
        openedBy: {type: 'array', items: {type: 'string'}},
        errs: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        code: {type: 'number'},
                        message: {type: 'string'},
                        pid: {type: 'string'}
                    }
                }
            }
        }
    }
} as const;
// Main data model schema
export const imsSchema = {
    $id: 'Ims',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        subject: ObjectIdSchema(),
        subjectService: {type: 'string'},
        plan: ObjectIdSchema(),
        person: ObjectIdSchema(),
        team: ObjectIdSchema(),
        participant: pSchema,
        views: { type: 'array', items: { type: 'object', properties: { id: {type: 'string'}, at: {} } }},
        orphan: {type: 'boolean'},
        texting: { type: 'boolean' },
        calling: { type: 'boolean' },
        typing: { type: 'array', items: {type: 'string'}},
        support: {
            type: 'object', patternProperties: {
                "^.*$": pSchema
            }
        },
        messages: {
            type: 'array', items: msgSchema
        },
        ...commonFields.properties
    }
} as const
export type Ims = FromSchema<typeof imsSchema>
export const imsValidator = getValidator(imsSchema, dataValidator)
export const imsResolver = resolve<Ims, HookContext>({})

export const imsExternalResolver = resolve<Ims, HookContext>({})

// Schema for creating new data
export const imsDataSchema = {
    $id: 'ImsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...imsSchema.properties
    }
} as const
export type ImsData = FromSchema<typeof imsDataSchema>
export const imsDataValidator = getValidator(imsDataSchema, dataValidator)
export const imsDataResolver = resolve<ImsData, HookContext>({})

const pushPull = [
    {path: 'messages', type: msgSchema},
    {path: 'typing', type: {type: 'string'}}
]
// Schema for updating existing data
export const imsPatchSchema = {
    $id: 'ImsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...imsSchema.properties,
        ...commonPatch(imsSchema.properties).properties,
        $pull: pull(pushPull),
        $addToSet: addToSet(pushPull),
        'messages.$.body': { type: 'string' }
    }
} as const
export type ImsPatch = FromSchema<typeof imsPatchSchema>
export const imsPatchValidator = getValidator(imsPatchSchema, dataValidator)
export const imsPatchResolver = resolve<ImsPatch, HookContext>({})

// Schema for allowed query properties
export const imsQuerySchema = {
    $id: 'ImsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(imsSchema.properties),
        'participant.fp': ObjectIdSchema(),
        'participant.login': ObjectIdSchema(),
        'participant.name': {},
        'participant.email': {},
        'messages.id': {}
    }
} as const
export type ImsQuery = FromSchema<typeof imsQuerySchema>
export const imsQueryValidator = getValidator(imsQuerySchema, queryValidator)
export const imsQueryResolver = resolve<ImsQuery, HookContext>({})
