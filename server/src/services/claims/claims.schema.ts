// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema, pull, taxSchema} from '../../utils/common/schemas.js';

import {enteredBy} from '../visits/visits.schema.js';

export const adjSchema = {
    type: 'object',
    required: ['adjBy', 'adjAt', 'fp'],
    properties: {
        adjBy: ObjectIdSchema(),
        adjAt: {},
        fp: ObjectIdSchema(),
        enrollment: ObjectIdSchema(),

        ded: {type: 'number'},
        coins: {type: 'number'},
        copay: {type: 'number'},
        coverage: ObjectIdSchema(),
        waived_ded: { type: 'number'},
        waived_coins: { type: 'number'},
        waived_copay: { type: 'number'},

        preventive: { type: 'boolean' },
        amount: {type: 'number'},
        qty: {type: 'number'},
        total: {type: 'number'},
        notes: { type: 'string' }
    }
} as const;
export const adjKeys = Object.keys(adjSchema.properties)

import { paids } from './utils/index.js'
import {coins_categories} from '../marketplace/utils/index.js';

export const claimsSchema = {
    $id: 'Claims',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'visit', 'plan', 'patient', 'person', 'provider', 'date'],
    properties: {
        _id: ObjectIdSchema(),
        visit: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        patient: ObjectIdSchema(),
        person: ObjectIdSchema(),
        practitioner: ObjectIdSchema(),
        provider: ObjectIdSchema(),
        procedure: ObjectIdSchema(),
        med: ObjectIdSchema(),
        coverage: ObjectIdSchema(),
        date: {},
        misc: {type: 'string'},
        enteredBy,
        log: {
            type: 'object',
            properties: {
                code: {type: 'string'},
                standard: {type: 'string'}
            }
        },
        category: {type: 'string', enum: coins_categories},
        description: {type: 'string'},
        notes: {type: 'string'},
        preventive: { type: 'boolean' },

        adj: adjSchema,

        ...paids.properties,

        amount: {type: 'number'},
        subtotal: {type: 'number'},
        total: {type: 'number'},
        qty: {type: 'number'},
        balanceSyncedAt: {},
        balance: {type: 'number'},

        adjHistory: {
            type: 'array', items: adjSchema
        },
        taxes: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        name: {type: 'string'},
                        amount: {type: 'number'}
                    }
                }
            }
        },
        fees: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        name: {type: 'string'},
                        amount: {type: 'number'}
                    }
                }
            }
        },
        payments: {type: 'array', items: ObjectIdSchema()},
        reduced: {
            type: 'object',
            properties: {
                from: {type: 'number'},
                to: {type: 'number'},
                on: {type: 'string'}
            }
        },
        status: {
            type: 'number',
            enum: [0, 1, 2, 3, 4, 5] //0 not opened, 1 processing, 2 adjudicated 3
        },
        settings: {
            type: 'object',
            properties: {
                tax: taxSchema
            }
        },
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },
        threads: {type: 'array', items: ObjectIdSchema()},
        ...commonFields.properties
    }
} as const
export type Claims = FromSchema<typeof claimsSchema>
export const claimsValidator = getValidator(claimsSchema, dataValidator)
export const claimsResolver = resolve<Claims, HookContext>({})

export const claimsExternalResolver = resolve<Claims, HookContext>({
    status: async (val) => {
        if (!val) return 0;
        return val;
    }
})

// Schema for creating new data
export const claimsDataSchema = {
    $id: 'ClaimsData',
    type: 'object',
    additionalProperties: false,
    required: ['visit', 'plan', 'patient', 'person', 'provider', 'date'],
    properties: {
        ...claimsSchema.properties
    }
} as const
export type ClaimsData = FromSchema<typeof claimsDataSchema>
export const claimsDataValidator = getValidator(claimsDataSchema, dataValidator)
export const claimsDataResolver = resolve<ClaimsData, HookContext>({})

const pushPull = [
    {path: 'claimPayments', type: ObjectIdSchema()},
    {path: 'threads', type: ObjectIdSchema()},
    {path: 'adjHistory', type: adjSchema}
]
// Schema for updating existing data
export const claimsPatchSchema = {
    $id: 'ClaimsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...claimsSchema.properties,
        ...commonPatch(claimsSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type ClaimsPatch = FromSchema<typeof claimsPatchSchema>
export const claimsPatchValidator = getValidator(claimsPatchSchema, dataValidator)
export const claimsPatchResolver = resolve<ClaimsPatch, HookContext>({})

// Schema for allowed query properties
export const claimsQuerySchema = {
    $id: 'ClaimsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(claimsSchema.properties),
        date: {}
    }
} as const
export type ClaimsQuery = FromSchema<typeof claimsQuerySchema>
export const claimsQueryValidator = getValidator(claimsQuerySchema, queryValidator)
export const claimsQueryResolver = resolve<ClaimsQuery, HookContext>({})
