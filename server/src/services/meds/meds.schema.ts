// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, commonQueries, pull} from '../../utils/common/schemas.js';

const relatedInfo = {
    type: 'object',
    patternProperties: {
        "^.*$": {
            type: 'object',
            additionalProperties: true,
            properties: {
                rxcui: {type: 'string'},
                name: {type: 'string'},
                synonym: {type: 'string'},
                language: {type: 'string'},
                suppress: {type: 'string'},
                umlscui: {type: 'string'}
            }
        }
    }
} as const
// Main data model schema
export const medsSchema = {
    $id: 'Meds',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'rxcui'],
    properties: {
        _id: ObjectIdSchema(),
        standard: {type: 'string'},
        rxcuis: {type: 'array', items: {type: 'string'}},
        s_f: {type: 'array', items: {type: 'string'}}, //strengths and forms
        variants: {type: 'array', items: {type: 'string'}},
        name: {type: 'string'},
        rxcui: {type: 'string'},
        medical_name: {type: 'string'},
        consumer_name: {type: 'string'},
        description: {type: 'string'},
        activeIngredient: {type: 'string'},
        synonyms: {type: 'string'},
        sbdOf: ObjectIdSchema(),
        ndcs: {type: 'array', items: {type: 'string'}},
        info: {
            type: 'object',
            properties: {
                'IN': relatedInfo,
                'PIN': relatedInfo,
                'MIN': relatedInfo,
                'SCD': relatedInfo,
                'SCDF': relatedInfo,
                'SCDG': relatedInfo,
                'SCDC': relatedInfo,
                'GPCK': relatedInfo,
                'BN': relatedInfo,
                'BPCK': relatedInfo,
                'DF': relatedInfo,
                'DFG': relatedInfo,
                'SBD': relatedInfo,
                'SBDG': relatedInfo,
                'SBDC': relatedInfo,
                'SBDF': relatedInfo,
                'SBDFP': relatedInfo
            }
        },
        ...commonFields.properties
    }
} as const

export type Meds = FromSchema<typeof medsSchema>
export const medsValidator = getValidator(medsSchema, dataValidator)
export const medsResolver = resolve<Meds, HookContext>({})

export const medsExternalResolver = resolve<Meds, HookContext>({})

// Schema for creating new data
export const medsDataSchema = {
    $id: 'MedsData',
    type: 'object',
    additionalProperties: false,
    required: ['rxcui'],
    properties: {
        ...medsSchema.properties
    }
} as const
export type MedsData = FromSchema<typeof medsDataSchema>
export const medsDataValidator = getValidator(medsDataSchema, dataValidator)
export const medsDataResolver = resolve<MedsData, HookContext>({})

const pushPull = [{ path: 'ndcs', type: { type: 'string' }}]
// Schema for updating existing data
export const medsPatchSchema = {
    $id: 'MedsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...medsSchema.properties,
        ...commonPatch(medsSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type MedsPatch = FromSchema<typeof medsPatchSchema>
export const medsPatchValidator = getValidator(medsPatchSchema, dataValidator)
export const medsPatchResolver = resolve<MedsPatch, HookContext>({})

// Schema for allowed query properties
export const medsQuerySchema = {
    $id: 'MedsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...commonQueries.properties,
        ...querySyntax({
            ...medsSchema.properties,
            name: {},
            medical_name: {},
            code: {},
            rxcui: {},
            'ndcs.0': {}
        }),

    }
} as const
export type MedsQuery = FromSchema<typeof medsQuerySchema>
export const medsQueryValidator = getValidator(medsQuerySchema, queryValidator)
export const medsQueryResolver = resolve<MedsQuery, HookContext>({})
