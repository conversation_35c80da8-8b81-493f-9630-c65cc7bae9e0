// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js'
import {normPolicySchema} from '../marketplace/utils/index.js';

// Main data model schema
export const sePlansSchema = {
    $id: 'SePlans',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name', 'plan_id', 'state_code'],
    properties: {
        _id: ObjectIdSchema(),
        // Common fields
        org: ObjectIdSchema(),
        public: {type: 'boolean'},
        template: {type: 'boolean'},
        fromTemplate: ObjectIdSchema(),
        sim: {type: 'boolean'},
        rating_areas: {
            type: 'object',
            patternProperties: {
                "^\d+$": {
                    type: 'object',
                    properties: {
                        name: {type: 'string'},
                        zips: {type: 'array', items: {type: 'string'}},
                        fips: {type: 'array', items: {type: 'string'}},
                        cities: {type: 'array', items: {type: 'string'}},
                        county: {type: 'string'},
                        rates: {
                            type: 'object',
                            patternProperties: {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {type: 'number'}}
                        }
                    }
                }
            }
        },
        rateIncrease: {
            type: 'object',
            properties: {
                amount: {type: 'number'},
                date: {}
            }
        },
        all_fips: { type: 'array', items: {type: 'string'}},
        first_3_zips: {type: 'array', items: {type: 'string'}}, // First 3 digits of all zips in rating areas for more efficient querying
        all_zips: {type: 'array', items: {type: 'string'}}, // All zips in rating areas for querying and search
        fortyPremium: {type: 'number'},
        design: {type: 'string'},
        // NormPolicy fields based on the type definition
        ...normPolicySchema.properties,
        //Currently only supporting variants for silver plans. The others are native population variants (bronze, gold).
        csr: {
            type: 'object', patternProperties: {
                "^(02|03|04)$": normPolicySchema
            }
        },
        ...commonFields.properties
    }
} as const
export type SePlans = FromSchema<typeof sePlansSchema>
export const sePlansValidator = getValidator(sePlansSchema, dataValidator)
export const sePlansResolver = resolve<SePlans, HookContext>({})

export const sePlansExternalResolver = resolve<SePlans, HookContext>({})

// Schema for creating new data
export const sePlansDataSchema = {
    $id: 'SePlansData',
    type: 'object',
    additionalProperties: false,
    required: ['name', 'plan_id', 'state_code'],
    properties: {
        ...sePlansSchema.properties
    }
} as const
export type SePlansData = FromSchema<typeof sePlansDataSchema>
export const sePlansDataValidator = getValidator(sePlansDataSchema, dataValidator)
export const sePlansDataResolver = resolve<SePlansData, HookContext>({})

// Schema for updating existing data
export const sePlansPatchSchema = {
    $id: 'SePlansPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...sePlansSchema.properties,
        ...commonPatch(sePlansSchema.properties).properties,
        $addToSet: {},
        $pull: {}
    }
} as const
export type SePlansPatch = FromSchema<typeof sePlansPatchSchema>
export const sePlansPatchValidator = getValidator(sePlansPatchSchema, dataValidator)
export const sePlansPatchResolver = resolve<SePlansPatch, HookContext>({})

// Schema for allowed query properties
export const sePlansQuerySchema = {
    $id: 'SePlansQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(
            {
                ...sePlansSchema.properties,
                name: {},
                carrierName: {},
                plan_id: {},
                fortyPremium: {},
                rating_areas: {}
            }
        ),

    }
} as const
export type SePlansQuery = FromSchema<typeof sePlansQuerySchema>
export const sePlansQueryValidator = getValidator(sePlansQuerySchema, queryValidator)
export const sePlansQueryResolver = resolve<SePlansQuery, HookContext>({})
