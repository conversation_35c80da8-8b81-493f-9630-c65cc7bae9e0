// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {addToSet, commonFields, commonPatch, commonQueries, imageSchema, pull} from '../../utils/common/schemas.js';

// Main data model schema
export const networksSchema = {
  $id: 'Networks',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'name', 'description'],
  properties: {
    _id: ObjectIdSchema(),
    avatar: imageSchema,
    access: { type: 'string', enum: ['public', 'private', 'searchable'] },
    subs: { type: 'array', items: ObjectIdSchema() },
    managers: { type: 'array', items: ObjectIdSchema() },
    writers: { type: 'array', items: ObjectIdSchema() },
    images: { type: 'array', items: imageSchema },
    name: { type: 'string' },
    description: { type: 'string' },
    lastSync: { type: 'string' },
    bundle_changes: { type: 'array', items: ObjectIdSchema() },
    plans: { type: 'array', items: ObjectIdSchema() }, //added by network, may not match on plan
    bundles: { type: 'array', items: ObjectIdSchema() }, //added by network - may not be matching on bundle
    bundle_reqs: { type: 'array', items: ObjectIdSchema() },
    plan_reqs: { type: 'array', items: ObjectIdSchema() },
    bundle_invites: { type: 'array', items: ObjectIdSchema() },
    plans_invites: { type: 'array', items: ObjectIdSchema() },
    ...commonFields.properties
  }
} as const
export type Networks = FromSchema<typeof networksSchema>
export const networksValidator = getValidator(networksSchema, dataValidator)
export const networksResolver = resolve<Networks, HookContext>({})

export const networksExternalResolver = resolve<Networks, HookContext>({})

// Schema for creating new data
export const networksDataSchema = {
  $id: 'NetworksData',
  type: 'object',
  additionalProperties: false,
  required: ['name', 'description'],
  properties: {
    ...networksSchema.properties
  }
} as const
export type NetworksData = FromSchema<typeof networksDataSchema>
export const networksDataValidator = getValidator(networksDataSchema, dataValidator)
export const networksDataResolver = resolve<NetworksData, HookContext>({
  access: async (val) => {
    if(!val) return 'private'
    return val
  }
})

const pushPull = [{path: 'plan_invites', type: ObjectIdSchema()}, { path: 'plan_reqs', type: ObjectIdSchema() }, {path: 'bundle_invites', type: ObjectIdSchema()}, { path: 'bundle_reqs', type: ObjectIdSchema() }, { path: 'plans', type: ObjectIdSchema() }, { path: 'bundles', type: ObjectIdSchema() }]

// Schema for updating existing data
export const networksPatchSchema = {
  $id: 'NetworksPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...networksSchema.properties,
    ...commonPatch(networksSchema.properties).properties,
    $addToSet: addToSet(pushPull),
    $pull: pull(pushPull)
  }
} as const
export type NetworksPatch = FromSchema<typeof networksPatchSchema>
export const networksPatchValidator = getValidator(networksPatchSchema, dataValidator)
export const networksPatchResolver = resolve<NetworksPatch, HookContext>({})

// Schema for allowed query properties
export const networksQuerySchema = {
  $id: 'NetworksQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax({...networksSchema.properties, ...commonQueries.properties}),
    name: {}
  }
} as const
export type NetworksQuery = FromSchema<typeof networksQuerySchema>
export const networksQueryValidator = getValidator(networksQuerySchema, queryValidator)
export const networksQueryResolver = resolve<NetworksQuery, HookContext>({})
