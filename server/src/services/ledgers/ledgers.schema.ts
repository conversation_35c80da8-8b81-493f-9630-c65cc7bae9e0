// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const ledgersSchema = {
  $id: 'Ledgers',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'plan', 'org', 'person', 'planYear'],
  properties: {
    _id: ObjectIdSchema(),
    plan: ObjectIdSchema(),
    org: ObjectIdSchema(),
    person: ObjectIdSchema(),
    planYear: { type: 'number'},
    type: { type: 'string', enum: ['fsa', 'hra', 'ichra', 'dcap']},
    account: ObjectIdSchema(),
    transactions: { type: 'array', items: ObjectIdSchema() },
    ...commonFields.properties
  }
} as const
export type Ledgers = FromSchema<typeof ledgersSchema>
export const ledgersValidator = getValidator(ledgersSchema, dataValidator)
export const ledgersResolver = resolve<Ledgers, HookContext>({})

export const ledgersExternalResolver = resolve<Ledgers, HookContext>({})

// Schema for creating new data
export const ledgersDataSchema = {
  $id: 'LedgersData',
  type: 'object',
  additionalProperties: false,
  required: ['plan', 'org', 'person', 'planYear'],
  properties: {
    ...ledgersSchema.properties
  }
} as const
export type LedgersData = FromSchema<typeof ledgersDataSchema>
export const ledgersDataValidator = getValidator(ledgersDataSchema, dataValidator)
export const ledgersDataResolver = resolve<LedgersData, HookContext>({})

// Schema for updating existing data
export const ledgersPatchSchema = {
  $id: 'LedgersPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...ledgersSchema.properties,
    ...commonPatch(ledgersSchema.properties).properties
  }
} as const
export type LedgersPatch = FromSchema<typeof ledgersPatchSchema>
export const ledgersPatchValidator = getValidator(ledgersPatchSchema, dataValidator)
export const ledgersPatchResolver = resolve<LedgersPatch, HookContext>({})

// Schema for allowed query properties
export const ledgersQuerySchema = {
  $id: 'LedgersQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(ledgersSchema.properties)
  }
} as const
export type LedgersQuery = FromSchema<typeof ledgersQuerySchema>
export const ledgersQueryValidator = getValidator(ledgersQuerySchema, queryValidator)
export const ledgersQueryResolver = resolve<LedgersQuery, HookContext>({})
