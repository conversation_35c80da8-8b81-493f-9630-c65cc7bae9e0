// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const walletsSchema = {
  $id: 'Wallets',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'owner'],
  properties: {
    _id: ObjectIdSchema(),
    owner: ObjectIdSchema(),
    ownerService: { type: 'string', enum: ['ppls', 'orgs']},
    methods: {
      type: 'object',
      patternProperties: {
        "^.*$": {
          type: 'object', properties: {
            id: {type: 'string'},
            name: { type: 'string' }
          }
        }
      }
    },
    ...commonFields.properties
  }
} as const
export type Wallets = FromSchema<typeof walletsSchema>
export const walletsValidator = getValidator(walletsSchema, dataValidator)
export const walletsResolver = resolve<Wallets, HookContext>({})

export const walletsExternalResolver = resolve<Wallets, HookContext>({})

// Schema for creating new data
export const walletsDataSchema = {
  $id: 'WalletsData',
  type: 'object',
  additionalProperties: false,
  required: ['owner'],
  properties: {
    ...walletsSchema.properties
  }
} as const
export type WalletsData = FromSchema<typeof walletsDataSchema>
export const walletsDataValidator = getValidator(walletsDataSchema, dataValidator)
export const walletsDataResolver = resolve<WalletsData, HookContext>({})

// Schema for updating existing data
export const walletsPatchSchema = {
  $id: 'WalletsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...walletsSchema.properties,
    ...commonPatch(walletsSchema.properties).properties
  }
} as const
export type WalletsPatch = FromSchema<typeof walletsPatchSchema>
export const walletsPatchValidator = getValidator(walletsPatchSchema, dataValidator)
export const walletsPatchResolver = resolve<WalletsPatch, HookContext>({})

// Schema for allowed query properties
export const walletsQuerySchema = {
  $id: 'WalletsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(walletsSchema.properties)
  }
} as const
export type WalletsQuery = FromSchema<typeof walletsQuerySchema>
export const walletsQueryValidator = getValidator(walletsQuerySchema, queryValidator)
export const walletsQueryResolver = resolve<WalletsQuery, HookContext>({})
