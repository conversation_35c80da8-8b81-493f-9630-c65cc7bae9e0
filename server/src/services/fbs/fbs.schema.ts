// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema, pull} from '../../utils/common/schemas.js';

const vSchema = {
    type: 'object',
    properties: {
        check: {type: 'string'},
        arg: {},
        error: {type: 'string'},
        rule: {type: 'string'}
    }
} as const

const fieldSchema = {
    type: 'object',
    properties: {
        id: { type: 'string' },
        color: {type: 'string'},
        icon: {type: 'string'},
        label: {type: 'string'},
        value: {},
        next: {type: 'array', items: {type: 'object', properties: {field: { type: 'string'}, if: vSchema}}},
        validators: vSchema,
        slots: {type: 'array'},
        rules: {type: 'array'},
        errs: {type: 'array'},
        fieldType: {type: 'string'},
        path: {type: 'string'},
        attrs: {},
        'div-attrs': {}
    }
} as const

// Main data model schema
export const fbsSchema = {
    $id: 'Fbs',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        active: {type: 'boolean'},
        owner: ObjectIdSchema(),
        org: ObjectIdSchema(),
        live: {type: 'boolean'},
        parent: ObjectIdSchema(),
        children: {type: 'array', items: ObjectIdSchema()},
        name: {type: 'string'},
        primaryColor: {type: 'string'},
        secondaryColor: {type: 'string'},
        description: {type: 'string'},
        avatar: imageSchema,
        welcomeTitle: {type: 'string'},
        welcomeMessage: {type: 'string'},
        welcomeImage: imageSchema,
        welcomeVideos: {type: 'array', items: imageSchema},
        welcomeFiles: {type: 'array', items: imageSchema},
        finishTitle: {type: 'string'},
        finishMessage: {type: 'string'},
        finishImage: imageSchema,
        finishVideos: {type: 'array', items: imageSchema},
        finishFiles: {type: 'array', items: imageSchema},
        dark: {type: 'boolean'},
        class: {type: 'string'},
        products: {
            type: 'object',
            properties: {
                ids: {type: 'array'}
            }
        },
        style: {
            type: 'object',
            properties: {
                background: {type: 'string'},
                color: {type: 'string'},
                padding: {type: 'string'}
            }
        },
        fields: {type: 'array', items: fieldSchema},
        responses: {type: 'array', items: ObjectIdSchema()},
        canEdit: {type: 'array', items: ObjectIdSchema()},
        ...commonFields.properties
    }
} as const
export type Fbs = FromSchema<typeof fbsSchema>
export const fbsValidator = getValidator(fbsSchema, dataValidator)
export const fbsResolver = resolve<Fbs, HookContext>({})

export const fbsExternalResolver = resolve<Fbs, HookContext>({})

// Schema for creating new data
export const fbsDataSchema = {
    $id: 'FbsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...fbsSchema.properties
    }
} as const
export type FbsData = FromSchema<typeof fbsDataSchema>
export const fbsDataValidator = getValidator(fbsDataSchema, dataValidator)
export const fbsDataResolver = resolve<FbsData, HookContext>({})

const pushPull = [
    { path: 'children', type: ObjectIdSchema() },
    { path: 'responses', type: ObjectIdSchema() },
    { path: 'canEdit', type: ObjectIdSchema() },
    { path: 'welcomeVideos', type: imageSchema },
    { path: 'welcomeFiles', type: imageSchema },
    { path: 'finishVideos', type: imageSchema },
    { path: 'finishFiles', type: imageSchema },
]
// Schema for updating existing data
export const fbsPatchSchema = {
    $id: 'FbsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...fbsSchema.properties,
        ...commonPatch(fbsSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull)
    }
} as const
export type FbsPatch = FromSchema<typeof fbsPatchSchema>
export const fbsPatchValidator = getValidator(fbsPatchSchema, dataValidator)
export const fbsPatchResolver = resolve<FbsPatch, HookContext>({})

// Schema for allowed query properties
export const fbsQuerySchema = {
    $id: 'FbsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(fbsSchema.properties),
        name: {}
    }
} as const
export type FbsQuery = FromSchema<typeof fbsQuerySchema>
export const fbsQueryValidator = getValidator(fbsQuerySchema, queryValidator)
export const fbsQueryResolver = resolve<FbsQuery, HookContext>({})
