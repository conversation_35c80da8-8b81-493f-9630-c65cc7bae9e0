// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax, queryProperties} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addToSet,
    commonFields,
    commonPatch,
    commonQueries,
    imageSchema,
    pull
} from '../../utils/common/schemas.js';
import {contributions} from '../enrollments/enrollments.schema.js'
// Main data model schema

const cafe = {
    type: 'object',
    properties: {
        active: {type: 'boolean'},
        doc: ObjectIdSchema(),
        manual_doc: ObjectIdSchema(), //upload id
        taxStatus: {type: 'number', enum: [0, 1, 2]}, //0 = taxable, 1 = no tax, 2 = mix
        hsaEligible: {type: 'boolean'},
        gracePeriod: {type: 'number'}, //days
        carryover: {type: 'number'},
        budget: ObjectIdSchema(),
        monthlySpend: {type: 'number'},
        limits: {
            type: 'object',
            properties: {
                single: {type: 'number'},
                family: {type: 'number'}
            }
        }
    }
} as const;

const cafeSchema = {
    type: 'object',
    properties: {
        hsa: cafe,
        fsa: cafe,
        dcp: cafe,
        pop: cafe,
        def: cafe, //retirement
        cash: cafe
    }
} as const

const hra = {
    type: 'object',
    properties: {
        active: {type: 'boolean'},
        doc: ObjectIdSchema(),
        manual_doc: ObjectIdSchema(),//upload id
        budget: ObjectIdSchema(),
        recurs: {type: 'number'},
        limits: {
            type: 'object',
            properties: {
                single: {type: 'number'},
                family: {type: 'number'}
            }
        }
    }
} as const

const hraSchema = {
    type: 'object',
    properties: {
        ichra: hra,
        ebhra: hra,
        gchra: hra
    }
} as const

const contactInfo = {
    type: 'object', properties: {
        name: {type: 'string'},
        phone: {type: 'string'},
        email: {type: 'string'}
    }
} as const

import {feeEnum} from '../offers/offers.schema.js';
import {guideEnum} from '../hosts/hosts.schema.js';

const rfpSchema = {
    type: 'object', properties: {
        hosts: {type: 'array', items: ObjectIdSchema()},
        public: {type: 'boolean'},
        fee: {type: 'number'},
        feeType: {type: 'string', enum: feeEnum}
    }
} as const

export const employerContribution = {
    type: 'object',
    properties: {
        amount: {type: 'number'},
        family: {type: 'number'},
        match: {type: 'boolean'},
        type: {type: 'string', enum: ['percent', 'flat']},
        percentType: {type: 'string', enum: ['cost', 'income']},
        postTax: {type: 'number'}, //percent of normal contribution to give for post tax elections
        includeExtras: {type: 'boolean'}
    }
} as const

export const plansSchema = {
    $id: 'Plans',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name'],
    properties: {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        parent: ObjectIdSchema(),
        doc: ObjectIdSchema(),
        spd: ObjectIdSchema(),
        ale: {type: 'boolean'},
        estFte: {type: 'number'},
        groups: {type: 'array', items: ObjectIdSchema()},
        orgs: {type: 'array', items: ObjectIdSchema()},
        vectorStoreIds: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        id: {type: 'string'}, /** Vector store id */
                        fileIds: {type: 'array', items: { type: 'string'}}, /** file id so they can be removed upon update */
                        updatedAt: {}
                    }
                }
            }
        },
        rfp: {
            type: 'object',
            properties: {
                'care_director': rfpSchema,
                'plan_guide': rfpSchema,
                'compliance': rfpSchema,
                'finance': rfpSchema,
                'physician': rfpSchema,
            }
        },
        dependents: {
            type: 'object',
            properties: {
                excludeSpouse: {type: 'boolean'}
            }
        },
        info: {
            type: 'object',
            additionalProperties: true,
            properties: {
                sponsor: {
                    type: 'object',
                    properties: {
                        ...contactInfo.properties,
                        ein: {type: 'string'}
                    }
                },
                planAdmin: contactInfo,
                fiduciary: contactInfo,
                legalAgent: contactInfo,
                number: {type: 'string'},
                numberEin: {type: 'string'},
            }
        },
        team: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        id: ObjectIdSchema(),
                        status: {type: 'string', enum: ['pending', 'canceled', 'active']},
                        role: {
                            type: 'string',
                            enum: guideEnum
                        },
                        roleDescription: {type: 'string'},
                        fee: {type: 'number'},
                        feeType: {type: 'string', enum: ['alg', 'pepm', 'pmpm', 'flat']},
                        feeDescription: {type: 'string'},
                        contract: ObjectIdSchema(),
                        conflicts: {type: 'string'},
                        approvedBy: ObjectIdSchema(),
                        approvedAt: {}
                    }
                }
            }
        },
        template: {type: 'boolean'},
        name: {type: 'string'},
        aka: {type: 'array', items: {type: 'string'}},
        active: {type: 'boolean'},
        description: {type: 'string'},
        eligibility: {
            type: 'object',
            properties: {
                hours: {type: 'number'},
                term: {type: 'number'}
            }
        },
        enrollments: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        active: {type: 'boolean'},
                        description: {type: 'string'},
                        open: {type: 'string'},
                        close: {type: 'string'},
                        enrolled: {type: 'number'},
                        lastUpdate: {},
                        lastEnrolled: {type: 'string'},
                        contributions,
                        open_enroll: {type: 'boolean'},
                        ppls: {type: 'array', items: ObjectIdSchema()},
                        groups: {type: 'array', items: ObjectIdSchema()},
                        sentThrough: {type: 'number'},
                    }
                }
            }
        },
        billEraser: {
            type: 'object', properties: {
                max: {type: 'number'},
                budget: ObjectIdSchema()
            }
        },
        planYearStart: {},
        cafe: cafeSchema,
        hra: hraSchema,
        coverages: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        type: {type: 'string'},
                        groups: {type: 'array', items: ObjectIdSchema()},
                        budget: ObjectIdSchema(),
                        card: ObjectIdSchema(),
                        employeeBudget: {type: 'boolean'},
                        employerContribution: {
                            type: 'object',
                            properties: {
                                single: {type: 'number'}, //if type is percent, whole number (ie: the actual percentage * 100)
                                family: {type: 'number'},
                                type: {type: 'string', enum: ['flat', 'percent']}
                            }
                        },
                        id: ObjectIdSchema()
                    }
                }
            }
        },
        employerContribution: {
            type: 'object',
            patternProperties: {
                "^.*$": employerContribution
            }
        },
        files: {
            type: 'object', patternProperties: {
                "^.*$": imageSchema
            }
        },
        networks: {type: 'array', items: ObjectIdSchema()},
        ...commonFields.properties
    }

} as const
export type Plans = FromSchema<typeof plansSchema>
export const plansValidator = getValidator(plansSchema, dataValidator)
export const plansResolver = resolve<Plans, HookContext>({
    properties: {
        cafe: async (val) => {
            if (val) val.cash ? val.cash['active'] = true : val.cash = {active: true}
            return val;
        },
        eligibility: async (val) => {
            if (val) {
                if ((val.hours || 0) > 30) return {term: 90, ...val, hours: 30}
                if ((val.term || 0) > 90) return {hours: 30, ...val, term: 90}
                return {hours: 30, term: 90, ...val}
            }
        },
        active: async (val) => {
            return !!val;
        },
        planYearStart: async (val) => {
            if (val) return val;
            const today = new Date();
            const year = today.getFullYear()
            return new Date(`01-01-${year}`)
        }
    }
})

export const plansExternalResolver = resolve<Plans, HookContext>({})

// Schema for creating new data
export const plansDataSchema = {
    $id: 'PlansData',
    type: 'object',
    additionalProperties: false,
    required: ['name'],
    properties: {
        ...plansSchema.properties
    }
} as const
export type PlansData = FromSchema<typeof plansDataSchema>
export const plansDataValidator = getValidator(plansDataSchema, dataValidator)
export const plansDataResolver = resolve<PlansData, HookContext>({
    properties: {
        eligibility: async (val) => {
            if (!val) return {hours: 30, term: 90};
            if ((val.hours || 0) > 30) return {term: 90, ...val, hours: 30}
            if ((val.term || 0) > 90) return {hours: 30, ...val, term: 90}
            return {hours: 30, term: 90, ...val}
        }
    }
})

const pushPull = [
    {path: 'groups', type: ObjectIdSchema()},
    {path: 'networks', type: ObjectIdSchema()},
    {path: 'rfp.plan_guide.hosts', type: ObjectIdSchema()},
    {path: 'rfp.care_director.hosts', type: ObjectIdSchema()},
    {path: 'rfp.compliance.hosts', type: ObjectIdSchema()},
    {path: 'rfp.finance.hosts', type: ObjectIdSchema()},
    {path: 'rfp.physician.hosts', type: ObjectIdSchema()},
]
// Schema for updating existing data
export const plansPatchSchema = {
    $id: 'PlansPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...plansSchema.properties,
        ...commonPatch(plansSchema.properties).properties,
        $addToSet: addToSet(pushPull),
        $pull: pull(pushPull),
        $inc: {}
    }
} as const
export type PlansPatch = FromSchema<typeof plansPatchSchema>
export const plansPatchValidator = getValidator(plansPatchSchema, dataValidator)
export const plansPatchResolver = resolve<PlansPatch, HookContext>({})

const qp = {
    ...plansSchema.properties,
    ...commonQueries.properties,
    'rfp.plan_guide.hosts': {type: 'array', items: ObjectIdSchema()},
    'rfp.care_director.hosts': {type: 'array', items: ObjectIdSchema()},
    'rfp.compliance.hosts': {type: 'array', items: ObjectIdSchema()},
    'rfp.finance.hosts': {type: 'array', items: ObjectIdSchema()},
    'rfp.plan_guide.public': {type: 'boolean'},
    'rfp.care_director.public': {type: 'boolean'},
    'rfp.compliance.public': {type: 'boolean'},
    'rfp.finance.public': {type: 'boolean'},
    name: {},
    description: {}
} as const
const querySchema = querySyntax(qp)
// Schema for allowed query properties
export const plansQuerySchema = {
    $id: 'PlansQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySchema,
        planYearStart: {}
    }
} as const
export type PlansQuery = FromSchema<typeof plansQuerySchema>
export const plansQueryValidator = getValidator(plansQuerySchema, queryValidator)
export const plansQueryResolver = resolve<PlansQuery, HookContext>({})
