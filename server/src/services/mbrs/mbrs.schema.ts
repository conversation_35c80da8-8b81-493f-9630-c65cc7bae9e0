// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, phoneSchema } from '../../utils/common/schemas.js'

// Main data model schema
export const mbrsSchema = {
  $id: 'Mbrs',
  type: 'object',
  additionalProperties: false,
  required: ['_id', 'person', 'coverage', 'itemId'],
  properties: {
    _id: ObjectIdSchema(),
    coverage: ObjectIdSchema(),
    person: ObjectIdSchema(),
    enrollment: ObjectIdSchema(),
    plan: ObjectIdSchema(),
    provider: ObjectIdSchema(),
    pm: ObjectIdSchema(),
    inactive: { type: 'boolean' },
    itemId: { type: 'string' },
    ...commonFields.properties
  }
} as const
export type Mbrs = FromSchema<typeof mbrsSchema>
export const mbrsValidator = getValidator(mbrsSchema, dataValidator)
export const mbrsResolver = resolve<Mbrs, HookContext>({})

export const mbrsExternalResolver = resolve<Mbrs, HookContext>({})

// Schema for creating new data
export const mbrsDataSchema = {
  $id: 'MbrsData',
  type: 'object',
  additionalProperties: false,
  required: ['person', 'coverage', 'itemId'],
  properties: {
    ...mbrsSchema.properties
  }
} as const
export type MbrsData = FromSchema<typeof mbrsDataSchema>
export const mbrsDataValidator = getValidator(mbrsDataSchema, dataValidator)
export const mbrsDataResolver = resolve<MbrsData, HookContext>({})

// Schema for updating existing data
export const mbrsPatchSchema = {
  $id: 'MbrsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...mbrsSchema.properties,
    ...commonPatch(mbrsSchema.properties).properties
  }
} as const
export type MbrsPatch = FromSchema<typeof mbrsPatchSchema>
export const mbrsPatchValidator = getValidator(mbrsPatchSchema, dataValidator)
export const mbrsPatchResolver = resolve<MbrsPatch, HookContext>({})

// Schema for allowed query properties
export const mbrsQuerySchema = {
  $id: 'MbrsQuery',
  type: 'object',
  additionalProperties: false,
  properties: {
    ...querySyntax(mbrsSchema.properties)
  }
} as const
export type MbrsQuery = FromSchema<typeof mbrsQuerySchema>
export const mbrsQueryValidator = getValidator(mbrsQuerySchema, queryValidator)
export const mbrsQueryResolver = resolve<MbrsQuery, HookContext>({})
