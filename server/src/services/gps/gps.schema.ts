// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, imageSchema, pull} from '../../utils/common/schemas.js';
import {employerContribution} from '../plans/plans.schema.js';
import {coverageCalcSchema} from '../coverages/coverages.schema.js';
import {eeSchema} from '../doc-requests/doc-requests.schema.js';

const premiumByKey = {
    type: 'object',
    properties: {
        'single': {type: 'number'},
        'plus_spouse': {type: 'number'},
        'plus_child': {type: 'number'},
        'plus_child__2': {type: 'number'},
        'plus_child__3': {type: 'number'},
        'family': {type: 'number'}
    }
} as const;

const simBase = {
    type: 'object',
    properties: {
        count: { type: 'number', $comment: 'Total number of auto-selected plans' },
        spendPtc: {type: 'number', $comment: 'premium + oop for auto-selected plan factoring in PTC'},
        premiumPtc: {type: 'number', $comment: 'premium for auto-selected plan factoring in PTC'},
        spend: {type: 'number', $comment: 'premium + oop for auto-selected plan'},
        premium: {type: 'number', $comment: 'premium for auto-selected plan'},
        // premiumByKey,
        // premiumByKeyPtc: premiumByKey,
        // countByKey: premiumByKey,
        tax_savings: { type: 'number', $comment: 'Total tax savings for auto-selected plan if ICHRA' },
        tax_rate: {type: 'number', $comment: 'Average tax rate for auto-selected plan employees - useful for group calculations' },
        ptc: { type: 'number', $comment: 'Total PTC for auto-selected plan' },
        ptc_likely: { type: 'number', $comment: 'Likely actual PTC based on whether the best option was PTC eligible' },
        altSpend: {type: 'number', $comment: 'Best non insurance option'},
        altPremium: {type: 'number'},
        // altByKey: premiumByKey,
        selected_spend: {type: 'number'},
        selected_spend_delta: { type: 'number' },
        selected_spendPtc_delta: { type: 'number' },

        selected_premium: {type: 'number'},
        selected_premium_delta: { type: 'number', $comment: 'The difference in premium between the auto-selected plan and the employee selected plan' },
        selected_premiumPtc_delta: { type: 'number', $comment: 'The difference in premium between the auto-selected plan and the employee selected plan' },

        selected_ptc: {type: 'number', $comment: 'PTC applying to selected plans'},
        selected_ptc_delta: { type: 'number', $comment: 'The difference in PTC between the auto-selected plan and the employee selected plan' },
        // selected_deltaByKey: premiumByKey,
        // selected_premiumByKey: premiumByKey,
        selected_count: {type: 'number'},
        selected_tax_savings: {type: 'number'},
        selected_tax_savings_delta: {type: 'number'},
        selected_tax_rate: { type: 'number' }
    }
} as const
const inactiveStats = () => {
    const obj = {};
    for(const k in simBase.properties){
        obj[`inactive_${k}`] = simBase.properties[k];
    }
    return obj;
}
// Main data model schema
export const gpsSchema = {
    $id: 'Gps',
    type: 'object',
    additionalProperties: false,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        plan: ObjectIdSchema(),
        runRequest: {},
        companyName: {type: 'string'},
        companyAvatar: { type: 'string' },
        email: {type: 'string'},
        name: {type: 'string'},
        eeCount: {type: 'number'},
        planName: {type: 'string'},
        employerContribution,
        ale: {type: 'boolean'},
        owner: ObjectIdSchema(), //person,
        editors: {type: 'array', items: ObjectIdSchema()},
        vectorId: {type: 'string'},
        lastSim: {},
        groupCompare: {type: 'boolean'},
        simProgress: {type: 'number'},
        simStats: {
            type: 'object',
            $comment: 'Split the stats into enrolled employees (no prefix) and not enrolled "inactive"',
            properties: {
                ...simBase.properties,
                ...inactiveStats()
            }
        },
        currentStats: {
            type: 'object',
            properties: {
                spend: {type: 'number'},
                spendCount: { type: 'number'},
                spendPremium: { type: 'number' },
                count: { type: 'number' },
                premium: {type: 'number'},
                premiumByKey,
                countByKey: premiumByKey
            }
        },
        employees: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    shop: ObjectIdSchema(),
                    coverage: {type: 'string'},
                    sim: ObjectIdSchema(),
                    simError: {type: 'string'},
                    bestPlan: {type: 'string'},
                    bestPlanPtc: {type: 'string'},
                    bestAlt: {type: 'string'},
                    ...eeSchema.properties
                }
            }
        },
        coverages: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        id: {type: 'string'},
                        compare_id: {type: 'string'},
                        similar: {
                            type: 'object', patternProperties: {
                                "^.*$": {type: 'string'}
                            }
                        },
                        mostSimilar: {type: 'string'},
                        ...coverageCalcSchema.properties,
                        knownKeys: {type: 'array', items: {type: 'string'}},
                        files: {type: 'array', items: imageSchema},
                        fromFile: {type: 'boolean'}
                    }
                }
            }
        },
        employerContributionReports: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        person: ObjectIdSchema(),
                        gps: ObjectIdSchema(),
                        updatedAt: {},
                        data: employerContribution
                    }
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type Gps = FromSchema<typeof gpsSchema>
export const gpsValidator = getValidator(gpsSchema, dataValidator)
export const gpsResolver = resolve<Gps, HookContext>({})

export const gpsExternalResolver = resolve<Gps, HookContext>({})

// Schema for creating new data
export const gpsDataSchema = {
    $id: 'GpsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...gpsSchema.properties
    }
} as const
export type GpsData = FromSchema<typeof gpsDataSchema>
export const gpsDataValidator = getValidator(gpsDataSchema, dataValidator)
export const gpsDataResolver = resolve<GpsData, HookContext>({})

// Schema for updating existing data
export const gpsPatchSchema = {
    $id: 'GpsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...gpsSchema.properties,
        ...commonPatch(gpsSchema.properties).properties,
        $addToSet: addToSet([{path: 'rates', type: ObjectIdSchema()}]),
        $pull: pull([{path: 'rates', type: ObjectIdSchema()}])
    }
} as const
export type GpsPatch = FromSchema<typeof gpsPatchSchema>
export const gpsPatchValidator = getValidator(gpsPatchSchema, dataValidator)
export const gpsPatchResolver = resolve<GpsPatch, HookContext>({})

// Schema for allowed query properties
export const gpsQuerySchema = {
    $id: 'GpsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(gpsSchema.properties),
        companyName: {},
        planName: {},
        place: {}
    }
} as const
export type GpsQuery = FromSchema<typeof gpsQuerySchema>
export const gpsQueryValidator = getValidator(gpsQuerySchema, queryValidator)
export const gpsQueryResolver = resolve<GpsQuery, HookContext>({})
