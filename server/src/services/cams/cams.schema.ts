// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, commonQueries, exists} from '../../utils/common/schemas.js';
import {compsCamsSchema} from '../comps/comps.schema.js';

// Main data model schema
export const camsSchema = {
    $id: 'Cams',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'org', 'person'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        comp: ObjectIdSchema(),
        hireDate: {},
        hoursWorked: { type: 'number' },
        off: { type: 'boolean' },
        stage: { type: 'string' },
        active: { type: 'string' },
        group: ObjectIdSchema(),
        terminated: { type: 'boolean' },
        terminatedAt: {},
        terminatedBy: ObjectIdSchema(),
        ...compsCamsSchema.properties,
        ...commonFields.properties
    }
} as const
export type Cams = FromSchema<typeof camsSchema>
export const camsValidator = getValidator(camsSchema, dataValidator)
export const camsResolver = resolve<Cams, HookContext>({})

export const camsExternalResolver = resolve<Cams, HookContext>({})

// Schema for creating new data
export const camsDataSchema = {
    $id: 'CamsData',
    type: 'object',
    additionalProperties: false,
    required: ['person', 'org'],
    properties: {
        ...camsSchema.properties
    }
} as const
export type CamsData = FromSchema<typeof camsDataSchema>
export const camsDataValidator = getValidator(camsDataSchema, dataValidator)
export const camsDataResolver = resolve<CamsData, HookContext>({})

// Schema for updating existing data
export const camsPatchSchema = {
    $id: 'CamsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...camsSchema.properties,
        ...commonPatch(camsSchema.properties).properties,
        $inc: {}
    }
} as const
export type CamsPatch = FromSchema<typeof camsPatchSchema>
export const camsPatchValidator = getValidator(camsPatchSchema, dataValidator)
export const camsPatchResolver = resolve<CamsPatch, HookContext>({})

// Schema for allowed query properties
export const camsQuerySchema = {
    $id: 'CamsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({
            ...camsSchema.properties,
            ...commonQueries.properties,
            name: {},
            ...exists(['extras', 'comp'], camsSchema.properties)
        })
    }
} as const
export type CamsQuery = FromSchema<typeof camsQuerySchema>
export const camsQueryValidator = getValidator(camsQuerySchema, queryValidator)
export const camsQueryResolver = resolve<CamsQuery, HookContext>({})
