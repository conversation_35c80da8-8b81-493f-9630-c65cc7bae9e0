// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';

// Main data model schema
export const salesTaxesSchema = {
    $id: 'SalesTaxes',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'postal_code', 'taxes', 'state'],
    properties: {
        _id: ObjectIdSchema(),
        country: {type: 'string'}, //two letter
        city: {type: 'string'},
        state: {type: 'string'}, //two letter
        postal_code: {type: 'string'},
        total_tax: {type: 'number'},
        taxes: {
            type: 'array', items: {
                type: 'object',
                properties: {
                    name: {type: 'string'},
                    rate: {type: 'number'},
                    type: {type: 'string', enum: ['percent', 'flat']}
                }
            }
        },
        expires: { type: 'string' },
        ...commonFields.properties
    }
} as const

export type SalesTaxes = FromSchema<typeof salesTaxesSchema>
export const salesTaxesValidator = getValidator(salesTaxesSchema, dataValidator)
export const salesTaxesResolver = resolve<SalesTaxes, HookContext>({})

export const salesTaxesExternalResolver = resolve<SalesTaxes, HookContext>({})

// Schema for creating new data
export const salesTaxesDataSchema = {
    $id: 'SalesTaxesData',
    type: 'object',
    additionalProperties: false,
    required: ['postal_code', 'taxes', 'state'],
    properties: {
        ...salesTaxesSchema.properties
    }
} as const
export type SalesTaxesData = FromSchema<typeof salesTaxesDataSchema>
export const salesTaxesDataValidator = getValidator(salesTaxesDataSchema, dataValidator)
export const salesTaxesDataResolver = resolve<SalesTaxesData, HookContext>({})

// Schema for updating existing data
export const salesTaxesPatchSchema = {
    $id: 'SalesTaxesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...salesTaxesSchema.properties,
        ...commonPatch(salesTaxesSchema.properties).properties
    }
} as const
export type SalesTaxesPatch = FromSchema<typeof salesTaxesPatchSchema>
export const salesTaxesPatchValidator = getValidator(salesTaxesPatchSchema, dataValidator)
export const salesTaxesPatchResolver = resolve<SalesTaxesPatch, HookContext>({})

// Schema for allowed query properties
export const salesTaxesQuerySchema = {
    $id: 'SalesTaxesQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(salesTaxesSchema.properties)
    }
} as const
export type SalesTaxesQuery = FromSchema<typeof salesTaxesQuerySchema>
export const salesTaxesQueryValidator = getValidator(salesTaxesQuerySchema, queryValidator)
export const salesTaxesQueryResolver = resolve<SalesTaxesQuery, HookContext>({})
