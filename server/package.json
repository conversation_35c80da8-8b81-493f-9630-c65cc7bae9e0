{"name": "commoncare_server", "description": "CommonCare Server", "version": "0.0.0", "homepage": "", "private": true, "keywords": ["feathers"], "type": "module", "author": {"name": "tyler hall", "email": "{49353034}+{ha6755ad}@users.noreply.github.com"}, "contributors": [], "bugs": {}, "engines": {"node": ">= 20.13.1", "npm": "^10.8.3"}, "feathers": {"language": "ts", "packager": "npm", "database": "mongodb", "framework": "express", "transports": ["rest", "websockets"], "schema": "json-schema"}, "directories": {"lib": "src", "test": "test"}, "files": ["lib/client.js", "lib/**/*.d.ts", "lib/**/*.shared.js"], "main": "lib/client", "scripts": {"test": "npm run lint && npm run mocha", "compile": "shx rm -rf lib/ && tsc", "dev": "tsc --noEmit && node --trace-warnings --loader ts-node/esm src/index.ts", "start": "node lib/index.js", "mocha": "mocha test/ --recursive --exit", "bundle:client": "npm run compile && npm pack --pack-destination ./public", "build": "npm install --production=false && npm run compile && npm ci"}, "standard": {"env": ["mocha"], "ignore": []}, "dependencies": {"@aws-sdk/client-s3": "^3.726.1", "@aws-sdk/s3-request-presigner": "^3.726.1", "@feathersjs/authentication": "^5.0.31", "@feathersjs/authentication-client": "^5.0.31", "@feathersjs/authentication-local": "^5.0.31", "@feathersjs/authentication-oauth": "^5.0.31", "@feathersjs/configuration": "^5.0.31", "@feathersjs/errors": "^5.0.31", "@feathersjs/express": "^5.0.31", "@feathersjs/feathers": "^5.0.31", "@feathersjs/mongodb": "^5.0.31", "@feathersjs/schema": "^5.0.31", "@feathersjs/socketio": "^5.0.31", "@feathersjs/transport-commons": "^5.0.31", "@moovio/node": "^1.2.2", "@moovio/sdk": "^0.14.18", "@sendgrid/mail": "^8.1.4", "@simplewebauthn/server": "^13.1.2", "@socket.io/redis-adapter": "^8.3.0", "@turf/turf": "^7.2.0", "@ucans/ucans": "^0.12.0", "awesome-phonenumber": "^7.2.0", "axios": "^1.4.0", "compression": "^1.7.5", "cors": "^2.8.5", "dauria": "^2.0.0", "eciesjs": "^0.4.13", "feathers-batch": "^1.1.1", "feathers-hooks-common": "^8.2.1", "feathers-ucan": "^0.1.0", "form-data": "^4.0.1", "formdata-node": "^6.0.3", "fs-blob-store": "^6.0.0", "geoip-lite": "^1.4.10", "helmet": "^8.0.0", "leven": "^4.0.0", "long-timeout": "^0.1.1", "mongodb": "^6.12.0", "mongoose": "^8.9.4", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "node-html-markdown": "^1.3.0", "node-xlsx": "^0.24.0", "npm": "^11.0.0", "openai": "^4.100.0", "pdf-to-png-converter": "^3.6.4", "psl": "^1.15.0", "puppeteer": "^24.0.0", "radash": "^12.1.0", "redis": "^5.8.2", "s3-blob-store": "^4.1.1", "sanitize-html": "^2.14.0", "serve-favicon": "^2.5.0", "showdown": "^2.1.0", "symbol-ucan": "^0.0.7", "tesseract-ocr": "^1.0.0", "tunnel-ssh": "^5.2.0", "tweetnacl": "^1.0.3", "twilio": "^5.4.2", "uint8arrays": "^5.1.0", "uninstall": "^0.0.0", "uuid": "^11.0.5", "winston": "^3.17.0", "winston-console-format": "^1.0.8"}, "devDependencies": {"@feathersjs/cli": "^5.0.31", "@feathersjs/rest-client": "^5.0.31", "@types/mocha": "^10.0.10", "@types/node": "^22.10.10", "axios": "^1.7.9", "cross-env": "^7.0.3", "mocha": "^11.0.1", "nodemon": "^3.1.9", "prettier": "^3.4.2", "shx": "^0.3.4", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}