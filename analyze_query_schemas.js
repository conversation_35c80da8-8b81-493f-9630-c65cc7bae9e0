#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const schemaFiles = [
    'server/src/services/ai-chats/ai-chats.schema.ts',
    'server/src/services/bank-accounts/bank-accounts.schema.ts',
    'server/src/services/bill-erasers/bill-erasers.schema.ts',
    'server/src/services/bills/bills.schema.ts',
    'server/src/services/budgets/budgets.schema.ts',
    'server/src/services/bundles/bundles.schema.ts',
    'server/src/services/calendars/calendars.schema.ts',
    'server/src/services/cams/cams.schema.ts',
    'server/src/services/caps/caps.schema.ts',
    'server/src/services/care-accounts/care-accounts.schema.ts',
    'server/src/services/cares/cares.schema.ts',
    'server/src/services/cats/cats.schema.ts',
    'server/src/services/challenges/challenges.schema.ts',
    'server/src/services/change-logs/change-logs.schema.ts',
    'server/src/services/claim-payments/claim-payments.schema.ts',
    'server/src/services/claim-reqs/claim-reqs.schema.ts',
    'server/src/services/claims/claims.schema.ts',
    'server/src/services/cobras/cobras.schema.ts',
    'server/src/services/comps/comps.schema.ts',
    'server/src/services/conditions/conditions.schema.ts',
    'server/src/services/contracts/contracts.schema.ts',
    'server/src/services/coverages/coverages.schema.ts',
    'server/src/services/cross-sections/cross-sections.schema.ts',
    'server/src/services/doc-requests/doc-requests.schema.ts',
    'server/src/services/doc-templates/doc-templates.schema.ts',
    'server/src/services/drops/drops.schema.ts',
    'server/src/services/enrollments/enrollments.schema.ts',
    'server/src/services/errs/errs.schema.ts',
    'server/src/services/expenses/expenses.schema.ts',
    'server/src/services/fb-res/fb-res.schema.ts',
    'server/src/services/fbs/fbs.schema.ts',
    'server/src/services/fingerprints/fingerprints.schema.ts',
    'server/src/services/flow-charts/flow-charts.schema.ts',
    'server/src/services/funds/funds.schema.ts',
    'server/src/services/funds-requests/funds-requests.schema.ts',
    'server/src/services/gps/gps.schema.ts',
    'server/src/services/groups/groups.schema.ts',
    'server/src/services/grp-mbrs/grp-mbrs.schema.ts',
    'server/src/services/health-shares/health-shares.schema.ts',
    'server/src/services/hosts/hosts.schema.ts',
    'server/src/services/households/households.schema.ts',
    'server/src/services/ims/ims.schema.ts',
    'server/src/services/issues/issues.schema.ts',
    'server/src/services/junk-drawers/junk-drawers.schema.ts',
    'server/src/services/leads/leads.schema.ts',
    'server/src/services/ledgers/ledgers.schema.ts',
    'server/src/services/logins/logins.schema.ts',
    'server/src/services/markets/markets.schema.ts',
    'server/src/services/mbrs/mbrs.schema.ts',
    'server/src/services/meds/meds.schema.ts',
    'server/src/services/networks/networks.schema.ts',
    'server/src/services/offers/offers.schema.ts',
    'server/src/services/orgs/orgs.schema.ts',
    'server/src/services/passkeys/passkeys.schema.ts',
    'server/src/services/pings/pings.schema.ts',
    'server/src/services/plan-docs/plan-docs.schema.ts',
    'server/src/services/plans/plans.schema.ts',
    'server/src/services/ppls/ppls.schema.ts',
    'server/src/services/practitioners/practitioners.schema.ts',
    'server/src/services/price-estimates/price-estimates.schema.ts',
    'server/src/services/prices/prices.schema.ts',
    'server/src/services/procedures/procedures.schema.ts',
    'server/src/services/providers/providers.schema.ts',
    'server/src/services/rates/rates.schema.ts',
    'server/src/services/refs/refs.schema.ts',
    'server/src/services/reqs/reqs.schema.ts',
    'server/src/services/sales-taxes/sales-taxes.schema.ts',
    'server/src/services/se-plans/se-plans.schema.ts',
    'server/src/services/shops/shops.schema.ts',
    'server/src/services/specs/specs.schema.ts',
    'server/src/services/teams/teams.schema.ts',
    'server/src/services/threads/threads.schema.ts',
    'server/src/services/uploads/uploads.schema.ts',
    'server/src/services/visits/visits.schema.ts',
    'server/src/services/wallets/wallets.schema.ts'
];

const results = {};

for (const filePath of schemaFiles) {
    try {
        if (!fs.existsSync(filePath)) continue;
        
        const content = fs.readFileSync(filePath, 'utf8');
        const serviceName = path.basename(path.dirname(filePath));
        
        // Find the querySchema definition
        const querySchemaMatch = content.match(/export const \w+QuerySchema = \{[\s\S]*?\} as const/);
        
        if (querySchemaMatch) {
            const querySchemaText = querySchemaMatch[0];
            
            // Look for properties object
            const propertiesMatch = querySchemaText.match(/properties:\s*\{([\s\S]*?)\}/);
            
            if (propertiesMatch) {
                const propertiesContent = propertiesMatch[1];
                
                // Skip if it only contains querySyntax and commonQueries
                if (propertiesContent.includes('...querySyntax') && 
                    !propertiesContent.match(/[a-zA-Z_][a-zA-Z0-9_]*:\s*\{/)) {
                    continue;
                }
                
                console.log(`\n=== ${serviceName} ===`);
                console.log('QuerySchema properties:');
                console.log(propertiesContent.trim());
            }
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}
