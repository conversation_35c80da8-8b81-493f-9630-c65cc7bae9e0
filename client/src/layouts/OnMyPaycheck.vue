<template>
  <q-layout>
    <q-header>
      <q-toolbar :class="`__tool pw2 ${isScrolled ? '__shrink' : ''}`">
        <div class="_fa row items-center">
          <q-btn @click="drawer = !drawer" v-if="$q.screen.lt.md" class="q-mr-sm text-primary" dense flat
                 icon="mdi-menu"></q-btn>
          <div v-if="!isAuthenticated" class="__img _hov cursor-pointer" @click="router.push('/')">
            <q-img class="_fa" fit="contain" :src="logo"></q-img>
          </div>
          <div class="col-shrink">
            <org-context-item v-if="isAuthenticated"></org-context-item>
          </div>
          <div class="col-grow">
            <div v-if="$q.screen.gt.md" class="row justify-center items-center">

              <q-chip
                  class="q-mx-sm"
                  @pointerenter="toggleHover(link.key)"
                  @pointerleave="toggleHover('', link.key)"
                  dense
                  square
                  clickable
                  color="transparent"
                  :text-color="link.active ? 'primary' : ''"
                  v-for="(link, i) in nav" :key="`link-${i}`" @click="link.click()">
                <span class="tw-five text-xs">{{ link.label }}</span>
                <template v-if="link.items">
                  <q-icon class="q-ml-xs" name="mdi-chevron-down"></q-icon>

                  <q-menu
                      style="background: transparent"
                      :offset="[0, 20]"
                      @pointerenter="toggleHover(link.key)"
                      @pointerleave="toggleHover('', link.key)"
                      :model-value="hover === link.key" @update:model-value="val => val ? hover = item.key : ''">
                    <div class="__menu">
                      <q-list>
                        <q-item v-for="(sub, idx) in link.items.filter(a => !a.hide)" :key="`sub-${i}-${idx}`" clickable
                                @click="sub.click" :class="sub.active ? 'bg-p0' : ''">
                          <q-item-section avatar>
                            <q-icon :name="sub.icon" :color="sub.active ? 'p9' : 'p4'"></q-icon>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label :class="`tw-five text-${sub.active ? 'p9' : 'p10'}`">{{ sub.label }}
                            </q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-menu>
                </template>
              </q-chip>

            </div>
          </div>
          <div class="col-shrink">
            <profile-button
            ></profile-button>
          </div>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="drawer" bordered class="__drawer">
      <div class="_fw q-pa-sm">
        <div class="q-pa-md">
          <q-img class="__img _hov cursor-pointer" fit="contain" :src="logo" @click="router.push('/')"></q-img>
        </div>
        <q-list>
          <template v-for="(item, i) in nav" :key="`d-${i}`">
            <q-expansion-item dense :hide-expand-icon="!item.items" group="drawer" expand-icon="mdi-menu-down"
                              @update:model-value="item.click()">
              <template v-slot:header>
                <q-item class="_fw">
                  <q-item-section avatar>
                    <q-icon :color="item.active ? 'primary' : ''" :name="item.icon"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label :class="`tw-six text-${item.active ? 'primary' : 'p10'}`">{{ item.label }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <q-list separator class="bg-p0">
                <q-item v-for="(sub, idx) in (item.items || []).filter(a => !a.hide)" :key="`sub-${i}-${idx}`" clickable
                        @click="sub.click" :class="sub.active ? 'bg-p0' : ''">
                  <q-item-section avatar>
                    <q-icon :name="sub.icon" :color="sub.active ? 'p9' : 'p4'"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label :class="`tw-five text-${sub.active ? 'secondary' : 'p10'}`">{{ sub.label }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-expansion-item>

          </template>

        </q-list>
      </div>
    </q-drawer>


    <q-page-container>
      <router-view/>
    </q-page-container>
  </q-layout>
</template>

<script setup>
  import logo from 'src/assets/paycheck_logo.svg'
  import ProfileButton from 'layouts/utils/ProfileButton.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';

  import {computed, onMounted, ref} from 'vue';
  import {toolScroll} from 'layouts/utils/tool-scroll';
  import {$setCssVar, fakeId} from 'src/utils/global-methods';
  import {visitorPrints} from 'layouts/utils/prints';
  import {trackContext} from 'layouts/utils/track-context';
  import {useRouter} from 'vue-router';
  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {useEnvStore} from 'stores/env';

  const router = useRouter();

  const orgStore = useOrgs();
  const envStore = useEnvStore();

  const { isAuthenticated, login } = loginPerson();

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => envStore.getOrgId)
  })

  const hover = ref('');
  const hoverTo = ref()
  const toggleHover = (val, check) => {
    clearTimeout(hoverTo.value)
    if (val) {
      hover.value = val;
    } else if (check) {
      hoverTo.value = setTimeout(() => {
        if (hover.value === check) hover.value = ''
      }, 250)
    }
  }

  const orgId = computed(() => org.value._id || fakeId)
  const { canEdit } = clientCanU({
    subject: org,
    login,
    caps: [[`orgs:${orgId.value}`, 'WRITE'], [`orgs:${orgId.value}`, 'orgAdmin']],
    loginPass: [[['updatedBy.login'], '*']],
    cap_subjects: computed(() => [org.value._id])
  })

  const { route } = visitorPrints({})
  trackContext()

  const { isScrolled } = toolScroll({ breakpoint: 100 })

  const drawer = ref(false);


  const nav = computed(() => [
    {
      key: 'company',
      label: 'My Company',
      icon: 'mdi-file-document',
      active: route.meta?.name === 'company',
      click: () => hover.value = 'company',
      items: [
        {
          key: 'employees',
          icon: 'mdi-face-man',
          label: 'Employees',
          hide: !canEdit.value.ok,
          active: route.meta?.name === 'employees',
          click: () => router.push({ name: 'employees' })
        },
        {
          icon: 'mdi-file-document',
          label: 'Agreements',
          active: route.meta?.name === 'agreements',
          click: () => router.push({ name: 'agreements' })
        },
        {
          icon: 'mdi-receipt',
          label: 'Comp Statements'
        }
      ]
    },
    {
      key: 'offers',
      label: 'Offers',
      icon: 'mdi-tag'
    },
    {
      key: 'surveys',
      label: 'Surveys',
      icon: 'mdi-help-box-multiple'
    },
    {
      key: 'about',
      label: 'About',
      icon: 'mdi-information-box'
    }
  ])

  onMounted(() => {
    const map = {
      '--q-primary': '#4CA9E4',
      "--q-p0": "#EDF7FC",
      "--q-p1": "#DCEFF9",
      "--q-p2": "#CBE7F6",
      "--q-p3": "#A0D1F0",
      "--q-p4": "#71BBEA",
      "--q-p5": "#4CA9E4",
      "--q-p6": "#2A9ADF",
      "--q-p7": "#154C7A",
      "--q-p8": "#0C2B44",
      "--q-p9": "#0F3757",
      "--q-p10": "#092134",
      "--q-p11": "#061623",
      "--q-p12": "#030B11",

      "--q-secondary": "#EF767A",
      "--q-s0": "#FDEBEC",
      "--q-s1": "#FBD6D8",
      "--q-s2": "#F8C0C3",
      "--q-s3": "#F6AAB0",
      "--q-s4": "#F29094",
      "--q-s5": "#EF767A",
      "--q-s6": "#DC5F64",
      "--q-s7": "#B9464A",
      "--q-s8": "#9C3C3D",
      "--q-s9": "#7D2E30",
      "--q-s10": "#561E20",
      "--q-s11": "#3B1314",
      "--q-s12": "#1F0809",


      "--q-accent": "#D8B861",
      "--q-a0": "#FBF9F2",
      "--q-a1": "#F6EFD9",
      "--q-a2": "#F1E6BD",
      "--q-a3": "#EBDCA2",
      "--q-a4": "#E3CE7E",
      "--q-a5": "#D8B861",
      "--q-a6": "#C2A24F",
      "--q-a7": "#A3873F",
      "--q-a8": "#897035",
      "--q-a9": "#6F5C2B",
      "--q-a10": "#52451F",
      "--q-a11": "#3A3015",
      "--q-a12": "#1F180B"
    }

    const el = document.body
    for (const k in map) {
      $setCssVar(k, map[k], el)
    }
  })

</script>

<style lang="scss" scoped>

  .__tool {
    height: 80px;
    transition: all .4s;
    background: white;
    border-bottom: solid 1px var(--q-primary);
  }

  .__drawer {
    width: 300px;
    max-width: 85vw;
    background: white;
  }

  .__shrink {
    height: 40px;
  }

  .__img {
    height: 60%;
    width: 250px;
    max-width: 35vw;
  }

  .__menu {
    width: 400px;
    max-width: 95vw;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .2);
    background: rgba(255, 255, 255, .5);
    backdrop-filter: blur(10px);
  }


</style>
