<template>
  <div>
    <div class="flex items-center">
      <q-chip color="transparent" clickable @click="toggleActive">
        <q-icon color="primary" name="mdi-filter" size="20px"></q-icon>
        <span class="q-ml-sm font-3-4r">Filters</span>
        <q-icon name="mdi-menu-down"></q-icon>
      </q-chip>
      <q-btn size="xs" v-if="dirty" flat dense color="red" icon="mdi-close" @click="clearAll"></q-btn>
      <div class="flex items-center" v-for="(k, i) in Object.keys(query)"
           :key="k">
        <template v-if="filterConfig[k]?.label">
          <q-chip
              dense color="transparent"
              :class="`__chip ${filterConfig[k]?.count ? '__on' : ''}`"
              clickable
              @click="toggleActive(k)"
              :label="`${filterConfig[k]?.label}${filterConfig[k]?.count ? ` (${filterConfig[k]?.count})` : ''}`"
          ></q-chip>
          <div v-if="i < Object.keys(filterConfig).length - 1" class="text-grey-6">|</div>
        </template>
      </div>

    </div>
    <q-slide-transition>
      <div class="_fw mw800 q-pa-md" v-if="!!active">
        <div class="_form_grid">
          <template v-for="(k, i) in Object.keys(filterConfig)" :key="`filter-${i}`">
            <template v-if="filterConfig[k] && !filterConfig[k].hide">
              <div class="_form_label">{{ filterConfig[k].label }}</div>
              <div class="q-pa-sm">
                <component
                    :is="filterConfig[k].component"
                    v-bind="{
                        plan: fullPlan,
                        dense: true,
                        modelValue: filters[k],
                        picker: true,
                        multiple: true,
                        emitValue: true,
                        ...filterConfig[k].attrs
                      }"
                    @update:model-value="setFilter(k, $event)"
                ></component>
              </div>
            </template>
          </template>
          <slot name="fields"></slot>
        </div>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>

  import {careFilters} from 'components/care/utils/filters';
  import {computed, nextTick, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {idGet} from 'src/utils/id-get';

  const planStore = usePlans();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    plan: { required: true },
    modelValue: { required: true },
    set: { required: true }
  })

  const active = ref('');
  const toggleActive = (val) => {
    if (!active.value) active.value = val || '*';
    else active.value = '';
  }

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  })


  const query = computed(() => {
    return { ...props.modelValue }
  })

  const emitUp = (path, val) => {
    emit('update:model-value', val);
  }

  const { filters, filterConfig, setFilter, dirty, clearAll } = careFilters(query, { emit: emitUp, set: props.set })
</script>

<style lang="scss" scoped>
  .__chip {
    color: #666;
    font-size: .8rem;
  }

  .__on {
    font-weight: 500;
    color: var(--q-p9);
  }
</style>
