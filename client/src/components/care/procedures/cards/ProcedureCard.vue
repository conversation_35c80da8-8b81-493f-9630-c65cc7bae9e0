<template>
  <table>

  </table>
</template>

<script setup>
  import {useConditions} from 'stores/conditions';
  import {idGet} from 'src/utils/id-get';

  const store = useConditions();
  const props = defineProps({
    modelValue: { required: true}
  })

  const { item:condition } = idGet({
    store,
    value: computed(() => props.modelValue)
  })
</script>

<style lang="scss" scoped>

  table {
    width: 100%;
    border-collapse: collapse;
    tr {
      td {
        padding: 5px 8px;
        border-bottom: solid .3px #999;
      }
    }
    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
</style>
