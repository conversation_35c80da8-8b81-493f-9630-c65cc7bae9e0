<template>
  <div class="_fw w400 mw100">
    <q-select
        ref="proAuto"
        v-bind="{
    useInput: true,
    options:h$.data,
    placeholder: 'Search procedures & supplies...',
    hint: search.text ? `${h$.total || 0} likely matches found` : undefined,
    modelValue: item,
    class: $attrs?.selectClass,
    ...$attrs
      }"
        @update:model-value="emit('update:model-value', $event)"
        @input-value="setSearch"
        @focus="setFocus"
        @blur="setBlur"
    >
      <template v-slot:prepend>
        <slot name="prepend">
          <q-spinner color="primary" v-if="h$.isPending"></q-spinner>
        </slot>
      </template>
      <template v-slot:option="scope">
        <procedure-item
            :model-value="scope.opt"
            @click="scope.toggleOption(scope.opt)"
        ></procedure-item>
      </template>
    </q-select>
  </div>
</template>

<script setup>
  import ProcedureItem from 'components/care/procedures/cards/ProcedureItem.vue';

  import {useProcedures} from 'stores/procedures';
  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {idGet} from 'src/utils/id-get';

  const store = useProcedures();

  const emit = defineEmits(['update:model-value', 'input-value', 'focus', 'blur']);
  const props = defineProps({
    modelValue: { required: false },
    searchDefault: { required: false },
    multiple: Boolean,
    params: Object
  })

  const { item } = idGet({
    store,
    value: computed(() => Array.isArray(props.modelValue) ? undefined : props.modelValue),
    def: ''
  })

  const focused = ref(false);
  const setFocus = () => {
    focused.value = true;
    emit('focus')
  }
  const setBlur = () => {
    focused.value = false;
    emit('blur');
  }

  const search = ref({ text: '' });
  const useSearch = computed(() => {
    const isName = !/.*\d.*\d.*/.test(search.value.text);
    return {
      text: search.value.text,
      keys: isName ? ['name', 'layName'] : ['code']
    }
  })
  const setSearch = (val) => {
    search.value.text = val;
    emit('input-value', val);
  }

  const { searchQ } = HQuery({ search: useSearch })

  const { h$ } = HFind({
    store,
    pause: computed(() => !search.value.text),
    params: computed(() => {
      return {
        ...props.params,
        query: {
          ...searchQ.value,
          ...props.params?.query
        }
      }
    })
  })


</script>

<style lang="scss" scoped>

</style>
