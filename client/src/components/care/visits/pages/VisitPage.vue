<template>
  <div class="_fw">
    <template v-if="canEdit.ok">
      <div class="row items-center">
        <member-chip show-age :model-value="visit?.patient"></member-chip>
        <q-space></q-space>
        <q-btn flat no-caps @click="editDialog = true">
          <span class="q-mr-sm" v-if="$q.screen.gt.sm">Edit Visit</span>
          <q-icon color="accent" name="mdi-pencil-box"></q-icon>
        </q-btn>
      </div>
      <div class="q-pt-sm">
        <claim-payment-button :disable="context === 'provider'" :payer="context" :visit="visit" flat rounded>
          <span class="tw-six text-grey-8 font-3-4r">Balance:&nbsp;</span>
          <span class="tw-six font-1r text-primary">{{dollarString((visit?.balance || 0)/100, '$', 2)}}</span>
        </claim-payment-button>
      </div>
      <div v-if="removeRequest">
        <q-chip color="transparent">
          <q-avatar size="18px" :color="claimRequestStatus[removeRequest.status]?.color"></q-avatar>
          <span class="q-ml-xs">Removal Request Status: {{ claimRequestStatus[removeRequest.status]?.label }}</span>
        </q-chip>
      </div>
      <div class="row" v-if="visit">
        <div class="col-12 col-md-6 q-pa-sm">
          <div class="__c">
            <div class="__title">Bills</div>
            <claims-list sync adding table-layer :visit="visit" :claim-in="$route.params.claimId"></claims-list>
          </div>
          <div class="__c">
            <div class="__title">Payments</div>
            <template v-if="context !== 'provider'">
              <div class="row q-py-sm">
                <claim-payment-button
                    :disable="context === 'provider'"
                    :payer="context"
                    :visit="visit"
                    rounded
                    size="sm"
                    flat
                >
                  <span class="q-mr-xs">New</span>
                  <q-icon color="primary" name="mdi-plus"></q-icon>
                </claim-payment-button>
              </div>
            </template>
            <claim-payment-table :visit="visit"></claim-payment-table>
          </div>
          <div class="__c">
            <div class="__title">Pending Bills</div>
            <claims-request-display
                align="right"
                :visit-id="visit._id"
            ></claims-request-display>
          </div>
          <div class="__c">
            <div class="__title">Visit Details</div>
            <div class="_form_grid _f_g_r">
              <div class="_form_label">Status</div>
              <div>
                <visit-status-chip picker :visit="visit"></visit-status-chip>
              </div>
              <div class="_form_label">Date</div>
              <div>
                <q-chip color="transparent">
                  {{
                    formatDate(visit.date, 'ddd MMM DD, YYYY')
                  }}{{ visit.endDate ? `- ${formatDate(visit.endDate, 'ddd MMM dd, YYYY')}` : '' }}
                </q-chip>
              </div>
              <div class="_form_label">Provider</div>
              <div>
                <provider-item :model-value="visit.provider"></provider-item>
              </div>
            </div>
          </div>

        </div>
        <div class="col-12 col-md-6 q-pa-sm">
          <div class="__c">
            <div class="__title">Conditions</div>
            <div class="q-py-sm">
              <conditions-manager
                  :model-value="visit.conditions"
                  @update:one="addCondition($event, visit)"
                  @remove:one="removeCondition($event, visit)"
              ></conditions-manager>
            </div>
          </div>
          <div class="__c">
            <div class="__title">Practitioners</div>
            <div class="_fw">
              <q-list separator>
                <q-item-label header v-if="!p$.total">
                  <span class="text-italic">None Assigned</span>
                </q-item-label>
                <add-practitioner-item @update:model-value="addPractitioner"></add-practitioner-item>
                <q-item v-for="(p, i) in p$.data" :key="`p-${i}`">
                  <practitioner-item :model-value="p"></practitioner-item>
                </q-item>
              </q-list>
            </div>
          </div>
          <div class="__c">
            <div class="__title">Communication</div>
            <thread-form :parent="{ id: visit._id, service: 'visits' }"></thread-form>
            <show-threads empty-message="No Conversations" :threads="visit.threads"></show-threads>

          </div>
        </div>
      </div>

      <common-dialog setting="right" v-model="editDialog">
        <div class="_fw bg-white q-pa-md">
          <div v-if="!iAdded" class="font-1r q-pa-md">
            You did not add this visit - you can add details, but you cannot edit what was added by others.<br><br>If
            there is a problem with this data, you can <span class="cursor-pointer tw-six text-accent">request it be removed
          <q-menu>
            <div class="w200 bg-white q-pa-md">
              <div class="font-7-8r">Request visit be removed?</div>
              <div class="row items-center justify-end">
                <q-btn flat no-caps @click="requestRemove">
                  <span class="q-mr-sm">Confirm</span>
                  <q-icon dense flat name="mdi-check" color="green"></q-icon>
                </q-btn>
              </div>
            </div>
          </q-menu>
        </span>.
          </div>
          <visit-form v-else @update:model-value="editDialog = false" :model-value="visit"></visit-form>
        </div>
      </common-dialog>
    </template>
    <template v-else>
      <loading-status></loading-status>
    </template>

  </div>
</template>

<script setup>
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import AddPractitionerItem from 'components/practitioners/cards/AddPractitionerItem.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import VisitForm from 'components/care/visits/forms/VisitForm.vue';
  import ShowThreads from 'components/threads/cards/ShowThreads.vue';
  import ThreadForm from 'components/threads/forms/ThreadForm.vue';
  import PractitionerItem from 'components/practitioners/cards/PractitionerItem.vue';
  import ClaimsList from 'components/claims/lists/ClaimsList.vue';
  import ClaimsRequestDisplay from 'components/claims/claim-requests/cards/ClaimsRequestDisplay.vue';
  import ConditionsManager from 'components/care/conditions/forms/ConditionsManager.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import LoadingStatus from 'components/utils/LoadingStatus.vue';
  import VisitStatusChip from 'components/care/visits/cards/VisitStatusChip.vue';
  import ClaimPaymentTable from 'components/claim-payments/lists/ClaimPaymentTable.vue';
  import ClaimPaymentButton from 'components/claim-payments/forms/ClaimPaymentButton.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useRoute} from 'vue-router';
  import {useVisits} from 'stores/visits';
  import {formatDate} from 'src/utils/date-utils';
  import {HFind} from 'src/utils/hFind';
  import {usePractitioners} from 'stores/practitioners';
  import {useClaimReqs} from 'stores/claim-reqs';
  import {claimRequestStatus} from 'src/components/claims/utils';
  import {visitConditions} from '../utils';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {usePlans} from 'stores/plans';
  import {dollarString} from 'src/utils/global-methods';
  const { login } = loginPerson()

  const route = useRoute();
  const practitionerStore = usePractitioners();
  const planStore = usePlans();

  const visitStore = useVisits();
  const { addCondition, removeCondition } = visitConditions(visitStore)
  const crStore = useClaimReqs();

  const props = defineProps({
    context: String //participant, plan, provider
  })

  const editDialog = ref(false);

  const { item: visit } = idGet({
    value: computed(() => route.params.visitId),
    store: visitStore,
    params: ref({ runJoin: { visit_claims: true } })
  })
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => visit.value?.plan),
  })

  const iAdded = computed(() => {
    const { auto, id } = visit.value?.enteredBy || {};
    return !auto && !id;
  })

  const addPractitioner = (val) => {
    visitStore.patchInStore(visit.value._id, { $addToSet: { practitioners: val._id } })
    visitStore.patch(visit.value._id, { $addToSet: { practitioners: val._id } })
        .catch(err => console.log(err));
  }


  const practitionerIds = computed(() => visit.value?.practitioners || [])
  const { h$: p$ } = HFind({
    store: practitionerStore,
    limit: computed(() => practitionerIds.value.length),
    params: computed(() => {
      return {
        query: { _id: { $in: practitionerIds.value }, }
      }
    })
  })

  const removeRequest = ref();
  const requestRemove = async () => {
    const { _id, patient, person, plan, provider } = visit.value;
    removeRequest.value = await crStore.create({ removeRequest: true, visit: _id, person, patient, plan, provider })
    editDialog.value = false;
  }

  const caps = computed(() => {
    return [[`plans:${plan.value?._id}`, ['planAdmin']], [`orgs:${plan.value?.org}`, ['orgAdmin']], [`orgs:${plan.value?.org}`, ['WRITE']], ['orgs', 'WRITE'], [`providers:${visit.value?.provider}`, ['providerAdmin']], [`providers:${visit.value?.provider}`, ['WRITE']]]
  })
  const { canEdit } = clientCanU({
    subject: visit,
    or: true,
    caps,
    login,
    cap_subjects: computed(() => [plan.value._id]),
    loginPass: [[['patient/owner', 'person/owner'], '*']]
  })

  watch(visit, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      const rmv = await crStore.find({ query: { $limit: 1, visit: nv._id, removeRequest: true } })
      if (rmv.total) removeRequest.value = rmv.data[0];
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    padding: 35px 20px 20px 20px;
    position: relative;
    margin-top: 30px;
  }

  .__title {
    position: absolute;
    top: 0;
    left: 5%;
    transform: translate(0, -30%);
    border-radius: 6px;
    background: linear-gradient(170deg, var(--q-p7), var(--q-primary));
    color: white;
    font-weight: 600;
    padding: 6px 8px;
    font-size: 1rem;
  }
</style>
