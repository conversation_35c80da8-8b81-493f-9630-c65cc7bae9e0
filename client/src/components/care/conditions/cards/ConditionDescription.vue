<template>
  <div class="q-pa-md _fw mw400">
    <div class="font-3-4r text-grey-6 tw-six q-py-xs">{{cond?.standard?.toUpperCase()}}: {{cond?.code || 'No Code'}}</div>
    <div class="font-7-8r tw-five q-pb-sm">{{cond?.name || cond?.notes || ''}}</div>
    <div class="font-3-4r text-grey-8">{{cond?.description || 'No added description'}}</div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useConditions} from 'stores/conditions';

  const store = useConditions();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:cond } = idGet({
    store,
    idPath: 'id',
    value: computed(() => props.modelValue)
  })
</script>

<style lang="scss" scoped>

</style>
