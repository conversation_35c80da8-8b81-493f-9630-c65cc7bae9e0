<template>
  <q-item
      v-bind="{
    clickable: true,
    class: '_fw mw600',
    ...$attrs
      }"
      @click="$emit('update:model-value', condition)">
    <q-item-section>
      <q-item-label>{{ condition?.name }}</q-item-label>
      <q-item-label caption v-if="condition?.description">{{ condition.description }}</q-item-label>
    </q-item-section>
  </q-item>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useConditions} from 'stores/conditions';
  import {computed} from 'vue';

  const store = useConditions();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:condition } = idGet({
    store,
    value: computed(() => props.modelValue)
  })
</script>

<style lang="scss" scoped>

</style>
