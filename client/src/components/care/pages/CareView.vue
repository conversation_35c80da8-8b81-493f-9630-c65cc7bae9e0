<template>
  <div class="_fw _bg_ow">

    <template v-if="canEdit.ok">
      <div class="q-py-sm row items-center" v-if="!route.params.visitId">
        <q-input class="w200" dense filled input-class="tw-six font-1r" placeholder="Untitled Care Event"
                 v-model="name" @blur="maybeSetName"></q-input>
        <q-space></q-space>
        <div class="q-py-sm">
          <member-chip :model-value="care.patient"></member-chip>
        </div>
      </div>
      <div class="row items-center" v-if="visit?._id">
        <q-chip square dense color="transparent" clickable
                @click="$router.push({ name: 'care-page', params: { careId: care?._id }})">
          <q-icon color="primary" name="mdi-hospital-box"></q-icon>
          <span class="q-ml-sm">{{ care?.name }}</span>
        </q-chip>
        <div class="q-px-xs">|</div>
        <q-chip square dense color="transparent" clickable
                @click="$router.push({ name: 'visit-page', params: { visitId: visit?._id, careId: route.params.careId }})">
          <q-icon name="mdi-stethoscope" color="primary"></q-icon>
          <span class="q-ml-xs font-7-8r">{{ formatDate(visit.date, 'MM/DD/YYYY') }}</span>
        </q-chip>
        <template v-if="route.params.claimId">
          <div class="q-px-xs">|</div>
          <q-chip square dense color="transparent">
            <q-icon name="mdi-document" color="accent"></q-icon>
            <span class="q-ml-sm">Claim</span>
          </q-chip>

        </template>
      </div>
      <router-view></router-view>
    </template>
    <template v-else>
      <div class="q-pa-lg text-italic">This doesn't appear to be your data to view - if you think it is, contact
        your
        plan administrator
      </div>
    </template>

  </div>

</template>

<script setup>
  import MemberChip from 'components/households/cards/MemberChip.vue';

  import {loginPerson} from 'stores/utils/login';
  import {computed, ref, watch} from 'vue';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {useCares} from 'stores/cares';
  import {idGet} from 'src/utils/id-get';
  import {useRoute} from 'vue-router';
  import {usePlans} from 'stores/plans';
  import {useVisits} from 'stores/visits';
  import {formatDate} from 'src/utils/date-utils';
  const { login } = loginPerson()

  const route = useRoute();
  const careStore = useCares();
  const planStore = usePlans();
  const visitStore = useVisits();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: visit } = idGet({
    value: computed(() => route.params.visitId),
    store: visitStore,
    params: ref({ runJoin: { visit_claims: true } })
  })
  const { item: care } = idGet({
    value: computed(() => visit.value?.care || props.modelValue || route.params.careId),
    store: careStore
  })

  const { item: plan } = idGet({
    value: computed(() => care.value?.plan),
    store: planStore
  })

  const { canEdit } = clientCanU({
    subject: care,
    or: true,
    caps: computed(() => [[`plans:${plan.value?._id}`, ['planAdmin']], [`orgs:${plan.value?.org}`, ['orgAdmin']], [`orgs:${plan.value?.org}`, ['WRITE']], ['orgs', ['WRITE']]]),
    login,
    cap_subjects: computed(() => [plan.value.org]),
    loginPass: [[['patient/owner', 'person/owner'], '*']]
  })

  const name = ref('');
  watch(care, (nv) => {
    if (nv && nv.name !== name.value) name.value = nv.name;
  }, { immediate: true })
  const maybeSetName = async () => {
    if (name.value && name.value !== care.value.name) {
      await careStore.patch(care.value._id, { $set: { name: name.value } })
    }
  }

</script>

<style lang="scss" scoped>
  .__h {
    min-height: 95vh;
  }

  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    padding: 50px 20px 20px 20px;
    position: relative;
    margin-top: 30px;
  }

  .__title {
    position: absolute;
    top: 0;
    left: 5%;
    transform: translate(0, -30%);
    border-radius: 6px;
    background: linear-gradient(170deg, var(--q-p7), var(--q-primary));
    color: white;
    font-weight: 600;
    padding: 6px 8px;
    font-size: 1rem;
  }
</style>
