<template>
  <q-page class="__mp bg-ir-grey-2">
    <div class="row justify-center bg-white q-pt-xl">
      <div class="_sent q-pa-md">

        <div class="flex items-end">
          <image-form
              height="150px"
              width="150px"
              storage="storj"
              :model-value="form"
              file-path="avatar"
              @update:model-value="setImage"
          ></image-form>
          <div class="q-pa-md">
            <q-input placeholder="Your Name" borderless dense hide-bottom-space input-class="font-1-1-2r text-weight-bold" v-model="form.name"
                     @keyup.enter="autoSave('name')" @blur="autoSave('name')">
            </q-input>

            <div>
              <badge-select v-model="form.badges" multiple removable @update:model-value="autoSave('badges')"></badge-select>
            </div>
          </div>
        </div>
        <q-separator></q-separator>
        <q-tabs align="left" no-caps v-model="tab" @update:model-value="setTab">
          <q-tab
              v-for="(t, i) in Object.keys(tabs)"
              :key="`t-${i}`"
              :label="tabs[t].label"
              :name="t"
          ></q-tab>
        </q-tabs>
      </div>
    </div>
    <div class="row justify-center q-py-md">
      <div class="_sent bg-ir-grey-2">
        <q-tab-panels v-model="tab" class="_panel _fw bg-transparent">
          <q-tab-panel v-for="(panel, i) in Object.keys(tabs)"
                       :key="`panel-${i}`" :name="panel" class="_panel bg-transparent">
            <div class="_fw q-pa-md q-my-md _c bg-white" v-for="(component, idx) in tabs[panel].components"
                 :key="`comp-${idx}`">
              <div class="q-pa-sm font-1r text-weight-bold">{{component.label}}</div>
              <component
                  :is="component.component"
                  v-bind="component.attrs"
                  v-model="form"
                  @update:model-value="setForm"
              ></component>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>

  </q-page>
</template>

<script setup>
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';
  import BadgeSelect from 'components/account/profile/badges/BadgeSelect.vue';
  import ProfileContact from 'client/src/components/common/profile/ProfileContact.vue';
  import LoginSettings from 'components/common/profile/settings/LoginSettings.vue';
  import MyOrgs from 'components/orgs/cards/MyOrgs.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {ref, watch, computed, onMounted} from 'vue';
  import {useRoute, useRouter} from 'vue-router';

  const route = useRoute();
  const router = useRouter();

  import {usePpls} from 'stores/ppls';

  const store = usePpls();

  import {loginPerson} from 'stores/utils/login';
  const { person } = loginPerson()

  const tab = ref('account');

  const { form } = HForm({
    name: 'Multi-Profile',
    store,
    value: person,
    notify: false,
    validate: true,
    vOpts: ref({
      name: { name: 'Name', v: ['notEmpty'] }
    })
  })

  const { autoSave } = HSave({ form, store, log: false })

  const tabs = computed(() => {
    return {
      'account': {
        label: 'Account',
        components: [
          {
            label: 'Contact Info',
            component: ProfileContact,
            attrs: {
              store,
              modelValue: form.value
            }
          },
          {
            label: 'Wallet'
          },
          {
            label: 'Login',
            component: LoginSettings
          }
        ]
      },
      'groups': {
        label: 'Groups',
        components: [
          {
            component: MyOrgs
          }
        ]
      }
    };
  })

  const setImage = (val) => {
    form.value.avatar = val;
    autoSave('avatar');
  }



  const setForm = (val) => {
    if (val) form.value = val;
  }

  const setTab = (t) => {
    const {href} = router.resolve({...route, params: {tab: t }});
    window.history.pushState({}, '', href)
  };

  const routeTab = computed(() => {
    return route.params.tab;
  })

  watch(routeTab, (nv) => {
    if(nv && nv) tab.value = nv;
  });
  onMounted(() => {
    if (route.params.tab) tab.value = route.params.tab;
  })


  // const
</script>

<style lang="scss" scoped>
  .__mp {
    min-height: 90vh;
  }

</style>
