<template>
  <div class="_fw">
    <div class="row justify-center bg-ir-bg2">
      <div class="_cent pd5 pw2 bg-white">
        <q-tabs align="left" v-model="tab" inline-label>
          <q-tab v-for="(t, i) in Object.keys(tabs)" :key="`t-${i}`" :label="tabs[t].label" :name="t" :icon="tabs[t].icon">
          </q-tab>
        </q-tabs>
        <q-tab-panels v-model="tab" animated>
          <q-tab-panel v-for="(t, i) in Object.keys(tabs)" :key="`p-${i}`" :name="t">
            <component :is="tabs[t].component" v-bind="tabs[t].attrs"></component>
          </q-tab-panel>
        </q-tab-panels>

      </div>
    </div>

  </div>
</template>

<script setup>
  import CamsPage from 'components/comps/cams/pages/CamsPage.vue';
  import {storeToRefs} from 'pinia';
  import {useEnvStore} from 'stores/env';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {computed, onMounted, ref} from 'vue';
  import {useRoute} from 'vue-router';

  const envStore = useEnvStore();

  const route = useRoute()

  const { getOrgId } = storeToRefs(envStore)
  const orgStore = useOrgs();

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const tab = ref('pay');
  const tabs = computed(() => {
    return {
      'pay': {
        label: 'Pay',
        icon: 'mdi-cash',
        component: CamsPage,
        attrs: {
          org: org.value
        }
      }
    }
  })

  onMounted(() => {
    if(route.params.tab) tab.value = route.params.tab;
  })
</script>

<style lang="scss" scoped>

</style>
