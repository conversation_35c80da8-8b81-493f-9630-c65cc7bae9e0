<template>
  <div v-bind="{class: '_fw', ...divAttrs}">
    <div v-for="(row, i) in rows" :key="`row-${i}`">
      <div class="text-0-7">{{ row.label }}</div>
      <div class="row _fw items-center">
        <div class="col-11">
          <div class="text-1">{{ row.field(item) }}</div>
        </div>
        <div class="col-1">
          <q-btn color="primary" size="sm" flat dense v-if="row.copy" icon="mdi-content-copy" class="q-ml-sm"
                 @click="copy(row.copyVal(item))"></q-btn>
        </div>
      </div>
      <q-separator class="q-my-xs"></q-separator>
    </div>
    <div>
      <div class="text-0-7">Message</div>
      <div class="text-0-8">{{ _get(item, 'body') }}</div>
    </div>
    <q-separator class="q-my-xs"></q-separator>
    <div class="row justify-end q-pa-sm">
      <q-btn size="sm" color="black" class="tw-five" no-caps :label="`${showAll ? 'Hide' : 'Show'} Details`" :icon-right="showAll ? 'mdi-menu-up' : 'mdi-menu-down'" @click="showAll = !showAll"></q-btn>
    </div>
    <q-slide-transition>
      <div class="_fw" v-if="showAll">
        <div v-for="(field, i) in otherKeys || []" :key="`field-${i}`">
          <div class="text-0-7">{{ field }}</div>
          <div class="text-1">{{ _get(item, field) }}</div>
        </div>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import {$limitStr, $copyTextToClipboard, dollarString} from 'src/utils/global-methods';
  import {_get} from 'symbol-syntax-utils';
  import {date} from 'quasar';
  import {idGet} from 'src/utils/id-get';
  import {useDocRequests} from 'stores/doc-requests';

  const reqStore = useDocRequests();
  const props = defineProps({
    divAttrs: Object,
    modelValue: Object
  });

  const { item } = idGet({
    store: reqStore,
    name: 'reqs - Req Card',
    value: computed(() => props.modelValue)
  });

  const showAll = ref(false);

  const rows = computed(() => {
    return [
      {
        key: 'name',
        label: 'Name',
        field: data => data.name ? data.name : data.email || 'No Name',
        copy: true,
        copyVal: data => data.name ? data.name : data.email || 'No Name'
      },
      {
        key: 'orgName',
        label: 'Org Name',
        field: data => data.orgName || '',
        copy: true,
        copyVal: data => data.orgName  || ''
      },
      {
        key: 'eeCount',
        label: 'Employees',
        field: data => dollarString(data.eeCount, '', 0),
        copyVal: data => data.eeCount || ''
      },
      {
        key: 'email',
        label: 'Email',
        field: data => data.email,
        copy: true,
        copyVal: data => data.email
      },
      {
        key: 'phone',
        label: 'Phone',
        field: data => _get(data, 'phone.number.national'),
        copy: true,
        copyVal: data => _get(data, 'phone.number.e164')
      },
      {
        key: 'createdAt',
        label: 'Created',
        field: data => date.formatDate(data.createdAt, 'MM/DD/YYYY h:mm a')
      },
      {
        key: 'createdBy',
        label: 'Submission URL',
        field: data => $limitStr(_get(data, 'createdBy.longtail', ''), 50, '...'),
        copy: true,
        copyVal: data => _get(data, 'createdBy.longtail', '')
      }
    ];
  });

  const otherKeys = computed(() => {
    return Object.keys(item.value || {}).filter(a => ![...rows.value.map(a => a.key), a._id].includes(a))
  })

  const copy = text => {
    $copyTextToClipboard(text);
  };

</script>

<style scoped>

</style>
