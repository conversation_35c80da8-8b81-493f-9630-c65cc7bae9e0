<template>
  <div class="_fw">
    <q-input filled class="q-my-md w600 mw100" v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
    <table>
      <thead>
      <tr>
        <td>Company</td>
        <td>Plan</td>
        <td>Members</td>
      </tr>
      </thead>
      <tr v-for="(p, i) in p$.data" :key="`plan-${i}`">
        <td>
          <org-chip :model-value="p._fastjoin?.org || p.org"></org-chip>
        </td>
        <td>{{ p.name }}</td>
        <td>{{ dollarString(p.estFte, '', 0) }}</td>
      </tr>
    </table>
    <div v-if="!p$.data.length" class="q-pa-md text-italic font-1r">
      No participating plans found
    </div>
  </div>
</template>

<script setup>
  import OrgChip from 'components/orgs/cards/OrgChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {dollarString} from 'src/utils/global-methods';
  import {useNetworks} from 'stores/networks';
  import {useRoute} from 'vue-router';

  const planStore = usePlans();
  const networkStore = useNetworks();
  const route = useRoute();

  const props = defineProps({
    canEdit: Object
  })

  const { item: network } = idGet({
    store: networkStore,
    value: computed(() => route.params.networkId),
    params: ref({ runJoin: { sync_bundles: true, sync_plans: true } })
  })

  const query = computed(() => {
    return {}
  })

  const { search, searchQ } = HQuery({ query });

  const { h$: p$ } = HFind({
    store: planStore,
    params: computed(() => {
      return {
        runJoin: { plan_org: true },
        query: {
          ...searchQ.value,
          _id: { $in: network.value?.plans || [] }
        }
      }
    })
  })


</script>

<style lang="scss" scoped>

  table {
    border-collapse: collapse;
    width: 100%;

    thead {
      td {
        font-size: .75rem;
        font-weight: 600;
        color: #999;
      }
    }

    td {
      border-bottom: solid .4px #999;
      padding: 5px 10px;
      font-size: .9rem;
    }

    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
</style>
