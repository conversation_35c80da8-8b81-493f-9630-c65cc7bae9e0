<template>
  <div>
    <q-btn @click="dialog = !dialog" v-bind="{class: '_a_btn', noCaps: true, ...$attrs}">
      <slot name="default">
        <q-icon class="q-mr-sm" name="mdi-cog"></q-icon>
        <span class="tw-six">{{$possiblyPlural('Bundle', p$.total)}}</span>
      </slot>
    </q-btn>

    <common-dialog v-model="dialog" setting="smmd">
      <div class="bg-white q-pa-md">
        <div class="row">
        <q-tabs no-caps indicator-color="accent" align="left" v-model="tab">
          <q-tab name="current" label="Manage"></q-tab>
          <q-tab name="search" label="Add New"></q-tab>
        </q-tabs>
        </div>
        <q-input dense filled v-model="search.text" :placeholder="`Search ${$possiblyPlural('Bundle', p$.total)}`">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-tab-panels class="_panel" v-model="tab" animated>
          <q-tab-panel class="_panel" name="current">
            <q-list separator>
              <q-item-label header>Your bundles listed on {{network?.name}}</q-item-label>
              <q-item v-for="(p, i) in p$.data" :key="`p-${i}`">
                <q-item-section>
                  <q-item-label class="tw-six">{{p.name}}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <remove-proxy-btn icon="mdi-minus" name="Bundle" @remove="remove(p)"></remove-proxy-btn>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="search">
            <q-list separator>
              <q-item-label header>Add bundles to {{network?.name}}</q-item-label>

              <q-item v-for="(p, i) in p$.data" :key="`p-${i}`">
                <q-item-section>
                  <q-item-label class="tw-six">{{p.name}}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <remove-proxy-btn icon="mdi-plus" color="green" :remove-label="`Add ${p.name} to ${network.name}?`" @remove="add(p)"></remove-proxy-btn>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useProviders} from 'stores/providers';
  import {HFind} from 'src/utils/hFind';
  import {useBundles} from 'stores/bundles';
  import {HQuery} from 'src/utils/hQuery';
  import {$errNotify, $possiblyPlural, $successNotify} from 'src/utils/global-methods';
  import {LocalStorage} from 'symbol-auth-client';
  import {useNetworks} from 'stores/networks';

  const providerStore = useProviders();
  const bundleStore = useBundles();
  const networkStore = useNetworks();

  const props = defineProps({
    network: { required: true },
    provider: { required: false }
  })

  const dialog = ref(false);

  const { item:fullProvider } = idGet({
    store: providerStore,
    value: computed(() => props.provider || LocalStorage.getItem('provider_id'))
  })

  const tab = ref('current');
  const { search, searchQ } = HQuery({})
  const { h$:p$ } = HFind({
    store: bundleStore,
    pause: computed(() => !fullProvider.value),
    limit: computed(() => dialog.value ? 10 : 1),
    params: computed(() => {
      const q = { ...searchQ.value, provider: fullProvider.value?._id };
      if(!dialog.value || tab.value === 'current') q.networks = { $in: [props.network?._id || props.network] }
      else q.networks = { $nin: [props.network?._id || props.network] }
      return {
        query: q
      }
    })
  })

  const add = async (pb) => {
    const updated = networkStore.patch(props.network._id, { $addToSet: { bundle_reqs: pb._id }})
        .catch(err => $errNotify(`Error adding bundle: ${err.message}`))
    if(updated) $successNotify('Requested to add to network');
  }

  const remove = async (pb) => {
    const updated = bundleStore.patch(pb._id, { $pull: { networks: props.network._id }})
        .catch(err => $errNotify(`Error removing bundle: ${err.message}`))
    if(updated) {
      $successNotify('Removed from network - it may take a few minutes to reflect changes on network pages');
      networkStore.get(props.network._id, { runJoin: { sync_bundles: true } })
    }
  }


</script>

<style lang="scss" scoped>

</style>
