<template>
  <q-expansion-item dense v-model="open" expand-icon="mdi-menu-down">
    <template v-slot:header>
      <q-item class="_fw" v-if="multiple && modelValue?.length > 1">
        <q-item-section avatar>
          <avatar-row :model-value="selected" :use-atc-store="useAtcStore">
            <template v-slot:avatar="scope">
              <default-avatar
                  size-in="25px"
                  name-path="firstName"
                  :model-value="scope.item"
                  :bg-in="getGenderColor(scope.item)"
                  :divStyle="{ boxShadow: '0 0 0 1px white'}"
                  :use-atc-store="useAtcStore"
              ></default-avatar>
            </template>
          </avatar-row>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{selected.map(a => a.firstName).join(', ')}}</q-item-label>
        </q-item-section>
      </q-item>
      <member-item v-else-if="multiple && modelValue?.length" :model-value="selected[0]"></member-item>
      <member-item v-else-if="!multiple && !!modelValue" :model-value="selected"></member-item>
      <q-item v-else class="_fw">
        <q-item-section avatar>
          <q-avatar color="#999">
            <q-icon name="mdi-account"></q-icon>
          </q-avatar>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{label}}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
    <q-list separator>
      <member-item v-for="mbr in mbrList" :key="mbr._id" :model-value="mbr" clickable @update:model-value="select(mbr)">
        <template v-slot:side>
          <q-item-section side>
            <q-icon size="30px" color="green" v-if="mbrs.selected[mbr._id]" name="mdi-check"></q-icon>

          </q-item-section>
        </template>
      </member-item>
    </q-list>
  </q-expansion-item>
</template>

<script setup>
  import MemberItem from 'components/households/cards/MemberItem.vue';
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useHouseholds} from 'stores/households';
  import {getGenderColor} from '../utils';
  import {useAtcStore} from 'stores/atc-store';

  const hhStore = useHouseholds();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    multiple: Boolean,
    modelValue: { required: true },
    household: { required: false },
    person: { required: true, type: Object },
    emitValue: Boolean,
    label: { default: 'Select Household Member' },
    autoClose: Boolean
  })

  const open = ref(false);

  const { item: hh } = idGet({
    value: computed(() => props.household || props.person?.household),
    store: hhStore,
    refreshWhen: computed(() => !props.household?._fastjoin?.members),
    params: ref({ runJoin: { hh_members: true }})
  })

  const mbrList = computed(() => {
    return [props.person,...hh.value?._fastjoin?.members || []];
  })

  const mvIdArray = computed(() => {
    const arr = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue];
    if(props.emitValue) return arr;
    else return arr.filter(a => !!a).map(a => a._id);
  })
  const mbrs = computed(() => {
    const all = {};
    const selected = {};
    for(const mbr of mbrList.value){
      const id = mbr._id;
      all[id] = mbr;
      if(mvIdArray.value.includes(id)) selected[id] = mbr;
    }
    return {
      all,
      selected
    };
  })

  const selected = computed(() => {
    if(!props.multiple) {
      if(props.emitValue) return mbrs.value.all[props.modelValue];
      else return props.modelValue;
    } else {
      if(props.emitValue){
        return mbrList.value.filter(a => (props.modelValue || []).includes(a._id))
      } else return props.modelValue;
    }
  })

  const select = (val) => {
    if(props.multiple){
      const list = [...props.modelValue || []]
      if(props.emitValue){
        const idx = list.indexOf(val._id);
        if(idx > -1) list.splice(idx, 1);
        else list.push(val._id);
      } else {
        const idx = list.map(a => a._id).indexOf(val._id);
        if(idx > -1) list.splice(idx, 1);
        else list.push(val);
      }
      emit('update:model-value', list)
    } else if(props.emitValue) emit('update:model-value', val._id);
    else emit('update:model-value', val);
    if(props.autoClose) open.value = false;
  }

</script>

<style lang="scss" scoped>

</style>
