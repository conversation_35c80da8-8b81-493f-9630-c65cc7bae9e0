<template>
  <q-item v-bind="{ class: '_fw', ...$attrs}" @click="$emit('update:model-value', person)">
    <q-item-section avatar>
      <slot name="avatar">
        <default-avatar :bg-in="bg" :model-value="person" name-path="firstName" :use-atc-store="useAtcStore"></default-avatar>
      </slot>
    </q-item-section>
    <q-item-section>
      <q-item-label class="tw-six">{{name}} ({{age}})</q-item-label>
    </q-item-section>
    <slot name="side"></slot>
  </q-item>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {computed} from 'vue';
  import { dateDiff } from 'src/utils/date-utils';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useAtcStore} from 'stores/atc-store';
  import {getGenderColor} from '../utils';

  const pplsStore = usePpls();

  const props = defineProps({
    modelValue: { required: true },
  })

  const { item:person } = idGet({
    value: computed(() => props.modelValue),
    store: pplsStore
  })

  const convertPerson = () => {
    const { name } = person.value || { name: '' };
    const ns = name?.split(' ') || [];
    return { lastName: ns[ns.length - 1], firstName: ns[0], ...person.value };
  }

  const usePerson = computed(() => {
   return convertPerson()
  })

  const name = computed(() => {
    const { firstName, lastName } = usePerson.value || { firstName: '', lastName: '' };
    return firstName + ' ' + lastName;
  })

  const age = computed(() => {
    const { dob } = { dob: new Date(), ...usePerson.value };
    return Math.floor((dateDiff(new Date(), dob, 'months') || 0)/12);
  })

  const bg = computed(() => {
    return getGenderColor(usePerson.value);
  })
</script>

<style lang="scss" scoped>

</style>
