<template>
  <div :class="`_fw ${dark ? 'text-white' :''}`">
    <div class="flex items-center">
      <div class="q-pr-sm">Filing As:</div>
      <status-chip
          v-bind="{ dark }"
          class="tw-six"
          picker
          :model-value="household?.filingAs"
          @update:model-value="changeStatus"
      ></status-chip>
    </div>

    <table>
      <thead>
      <tr v-if="toggle">
        <th></th>
        <th>With Plan</th>
        <th>Without</th>
      </tr>
      </thead>
      <tbody>
      <tr :class="`__pay __top  ${dark ? '__p_d' : ''}`">
        <td><span class="b_b">Wages:</span></td>
        <td>{{ dollarString(taxes.wages, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(without.wages, '$', 0) }}</td>
        </template>
      </tr>
      <tr v-if="taxes.taxableBenefits" :class="`__pay  ${dark ? '__p_d' : ''}`">
        <td><span class="b_b">Taxable Benefits:</span></td>
        <td>{{ dollarString(taxes.taxableBenefits, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(without.taxableBenefits, '$', 0) }}</td>
        </template>
      </tr>

      <tr v-if="taxes.contributions" :class="`__minus  ${dark ? '__m_d' : ''}`">
        <td class="b_b">Pretax Elections</td>
        <td>({{ dollarString(taxes.contributions, '$', 0) }})</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(without.contributions, '$', 0) }}</td>
        </template>
      </tr>
      <tr :class="`__tr __bot  ${dark ? '__t_d' : ''}`">
        <td>&nbsp;&nbsp;&nbsp;<span class="b_b">Payroll Tax:</span></td>
        <td class="__t">{{ dollarString(taxes.payrollTax?.ee.total, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(without.payrollTax?.ee.total, '$', 0) }}</td>
        </template>
      </tr>
      <template v-if="taxes.selfEmployed">
        <tr>
          <td>Self Employed Income:</td>
          <td>{{ dollarString(taxes.selfEmployed, '$', 0) }}</td>
          <template v-if="toggle">
            <td class="">{{ dollarString(without.selfEmployed, '$', 0) }}</td>
          </template>
        </tr>
        <tr :class="`__tr  ${dark ? '__t_d' : ''}`">
          <td class="b_b">&nbsp;&nbsp;&nbsp;Self Employment Tax:</td>
          <td>{{ dollarString(taxes.payrollTax.se.total, '$', 0) }}</td>
          <template v-if="toggle">
            <td class="">{{ dollarString(without.payrollTax?.se.total, '$', 0) }}</td>
          </template>
        </tr>
      </template>
      <template v-if="taxes.investment">
        <tr>
          <td>Investment Income:</td>
          <td>{{ dollarString(taxes.investment, '$', 0) }}</td>
          <template v-if="toggle">
            <td class="">{{ dollarString(without.investment, '$', 0) }}</td>
          </template>
        </tr>
      </template>
      <tr>
        <td class="b_b">Deductions</td>
      </tr>
      <tr v-for="(k, i) in Object.keys(taxes.deductions || {})" :key="`k-${i}`">
        <td>
          &nbsp;&nbsp;&nbsp;{{ title(k) }}
        </td>
        <td>{{ taxes.deductions[k]?.off ? '$0' : dollarString((taxes.deductions || {})[k]?.amount, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString((without.deductions || {})[k]?.amount, '$', 0) }}</td>
        </template>
      </tr>
      <tr>
        <td class="b_b">Taxable Income:</td>
        <td>{{ dollarString(taxes.taxable, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(without.taxable, '$', 0) }}</td>
        </template>
      </tr>
      <tr :class="`__tr  ${dark ? '__t_d' : ''}`">
        <td class="b_b">Income Tax:</td>
        <td>{{ dollarString(taxes.tax, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(without.tax, '$', 0) }}</td>
        </template>
      </tr>
      <tr>
        <td class="b_b">Estimated Tax Credits</td>
      </tr>
      <tr v-for="(c, i) in Object.keys(taxes.credits ||{})" :key="`credit-${i}`">
        <td>&nbsp;&nbsp;&nbsp;{{ title(c) }}</td>
        <td>{{ dollarString((taxes.credits || {})[c], '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString((without.credits || {})[c], '$', 0) }}</td>
        </template>
      </tr>
      <tr :class="`__tr __top  ${dark ? '__t_d' : ''}`">
        <td class="b_b">Total Tax:</td>
        <td>{{ dollarString(totalTax, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(wTotalTax, '$', 0) }}</td>
        </template>
      </tr>

      <tr v-if="taxes.pretaxBenefits || taxes.contributions" :class="`__minus  ${dark ? '__m_d' : ''}`">
        <td class="b_b">Tax-free Benefit</td>
        <td>{{
            dollarString(tfb, '$', 0)
          }}
        </td>
        <template v-if="toggle">
          <td class="">$0</td>
        </template>
      </tr>

      <tr :class="`__pay  ${dark ? '__p_d' : ''}`">
        <td class="b_b">Take Home Pay</td>
        <td>{{ dollarString(thp, '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(wthp, '$', 0) }}</td>
        </template>
      </tr>
      <tr :class="`__net __bot ${dark ? '__n_d' : ''}`">
        <td>Net Benefit Cost</td>
        <td>{{ dollarString($max(0, wthp - thp), '$', 0) }}</td>
        <template v-if="toggle">
          <td class="">{{ dollarString(0, '$', 0) }}</td>
        </template>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
  import StatusChip from 'components/households/forms/StatusChip.vue';

  import {useHouseholds} from 'stores/households';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {$max, dollarString} from 'src/utils/global-methods';
  import {taxSummary} from 'components/households/utils/income';

  const store = useHouseholds();

  const emit = defineEmits(['update:all']);
  const props = defineProps({
    person: { required: true },
    hh: { required: false },
    plan: Object,
    enrollment: Object,
    toggle: Boolean,
    dark: Boolean
  })

  const { item: household } = idGet({
    store,
    value: computed(() => props.hh || props.person?.household),
    refreshWhen: computed(() => !props.hh?._fastjoin?.cams),
    params: ref({
      runJoin: { total_income: true }
    })
  })

  const changeStatus = (status) => {
    store.patch(household.value._id, { filingAs: status });
  }

  const title = (str) => {
    return typeof (str) === 'string' ? str.split('_').map(a => a.charAt(0).toUpperCase() + a.slice(1)).join(' ') : '';
  }

  const {
    thp,
    wthp,
    tfb,
    wTotalTax,
    totalTax,
    without,
    taxes
  } = taxSummary(household, { plan: props.plan, enrollment: props.enrollment, toggle: props.toggle });

  watch(totalTax, (nv, ov) => {
    if (nv !== ov && (ov || ov === 0)) {
      setTimeout(() => {
        emit('update:all', { thp, wthp, tfb, wTotalTax, totalTax, without, taxes });
      }, 250)
    }
  }, { immediate: true });
</script>

<style lang="scss" scoped>

  table {
    width: 100%;
    font-weight: 500;
  }

  th {
    text-align: right;
    font-weight: 600;
    font-size: .84rem;
  }

  tr {
    td {
      text-align: right;
      padding: 6px 10px;

      &:nth-child(2) {
        font-weight: 500;
      }

    }

    td:first-child {
      text-align: left;
    }

  }

  .b_b {
    font-weight: 600;
  }

  .__top {
    td {
      &:first-child {
        border-radius: 8px 0 0 0;
      }

      &:last-child {
        border-radius: 0 8px 0 0;
      }
    }
  }
  .__bot {
    td {
      &:first-child {
        border-radius: 0 0 0 8px;
      }

      &:last-child {
        border-radius: 0 0 8px 0;
      }
    }
  }

  .__tr {
    td {
      background: linear-gradient(180deg, var(--q-s0), var(--q-s2));
      font-weight: 600 !important;
      color: var(--q-s12);
    }
  }

  .__pay {
    td {
      color: var(--q-p12);
      font-weight: 600 !important;
      background: linear-gradient(180deg, var(--q-p0), var(--q-p1));
    }
  }

  .__minus {
    td {
      color: var(--q-a12);
      font-weight: 600 !important;
      background: linear-gradient(180deg, var(--q-a0), var(--q-a2));
    }
  }
  .__net {
    td {
      color: black;
      font-weight: 600 !important;
      background: linear-gradient(180deg, var(--q-ir-grey-2), var(--q-ir-grey-3));
    }
  }

  .__t_d {
    td {
      color: white;
      background: linear-gradient(180deg, var(--q-s10), var(--q-s10));
    }
  }
  .__p_d {
    td {
      color: white !important;
      background: linear-gradient(180deg, var(--q-p10), var(--q-p10)) !important;
    }
  }
  .__m_d {
    td {
      color: white;
      background: linear-gradient(180deg, var(--q-a10), var(--q-a10));
    }
  }
  .__n_d {
    td {
      color: white;
      background: linear-gradient(180deg, #212121, #212121);
    }
  }
</style>
