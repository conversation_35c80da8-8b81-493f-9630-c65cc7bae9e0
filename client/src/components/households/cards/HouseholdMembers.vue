<template>
  <div class="_fw">
    <q-list separator v-bind="{dense, ...listAttrs}">
      <q-expansion-item
          :dense="dense"
          :model-value="open === 'person'"
          @update:model-value="toggleOpen($event, 'person')"
          group="0"
          expand-icon="mdi-menu-down">
        <template v-slot:header>
          <member-item :model-value="person" is-person></member-item>
        </template>
        <member-form
            :special-change="specialChange"
            :model-value="person"
            :household="hh">
          <template v-slot:bottom>
              <enrollment-extras
                  :household="hh"
                  :member="byId[person.value?._id]"
                  :enrollment="enrollment"
              ></enrollment-extras>
          </template>
        </member-form>
      </q-expansion-item>
      <q-expansion-item
          :dense="dense"
          v-for="(k, i) in Object.keys(hh?.members || {})"
          :key="`member-${i}`"
          :model-value="open === k"
          @update:model-value="toggleOpen($event, k)"
          group="0"
          expand-icon="mdi-menu-down">
        <template v-slot:header>
          <member-item :model-value="byId[k]"></member-item>
        </template>
        <member-form
            :household="hh"
            :special-change="specialChange"
            :model-value="byId[k]"
            @update:model-value="updateItem($event)">
          <template v-slot:bottom>
            <enrollment-extras :household="hh" :member="byId[k]" :enrollment="enrollment"></enrollment-extras>
            <slot name="bottom" :member="byId[k]"></slot>
          </template>
        </member-form>
        <div class="row">
          <div class="col-6"></div>
          <div class="col-6">
            <remove-item :name="byId[k]?.name" @remove="rmv(k)"></remove-item>

          </div>
        </div>
      </q-expansion-item>
      <q-expansion-item
          :dense="dense"
          :model-value="open === 'new'"
          @update:model-value="toggleOpen($event, 'new')"
          hide-expand-icon
          expand-icon="mdi-menu-down">
        <template v-slot:header>
          <q-item class="_fw">
            <q-item-section avatar>
              <q-icon color="primary" name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label class="tw-six text-ir-grey-6">Add Member</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <member-form :special-change="specialChange" v-if="open === 'new'" @update:model-value="updateItem" :household="person?.household">
        </member-form>
      </q-expansion-item>
    </q-list>
  </div>
</template>

<script setup>
  import MemberItem from 'components/households/cards/MemberItem.vue';
  import MemberForm from 'components/households/forms/MemberForm.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';
  import EnrollmentExtras from 'components/households/forms/EnrollmentExtras.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useHouseholds} from 'stores/households';
  import {usePpls} from 'stores/ppls';
  import {$errNotify} from 'src/utils/global-methods';

  const store = useHouseholds();
  const pplStore = usePpls();

  const emit = defineEmits(['update:model-value', 'loaded']);
  const props = defineProps({
    person: { required: true },
    enrollment: { required: false },
    specialChange: Boolean,
    listAttrs: Object,
    dense: Boolean
  });

  const { item: hh } = idGet({
    store,
    value: computed(() => props.person?.household),
    onWatch: (val) => {
      emit('loaded', val)
    },
    params: ref({ runJoin: { hh_members: true }})
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => props.person)
  })

  const memberIds = computed(() => Object.keys(hh.value?.members || {}));


  const byId = ref({})

  const open = ref('');
  const toggleOpen = (val, k) => {
    if (!val) open.value = '';
    else open.value = k;
  }
  const updateItem = async (val) => {
    const { firstName, lastName, _id } = val;
    if (firstName && lastName && _id) {
      byId.value[_id] = val
      open.value = val._id

    }
  }

  const rmv = (k) => {
    const patchObj = { $unset: { [`members.${k}`]: '' } }
    delete byId.value[k]
    store.patchInStore(hh.value._id, patchObj)
    store.patch(hh.value._id, patchObj, { special_change: props.specialChange ? '*' : undefined })
        .catch(err => $errNotify(`Issue removing member: ${err.message}`));
  }


  watch(memberIds, async (nv, ov) => {
    if (nv?.length !== ov?.length) {
      const res = await pplStore.find({ query: { $limit: nv.length, _id: { $in: nv } }, runJoin: { hh_members: true } })
      for (const p of res.data) {
        const { dependent, disabled, incarcerated, annualIncome, relation } = hh.value.members[p._id] || {};
        byId.value[p._id] = { ...p, _id: p._id, dependent, disabled, incarcerated, annualIncome, relation }
      }
      const { disabled, incarcerated, monthsSinceSmoked } = hh.value || {}
      byId.value[person.value._id] = { ...person.value, disabled, incarcerated, monthsSinceSmoked }
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
