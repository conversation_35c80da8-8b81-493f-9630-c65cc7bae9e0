<template>
  <q-list separator dense>
    <member-item is-person :model-value="person"></member-item>
    <member-item v-for="id in memberIds" :key="id" :model-value="byId[id]"></member-item>
  </q-list>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useHouseholds} from 'stores/households';
  import {usePpls} from 'stores/ppls';
  import MemberItem from 'components/households/cards/MemberItem.vue';
  import {HFind} from 'src/utils/hFind';

  const store = useHouseholds();
  const pplsStore = usePpls();

  const emit = defineEmits(['update:model-value', 'loaded']);
  const props = defineProps({
    person: { required: true }
  });

  const { item: hh } = idGet({
    store,
    value: computed(() => props.person?.household),
    onWatch: (val) => {
      emit('loaded', val)
    }
  })

  const { item: person } = idGet({
    store: pplsStore,
    value: computed(() => props.person)
  })

  const memberIds = computed(() => Object.keys(hh.value?.members || {}));

  const limit = ref(10)
  const { h$ } = HFind({
    store: pplsStore,
    pause: computed(() => !hh.value?.members),
    limit,
    params: computed(() => {
      return {
        query: {
          $limit: limit.value,
          _id: { $in: memberIds.value || [] }
        }
      }
    }),
  })

  const byId = computed(() => {
    const obj = {};
    for(const id of h$.data || []){
      obj[id._id] = id;
    }
    return obj;
  })

  watch(memberIds, (nv) => {
    limit.value = nv.length;
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
