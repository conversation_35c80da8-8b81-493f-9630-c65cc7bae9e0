<template>
  <div class="_fw">

    <div class="__tw">
      <div class="__tbl">

        <slot name="top"></slot>

        <q-btn v-if="sArray.length" flat size="sm" no-caps class="tw-six" @click="remove">
          <q-icon class="q-mr-sm" name="mdi-delete" color="red"></q-icon>

          <span>Remove {{ $possiblyPlural('Row', sArray) }}</span>
        </q-btn>
        <table class="__inp alt-font">
          <thead>
          <tr>
            <th class="cursor-pointer text-right __ce">
              <q-icon size="20px" :color="allOn ? 'primary' : 'ir-grey-6'"
                      :name="`mdi-checkbox-${allOn ? 'marked' : 'blank-outline'}`" @click="selectAll(!allOn)"></q-icon>
            </th>
            <th v-for="(req, i) in reqs" :key="`req-${i}`">
              {{ req.label }}
              <template v-if="req.subtitle">
                <br><span class="font-5-8r">{{ req.subtitle }}</span>
              </template>
              <q-tooltip v-if="req.tooltip">
                <div class="text-center mw300 text-xxs tw-five">
                  {{ req.tooltip }}
                </div>
              </q-tooltip>
            </th>
          </tr>
          </thead>
          <tbody>
          <!--          first row-->
          <tr>
            <td class="cursor-pointer">
              <span>1</span>
            </td>
            <td v-for="(req, idx) in reqs" :key="`ex-0-${idx}`" @click="setFocus([0, idx])" :id="`col-0-${idx}`"
                :class="`${errs[`0-${idx}`] ? '__err' : ''} ${req.class ? req.class(0, idx) : ''}`">
              <q-tooltip v-if="errs[`0-${idx}`]">
                <div class="w300 tw-six text-xs">Err: {{ errs[`0-${idx}`] }}</div>
              </q-tooltip>
              <input
                  :id="`inp-0-${idx}`"
                  @keydown="senseTab($event, 0, idx)"
                  :value="req.format(getV(req, person._id, 0))"
                  @input="setData(0, idx, $event.target.value)"
                  @blur="checkData(0, idx)"
              >
            </td>

          </tr>
          <!--          ROWS 2 - end-->
          <tr v-for="(id, i) in memberIds.slice(1)" :key="`row-${i + 1}`" :id="`row-${i + 1}`">
            <td class="cursor-pointer" @click.stop="selected[id] = !selected[id]">
              <span v-if="!selected[id]">{{ i + 2 }}</span>
              <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
            </td>
            <td v-for="(req, idx) in reqs" :key="`col-${i + 1}-${idx}`" :id="`col-${i+1}-${idx}`"
                :class="`${errs[`${i+1}-${idx}`] ? '__err' : ''} ${req.class ? req.class(i+1, idx) : ''}`" @click="setFocus([i+1, idx])">
              <q-tooltip v-if="errs[`${i+1}-${idx}`]">
                <div class="mw300 tw-six">Err: {{ errs[`${i + 1}-${idx}`] }}</div>
              </q-tooltip>

              <input
                  :id="`inp-${i+1}-${idx}`"
                  @keydown="senseTab($event, i+1, idx)"
                  :value="req.format(getV(req, id, idx))"
                  @input="setData(i+1, idx, $event.target.value)"
                  @blur="checkData(i+1, idx)"
              >
            </td>
          </tr>
          </tbody>
        </table>

      </div>
    </div>

  </div>

</template>

<script setup>

  import {$possiblyPlural} from 'src/utils/global-methods';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {formatDate} from 'src/utils/date-utils';
  import {usePpls} from 'stores/ppls';
  import {_get, _set} from 'symbol-syntax-utils';
  import {date} from 'quasar';

  const hhStore = useHouseholds();
  const pplStore = usePpls();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    disable: Boolean
  })

  const ppl = ref({ data: [] })
  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => props.modelValue),
    deepWatch: true,
    onWatch: async (item) => {
      const mbrs = Object.keys(item.members || {})
      if(mbrs.length !== ppl.value.total) {
        const inStore = pplStore.findInStore({ query: { _id: { $in: mbrs }}})
        if(inStore.total === mbrs.length) ppl.value = inStore
        else ppl.value = await pplStore.find({ query: { _id: { $in: mbrs }, $limit: mbrs.length } })
            .catch(err => {
              console.error(`Error getting household table people: ${err.message}`)
              return ppl.value;
            })
      }
    }
  })
  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => hh.value.person),
    refreshOn: (val) => !val.dob
  })
  const pplById = computed(() => {
    const obj = {};
    if (person.value._id) obj[person.value._id] = person.value;
    for (let i = 0; i < ppl.value.data.length; i++) {
      obj[ppl.value.data[i]._id] = ppl.value.data[i]
    }
    return obj;
  })

  const pplChanges = ref({});
  const hhChanges = ref({})

  const errs = ref({})
  const getV = (config, id, idx) => {
    if (config.hh) return _get(hhChanges.value, `${idx > 0 ? `members.${id}.` : ''}${config.field}`) || _get(hh.value, `${idx > 0 ? `members.${id}.` : ''}${config.field}`)
    return _get(pplChanges.value, `${id}.${config.field}`) || _get(pplById.value, `${id}.${config.field}`)
  }

  const memberIds = computed(() => {
    const arr = [];
    if (hh.value.person) arr.push(hh.value.person);
    for (const k in hh.value.members) {
      arr.push(k);
    }
    return arr;
  })
  const focused = ref([0, 0]);

  const reqs = computed(() => {
    return [
      {
        label: 'First Name',
        field: 'firstName',
        format: (v) => v,
        rev: (v) => v?.trim() || '',
        check: (v, row, col) => {
          const key = `${row}-${col}`
          if (!v?.length) errs.value[key] = 'First Name Required'
          else delete errs.value[key]
        }
      },
      {
        label: 'Last Name',
        field: 'lastName',
        format: (v) => v,
        rev: (v) => v?.trim() || '',
        check: (v, row, col) => {
          const key = `${row}-${col}`
          if (!v?.length) errs.value[key] = 'Last Name Required'
          else delete errs.value[key]
        }
      },
      {
        label: 'DOB',
        subtitle: 'MM-DD-YYYY',
        field: 'dob',
        format: (v) => formatDate(v, 'MM-DD-YYYY'),
        rev: (v) => v?.trim() || '',
        check: (v, row, col) => {
          const key = `${row}-${col}`
          if (!date.isValid(v)) errs.value[key] = 'Valid DOB required'
          else delete errs.value[key]
        }
      },
      {
        label: 'Gender',
        subtitle: 'M/F',
        field: 'gender',
        format: (v) => v?.charAt(0)?.toUpperCase() || '',
        rev: (v) => v?.trim() || '',
        check: () => ''
      },
      {
        label: 'Tax ID',
        field: 'ssn',
        class: (i, idx) => focused.value[0] === i && focused.value[1] === idx ? '' : 'pss',
        format: (v) => {
          if (!v) return ''
          const V = v.split('-').join('')
          return `${V.substring(0, 3)}-${V.substring(3, 5)}-${V.substring(5, 10)}`
        },
        rev: (v) => {
          if (!v) return ''
          const V = v.split('-').join('')
          return `${V.substring(0, 3)}-${V.substring(3, 5)}-${V.substring(5, 10)}`
        },
        check: () => ''
      },
      {
        label: 'Pregnant',
        subtitle: 'Y/N',
        field: 'pregnant',
        hh: true,
        format: (v) => v ? '✅' : '',
        rev: (v) => !!v?.toLowerCase().includes('y'),
        check: () => ''
      },
      {
        label: 'Tobacco',
        subtitle: 'Y/N',
        field: 'monthsSinceSmoked',
        hh: true,
        format: (v) => typeof v === 'number' && v < 12 ? '✅' : '',
        rev: (v) => v?.toLowerCase().includes('y') ? 0 : undefined,
        check: () => ''
      },
      {
        label: 'US Citizen',
        subtitle: 'Y/N',
        field: 'us_citizen',
        hh: true,
        format: (v) => v ? '✅' : '',
        rev: (v) => !!v?.toLowerCase().includes('y'),
        check: () => ''
      },
      {
        label: 'Incarcerated',
        subtitle: 'Y/N',
        field: 'incarcerated',
        hh: true,
        format: (v) => v ? '✅' : '',
        rev: (v) => !!v?.toLowerCase().includes('y'),
        check: () => ''
      },

      {
        label: 'Native American/Alaskan',
        subtitle: 'Y/N',
        field: 'native',
        hh: true,
        format: (v) => v ? '✅' : '',
        rev: (v) => !!v?.toLowerCase().includes('y'),
        check: () => ''
      },
      {
        label: 'Hispanic/Latino',
        subtitle: 'Y/N',
        field: 'latino',
        hh: true,
        format: (v) => v ? '✅' : '',
        rev: (v) => !!v?.toLowerCase().includes('y'),
        check: () => ''
      }
    ]
  })

  const hasFocused = ref(false);
  const lastTab = ref(false);
  const setFocus = (val, lt) => {
    focused.value = val;
    hasFocused.value = true;
    const el = document.getElementById(`inp-${val[0]}-${val[1]}`);
    // console.log('el', el);
    if (el) el.focus();
    lastTab.value = lt;
  }
  const loading = ref(false);

  const selected = ref({});

  const sArray = computed(() => {
    let res = [];
    for (const k in selected.value) {
      if (selected.value[k]) res.push(k)
    }
    return res;
  })

  const allOn = computed(() => sArray.value.length === memberIds.value.length - 1)

  const trySave = async () => {
    if(props.disable) return
    const promises = []
    for (const k in pplChanges.value) {
      promises.push(pplStore.patch(k, { ...pplChanges.value[k] })
          .catch(err => console.error(`Error patching person ${k}: ${err.message}`)))
    }
    const $set = {};
    let runHh = false
    for (const k in hhChanges.value) {
      runHh = true;
      if (k === 'members') {
        for (const memberId in hhChanges.value[k]) {
          for(const subK in hhChanges.value[k][memberId]) {
            $set[`${k}.${memberId}.${subK}`] = hhChanges.value[k][memberId][subK]
          }
        }
      } else $set[k] = hhChanges.value[k];
    }
    if (runHh) {
      promises.push(hhStore.patch(hh.value._id, { $set })
          .catch(err => console.error(`Error patching household: ${err.message}`)))
    }
    await Promise.all(promises)
  }
  const saveTo = ref();
  const maybeSave = () => {
    if (saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(() => {
      if (hh.value?._id) {
        trySave(true)
      }
    }, 5000);
  }

  const checkData = (row, col) => {
    if (!memberIds.value[row]) {
      delete errs.value[row];
    } else reqs.value[col].check(getV(reqs.value[col], memberIds.value[row], col), row, col);
  }

  const checkAll = () => {
    for (let i = 0; i < memberIds.value.length; i++) {
      for (let idx = 0; idx < memberIds.value[i].length; idx++) {
        checkData(i, idx)
      }
    }
  }

  const setTo = ref()
  const setData = (row, col, value) => {
    if(props.disable) return
    const id = memberIds.value[row];
    const req = reqs.value[col]
    if (req.hh) {
      const path = row === 0 ? req.field : `members.${id}.${req.field}`
      const obj = _set(hhChanges.value, path, req.rev(value))
      hhChanges.value = obj
    } else {
      pplChanges.value[id] = { ...pplChanges.value[id], [req.field]: req.rev(value) }
    }
    if (setTo.value) clearTimeout(setTo.value);
    setTo.value = setTimeout(() => maybeSave(), 1500);
  }

  const senseTab = (e) => {
    if (e.keyCode === 9) {
      let i = focused.value[0];
      let idx = focused.value[1];
      if (idx >= reqs.value.length - 1) {
        if (i < memberIds.value.length - 1) {
          i += 1;
          idx = 0;
        } else return;
      } else idx++
      setFocus([i, idx - (lastTab.value ? 0 : 1)], true);
    }
  };


  const remove = () => {
    if(props.disable) return
    const arr = Object.keys(hh.value.members || {});
    const newMembers = {};
    for (let i = 0; i < arr.length; i++) {
      if (!selected.value[arr[i]]) newMembers[arr[i]] = hh.value.members[arr[i]];
    }
    hhStore.patchInStore(hh.value._id, { members: newMembers })
  }

  const selectAll = (val) => {
    if (val) {
      for (let i = 1; i < memberIds.value.length; i++) {
        selected.value[memberIds.value[i]] = true;
      }
    } else selected.value = {};
  }

  const save = async () => {
    loading.value = true
    try {
      checkAll()
      await trySave()
    } catch (e) {
      console.error(`Error running analysis: ${e.message}`)
    } finally {
      loading.value = false;
    }
  }

  const keyDown = (e) => {
    if (e.key === 'Enter') {
      if (hasFocused.value) {
        const row = focused.value[0];
        if (row < memberIds.value.length - 2) {
          setFocus([focused.value[0] + 1, focused.value[1]])
        }
      }
    }
  }
  window.addEventListener('keydown', keyDown);


</script>

<style lang="scss" scoped>

  .__tw {
    width: 100%;

    //background: var(--q-ir-grey-1);

    .__tbl {
      width: 100%;
      overflow-x: scroll !important;
    }
  }

  input {
    border: none;
    width: 6em;
    box-sizing: content-box;
    padding: 0px;
    background-color: transparent;

    &:focus {
      border: none;
      outline: none;
    }
  }

  .__hide {
    opacity: 0;
    pointer-events: none;
    width: 0;
  }

  .__inp {
    width: 100%;
    border-collapse: collapse;


    tr {
      th {
        padding: 8px 4px;
        text-align: center;
        //border: solid 1px #999;
        font-size: var(--text-xxs);
        color: var(--q-ir-grey-7);
        align-content: start;
        font-family: var(--main-font);
        border: solid 1px #999;
      }

      .__ce {
        align-content: center;
      }

      td {
        font-size: var(--text-xxs);
        padding: 4px 8px;
        text-align: left;
        border: solid 1px #999;
        background: var(--ir-bg1);

        &:first-child {
          background: white;
          text-align: right;

        }
      }

    }
  }

  .__ers {
    tr {
      td {
        background: var(--q-ir-red-1) !important;
      }
    }
  }

  .__err {
    background: var(--q-ir-red-1) !important;
    color: var(-q-ir-red-10) !important;
  }

  .__errs {
    padding: 0;
    width: 100%;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: all .3s ease;
  }

  .__on {
    max-height: 1500px;
    padding: 0 10px 20px 10px;
  }

</style>
