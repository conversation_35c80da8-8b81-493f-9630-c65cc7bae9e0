<template>

  <template v-if="head?._id !== hh?.person">
    <!--      DEPENDENT STATUS-->
    <div class="_form_label">Dependent</div>
    <div class="q-pa-sm">
      <q-radio v-model="form.dependent" :val="true" label="Yes" @update:model-value="autoSave('dependent')"></q-radio>
      <q-radio v-model="form.dependent" :val="false" label="No" @update:model-value="autoSave('dependent')"></q-radio>
    </div>
  </template>

  <div class="_form_label">Relation</div>
  <div class="q-pa-sm">
    <relation-chip outline picker v-model="form.relation" @update:model-value="autoSave('relation')"></relation-chip>
  </div>

  <!--      ADDRESS-->
  <div class="_form_label">Address</div>
  <div class="q-pa-sm">
    <tomtom-autocomplete v-model="form.address" @update:model-value="autoSave('address')"></tomtom-autocomplete>
  </div>

  <div class="_form_label">Last Tobacco Use</div>
  <div class="q-pa-sm">
    <q-chip outline
            :label="typeof form.monthsSinceSmoked === 'number' ? $possiblyPlural('Month', form.monthsSinceSmoked) : 'Never'"
            icon-right="mdi-menu-down" clickable>
      <q-menu>
        <div class="w300 mw100">
          <q-list separator>
            <q-item clickable @click="form.monthsSinceSmoked = undefined">
              <q-item-section>
                <q-item-label>More than 60 Months (or Never)</q-item-label>
              </q-item-section>
            </q-item>
            <q-item clickable @click="form.monthsSinceSmoked = mo - 1" v-for="mo in 61" :key="`mo-${mo}`">
              <q-item-section>
                <q-item-label>{{ mo - 1 }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-menu>
    </q-chip>
  </div>

  <template v-if="age > 17 || form.disabled">
    <!--    DISABLED-->
    <div class="_form_label">Disabled</div>
    <div class="q-pa-sm">
      <q-radio label="Yes" @update:model-value="autoSave('disabled', $event)" :model-value="form.disabled" :val="true"></q-radio>
      <q-radio label="No" @update:model-value="autoSave('disabled', $event)" :model-value="!!form.disabled"
               :val="false"></q-radio>
    </div>
    <template v-if="form.disabled">
      <div class="_form_label">Annual Income</div>
      <div class="q-pa-sm">
        <div class="_fw mw200">
          <money-input filled @blur="autoSave('annualIncome')" v-model="form.annualIncome" :decimal="0" prefix="$"
                       placeholder="Enter total income..."></money-input>
        </div>
      </div>
    </template>
  </template>

  <!--    INCARCERATED-->
  <template v-if="age > 12 || form.incarcerated">
    <div class="_form_label">Incarcerated</div>
    <div class="q-pa-sm">
      <q-radio label="Yes" v-model="form.incarcerated" @update:model-value="autoSave('incarcerated')" :val="true"></q-radio>
      <q-radio label="No" @update:model-value="autoSave('incarcerated', $event)" :model-value="!!form.incarcerated"
               :val="false"></q-radio>
    </div>
  </template>
</template>

<script setup>
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import RelationChip from 'components/households/cards/RelationChip.vue';

  import {computed, nextTick, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useHouseholds} from 'stores/households';
  import {useEnrollments, memberkeys} from 'stores/enrollments';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import {_get} from 'symbol-syntax-utils';

  const pplsStore = usePpls();
  const hhStore = useHouseholds();
  const eStore = useEnrollments();

  const props = defineProps({
    member: { required: true },
    household: { required: true },
    enrollment: { required: false }
  })

  const { item: mbr } = idGet({
    store: pplsStore,
    value: computed(() => props.member)
  })
  const hh = computed(() => props.household);

  const age = computed(() => {
    if (mbr.value?.dob) {
      const age = new Date(mbr.value.dob).getTime();
      return Math.floor((new Date().getTime() - age) / (1000 * 60 * 60 * 24 * 365))
    } else return 18;
  })

  const { item: head } = idGet({
    store: pplsStore,
    value: computed(() => hh.value?.person)
  })

  const formFn = (defs) => {
    return {
      disabled: false,
      incarcerated: false,
      annualIncome: 0,
      address: undefined,
      ...defs
    }
  }
  const form = ref(formFn());

  const patchObj = ref({});
  const save = async () => {
    const $set = {};
    const erSet = {}
    for (const id in patchObj.value) {
      const path = id === head.value._id ? '' : `members.${id}.`

      for (const k in patchObj.value[id]) {
        /** only set keys that get passed to household members or owner */
       if(memberkeys[`household_${path.length ? 'member' : 'owner'}`].includes(k)) $set[`${path}${k}`] = patchObj.value[id][k]

        /** always set keys on enrollment */
        erSet[`enrolled.${id}.${k}`] = patchObj.value[id][k]
      }
    }
    hhStore.patchInStore(hh.value._id, { $set });
    await hhStore.patch(hh.value._id, { $set },{ runJoin: { hh_members: true }})
        .catch(err => console.error(`Error saving household: ${err.message}`))
    if (props.enrollment) {
      const id = props.enrollment._id || props.enrollment;
      eStore.patchInStore(id, { $set: erSet });
      await eStore.patch(id, { $set: erSet })
    }
    patchObj.value = {}
  }

  const to = ref(undefined);
  const autoSave = (path, val) => {
    nextTick(() => {
      const isEnrolled = _get(props.enrollment, `enrolled.${mbr.value?._id}`);
      if (isEnrolled) {
        patchObj.value[mbr.value._id] = { ...patchObj[mbr.value._id], [path]: val || val === false ? val : form.value[path] }
      } else patchObj.value[mbr.value._id] = val || val === false ? {[path]: val } : form.value
      if (to.value) clearTimeout(to.value);
      to.value = setTimeout(() => {
        save();
      }, 1000)
    })
  }

  const setForm = (tries = 0) => {
    if (hh.value?.members && mbr.value?._id) {
      form.value = formFn({ ...form.value, ...hh.value.members[mbr.value._id] });
    } else if (tries < 6) {
      setTimeout(() => {
        setForm(tries + 1)
      }, 500)
    }
  }
  watch(hh, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setForm()
    }
  }, { immediate: true })

  watch(head, (nv) => {
    if (nv?.address && !form.value.address) form.value.address = nv.address;
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
