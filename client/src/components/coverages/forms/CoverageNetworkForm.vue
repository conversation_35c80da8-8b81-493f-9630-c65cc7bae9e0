<template>
  <div class="_fw">
    <network-select @focus="focused = true" @blur="focused = false" :run="focused" :params="params" @update:model-value="addNetwork"></network-select>

    <q-expansion-item group="0" expand-icon="mdi-menu-down" v-for="(k, i) in Object.keys(byId)" :key="`net-${i}`">
      <template v-slot:header>
        <q-item class="_fw">
          <q-item-section>
            <q-item-label>{{byId[k]?.name}}</q-item-label>
            <q-item-label class="font-3-4r text-ir-deep">
              <span>Deductible Discount: <span class="tw-six text-primary">{{dollarString(form[k].ded_discount, '', 2)}}%</span></span>&nbsp;|&nbsp;<span>Coinsurance Discount: <span class="tw-six text-accent">{{dollarString(form[k].coins_discount, '', 2)}}%</span></span>
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
      <div class="_form_grid _f_g_r">
        <div class="_form_label">{{byId[k]?.name}}</div>
        <div class="q-pa-sm">
          <money-input filled dense label="Deductible Discount %" v-model="form[k].ded_discount" :decimal="2" prefix="" suffix="%" @update:model-value="emitUp">
          </money-input>
          <div class="q-py-xs"></div>
          <money-input filled dense label="Coinsurance Discount %" v-model="form[k].coins_discount" :decimal="2" prefix="" suffix="%" @update:model-value="emitUp">
          </money-input>
          <div class="row justify-end">
            <remove-proxy-btn name="Network Incentives" size="sm" @remove="remove(k)"></remove-proxy-btn>
          </div>
        </div>
      </div>
    </q-expansion-item>

  </div>
</template>

<script setup>
  import NetworkSelect from 'components/networks/lists/NetworkSelect.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {useNetworks} from 'stores/networks';
  import {useCoverages} from 'stores/coverages';
  import {dollarString} from 'symbol-syntax-utils';
  import {fakeId} from 'src/utils/global-methods';

  const planStore = usePlans();
  const networkStore = useNetworks();
  const coverageStore = useCoverages();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    plan: { required: false },
    coverage: { required: true },
    modelValue: { required: true }
  })
  const { item:fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  })
  const { item:fullCoverage } = idGet({
    store: coverageStore,
    value: computed(() => props.coverage)
  })

  const focused = ref(false);

  const params = computed(() => {
    const query = { _id: { $nin: Object.keys(fullCoverage.value?.networks || {})}};
    if(fullPlan.value) query.$or = [{ access: 'public' }, { plans: { $in: [fullPlan.value._id || fakeId]}}]
    else query.access = 'public'
    return { query }
  })

  const form = ref({});

  const nIds = computed(() => Object.keys(form.value || {}))
  const byId = computed(() => {
    const obj = {};
    for(let i = 0; i < nIds.value.length; i++) {
      // console.log('by id loop', i, nIds.value[i])
      obj[nIds.value[i]] = networkStore.getFromStore(nIds.value[i]).value
    }
    return obj
  })

  const { h$:n$ } = HFind({
    store: networkStore,
    pause: computed(() => !nIds.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: nIds.value }
        }
      }
    }),
    limit: computed(() => nIds.value.length)
  })

  const remove = (id) => {
    delete form.value[id];
    emit('update:model-value', form.value);
  };
  const addNetwork = (val) => {
    if(!(form.value || {})[val._id]){
      emit('update:model-value', {...form.value, [`${val._id}`]: {}});
    }
  }
  watch(() => props.modelValue, (nv) => {
    if(nv) form.value = { ...form.value, ...nv }
  }, { immediate: true })

  const emitUp = () => {
    emit('update:model-value', form.value)
  }
</script>

<style lang="scss" scoped>

</style>
