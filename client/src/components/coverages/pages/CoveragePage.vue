<template>
  <div class="_fw row justify-center bg-ir-a text-ir-text" v-if="coverage">
    <div class="_xsent bg-ir-bg pd8 pw2">

      <q-tab-panels class="_panel" v-model="showTab" animated transition-prev="jump-down" transition-next="jump-up">

        <q-tab-panel class="_panel" name="info">
          <div class="mnh500 _fw">
            <div class="row q-py-sm">
              <q-chip color="ir-bg2" class="tw-five" clickable @click="showTab = 'base'">
                <q-icon color="a5" name="mdi-chevron-left"></q-icon>
                <span class="q-ml-sm">Back to coverage page</span>
              </q-chip>
            </div>
            <carrier-info :coverage="coverage"></carrier-info>
          </div>
        </q-tab-panel>
        <q-tab-panel class="_panel" name="base">
          <div class="flex items-center">
            <q-img v-if="carrierLogo.url" :src="carrierLogo.url" class="h30 w30" fit="contain"></q-img>
            <div v-if="coverage.carrierName" class="q-px-sm q-py-xs font-1r tw-six alt-font text-ir-mid">{{ coverage.carrierName }}</div>
            <q-btn size="sm" :color="productTypes[coverage.type]?.color" dense flat icon="mdi-information"
                   @click="showTab = 'info'"></q-btn>
          </div>

          <div class="tw-six text-sm">{{ coverage.name }}</div>
          <div class="text-xxs">{{ coverage.description }}</div>
          <q-separator class="q-my-sm"></q-separator>
          <div class="row items-center q-py-sm">
            <type-chip :model-value="coverage.type"></type-chip>
            <div class="q-px-sm tw-five font-3-4r">for</div>
            <covers-chip :dark="dark" :model-value="coverage.covered"></covers-chip>
          </div>

          <div v-if="(enrollment?._id || enrolled?.length) && !coverage.shop" class="q-pa-sm _fw row items-end">
            <div class="font-3-4r"><span class="tw-six text-primary font-1-1-4r alt-font">{{
                dollarString($max(eRate * (mult || 1), 0), '$', 0)
              }}</span>
              {{ mult === 12 ? '/yr' : '/mo' }}
            </div>

            <div v-if="subsidy" class="_fw q-pb-md font-1r">
              <div class="_fw">
                <q-chip class="tw-six text-ir-bg1 bg-accent alt-font">
                  <q-icon class="q-mr-sm" color="ir-bg1" name="mdi-minus"></q-icon>
                  <span class="font-1-1-4r">{{ dollarString(actualSubsidy, '$', 0) }}</span><span
                    class="font-3-4r tw-four">{{ mult === 12 ? ' /yr' : ' /mo' }} Subsidy</span>
                  <q-tooltip class="tw-six text-xxs">Employer Contributions</q-tooltip>
                </q-chip>
              </div>
              <div class="_fw">
                <q-chip class="tw-six text-ir-off alt-font" color="ir-bg2">
                  <q-icon class="q-mr-sm" color="ir-off" name="mdi-equal"></q-icon>
                  <span
                      class="font-1-1-4r">{{
                      dollarString($max(((eRate || 0) * (mult || 1)) - (actualSubsidy), 0), '$', 0)
                    }}</span><span
                    class="font-3-4r tw-four">{{ mult === 12 ? ' /yr' : ' /mo' }} your cost</span>
                </q-chip>
              </div>
            </div>
          </div>


<!--          <ichra-card :dark="dark" v-if="coverage.ichra"-->
<!--                      v-bind="{ coverage, plan, person, full: true, enrollment  }"></ichra-card>-->
          <group-shop-card v-if="(coverage.shop || coverage.ichra) && ['hra', 'mm', 'hs'].includes(coverage.type)"
                           v-bind="{ coverage, plan, person, enrollment }"></group-shop-card>
          <rate-table :age="age" :enrollment="Object.keys(enrollment || {}).length ? enrollment : undefined"
                      :dark="dark"
                      v-else :model-value="coverage"></rate-table>
          <q-separator class="q-my-md"></q-separator>
          <detail-table :model-value="coverage"></detail-table>

          <template v-if="!limited">
            <div v-if="coverage?.video" class="_fw row justify-center q-py-lg">
              <div class="__vc">
                <q-video ratio="1.777" :src="video.url"></q-video>
              </div>
            </div>

            <q-chip :dark="dark" square class="bg-ir-bg2 q-mt-lg font-1r tw-six">
              <q-icon class="q-mr-sm" name="mdi-file-document" color="primary"></q-icon>
              Documents
            </q-chip>
            <q-list separator>
              <q-item v-for="(doc, i) in Object.keys(byId.byUpload).map(a => byId.byUpload[a])"
                      :key="`doc-${i}`">
                <q-item-section avatar>
                  <file-type-handler
                      height="85px"
                      width="65px"
                      :file="doc"
                      :url="doc.url"
                  ></file-type-handler>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ doc.info.name || 'Untitled' }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn dense flat icon="mdi-open-in-new" @click="openFile(doc, $router)"></q-btn>
                </q-item-section>
              </q-item>
            </q-list>
            <slot name="bottom">
              <template v-if="admin">
                <div class="q-py-md">
                  <coverage-groups :coverage="coverage" :plan="fullPlan">
                    <template v-slot:title="scope">
                      <div class="row">
                        <div class="__fl">Eligible Groups ({{ scope.on?.length || scope.planGroupCount || 0 }})</div>
                      </div>
                    </template>
                  </coverage-groups>
                </div>
                <div>
                  <coverage-boundary :coverage="coverage" :plan="plan">
                    <template v-slot:title="scope">
                      <div class="row items-center">
                        <div class="__fl">Eligible Locations({{ coverage.geo?.features?.length || 0 }})</div>
                        <q-chip v-if="coverage.geo?.features" clickable @click="scope.toggleOpen" color="transparent">
                          <q-icon name="mdi-eye"></q-icon>
                          <q-icon name="mdi-menu-down" :class="`_flip_off ${scope.open ? '_flip' : ''}`"></q-icon>
                        </q-chip>

                      </div>
                    </template>
                  </coverage-boundary>
                </div>
              </template>
            </slot>
          </template>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </div>
  <div v-else class="h600 _fw flex flex-center">
    <q-spinner size="50px" color="primary"></q-spinner>
  </div>
</template>

<script setup>
  import RateTable from 'components/coverages/cards/RateTable.vue';
  import TypeChip from 'components/coverages/cards/TypeChip.vue';
  import CoversChip from 'components/coverages/cards/CoversChip.vue';
  import DetailTable from 'components/coverages/cards/DetailTable.vue';
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import CoverageBoundary from 'components/coverages/forms/CoverageBoundary.vue';
  import CoverageGroups from 'components/coverages/forms/CoverageGroups.vue';
  // import IchraCard from 'components/coverages/ichra/cards/IchraCard.vue';
  import GroupShopCard from 'components/market/shop/enroll/GroupShopCard.vue';
  import CarrierInfo from 'components/coverages/info/CarrierInfo.vue';

  import {useCoverages} from 'stores/coverages';
  import {idGet} from 'src/utils/id-get';
  import {computed, reactive, ref, watch} from 'vue';
  import {openFile} from 'components/common/uploads/utils/files';
  import {useRoute} from 'vue-router';
  import {usePlans} from 'stores/plans';
  import {useUploads} from 'stores/uploads';
  import {getCoverageRate} from 'components/coverages/utils/display';
  import {$max, dollarString} from 'src/utils/global-methods';
  import {productTypes} from 'components/market/utils';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';
  import {useEnvStore} from 'stores/env';

  const route = useRoute();

  const store = useCoverages();
  const planStore = usePlans();
  const uploadStore = useUploads();
  const envStore = useEnvStore();

  const props = defineProps({
    limited: Boolean,
    mult: Number,
    dark: Boolean,
    modelValue: { required: true },
    plan: { required: false },
    person: { required: false },
    admin: { required: false },
    enrollment: { required: false },
    subsidy: Number,
    enrolled: Array,
    age: { required: false },
  })

  const showTab = ref('base')

  const coverageId = computed(() => {
    if(typeof props.modelValue === 'string') return props.modelValue;
    if(props.modelValue) return props.modelValue._id;
    return route.params.coverageId
  })
  const coverage = ref({})
  const setCoverage = async (id) => {
    const inStore = store.getFromStore(id)
    if(inStore.value) return coverage.value = inStore.value.clone();
    const cvg = await store.get(id);
    if(cvg) coverage.value = cvg;
  }

  watch(coverageId, async (nv, ov) => {
    if(nv && nv !== ov){
      setCoverage(nv)
    }
  }, { immediate: true })

  const { byId } = manageFindUploads({
    sources: reactive({
      data: (coverage.value.documents || []).map(a => {
        return { _id: coverage.value._id, doc: a, _fastjoin: { files: { doc: coverage.value._fastjoin?.files?.documents?.find(b => b._id === a.uploadId)}} }
      })
    }),
    paths: ['doc']
  })

  const eRate = computed(() => {
    if (!props.enrollment?.enrolled && !props.enrolled) return undefined;
    return (getCoverageRate({
      coverage: coverage.value,
      enrollment: props.enrollment,
      enrolled: props.enrolled,
      def_age: props.age
    }) || 0)
  })

  const actualSubsidy = computed(() => {
    return (props.subsidy || 0) * (props.mult || 1)
  })

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || route.params.planId || envStore.getPlanId),
  })

  const videoId = computed(() => coverage.value?.video?.uploadId);

  const { item: carrierLogo } = idGet({
    store: uploadStore,
    value: computed(() => coverage.value._fastjoin?.files?.carrierLogo || coverage.value.carrierLogo?.uploadId)
  })

  const { item: video } = idGet({
    store: uploadStore,
    value: computed(() => videoId.value || coverage.value.video)
  })
</script>

<style lang="scss" scoped>
  .__vc {
    width: 600px;
    max-width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px #dedede;
  }
</style>
