<template>
  <q-page class="bg-ir-bg1">
    <div class="row justify-center">
      <div class="_sent pd10 pw2 bg-white">
        <health-shares @update:modelValue="setHs" :model-value="hs"></health-shares>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import HealthShares from 'components/coverages/info/health-shares/cards/HealthShares.vue';
  import {idGet} from 'src/utils/id-get';
  import {useHealthShares} from 'stores/health-shares';
  import {useRoute, useRouter} from 'vue-router';

  const hsStore = useHealthShares();
  const router = useRouter();
  const route = useRoute();

const { item:hs } = idGet({
  store: hsStore,
  routeParamsPath: 'hsId'
})

  const setHs = (val) => {
    if(val){
      if(val._id !== hs.value._id) router.push({ ...route, params: { hsId: val._id } });
    } else router.push({ ...route, params: { hsId: '' } })
  }

</script>

<style lang="scss" scoped>

</style>
