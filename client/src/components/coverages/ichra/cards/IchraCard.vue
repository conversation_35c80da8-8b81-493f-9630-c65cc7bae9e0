<template>
  <div class="_fw __t">
    <div v-if="!policyId" class="tw-six q-py-sm row items-center q-pb-md">
      <div>Choose your own coverage&nbsp;&nbsp;-&nbsp;</div>
      <q-btn rounded no-caps flat :disable="opening" dense clickable @click="openIchra">
        <span class="q-mr-sm tw-five">Shop Now</span>
        <q-icon v-if="!opening" color="primary" name="mdi-open-in-new"></q-icon>
        <q-spinner v-else></q-spinner>
      </q-btn>
    </div>

    <div class="q-pa-md  _fw" v-else>
      <div v-if="enrollment?.status === 'open'" class="tw-six q-py-sm flex items-center">
        <div>Your Selected Coverage</div>
        <q-btn dense flat icon="mdi-pencil-box" color="accent" @click="openIchra"></q-btn>
        <q-btn :disable="loading" dense flat icon="mdi-refresh" color="primary" @click="loadPolicy(true)"></q-btn>
      </div>
      <div class="__pc">
        <policy-card v-if="policy" :dark="dark" :model-value="policy" :enrollment="enrollment"></policy-card>
        <div class="_fa" v-else>
          <ai-logo opaque></ai-logo>
        </div>
      </div>
    </div>

    <single-contribution-table v-bind="{ coverage, dark, enrollment, plan, person }"></single-contribution-table>

    <template v-if="full">
      <div class="q-px-sm q-pt-md">
        <b>Because one-size-fits-all... doesn't usually fit.</b>
        <br>
        This plan allows you to choose any plan that meets at least a "bronze" metal tier - <span
          class="alt-font tw-six">tax free</span> through the group plan. See below for how much that saves you.

        <div class="q-py-sm __rate _fw">
          <div class="row items-center q-py-sm">
            <div>Your Effective Income Tax Rate:</div>
            <q-chip outline clickable>
              <span>{{ dollarString(taxRate * 100, '', 0) }}%</span>
              <q-icon name="mdi-menu-down" class="q-ml-sm"></q-icon>
              <q-popup-proxy>
                <div class="w400 mw100 bg-white q-pa-md">
                  <q-list>
                    <q-item v-for="m in 10" :key="`rate-${m}`" clickable @click="taxRate = .05 * m">
                      <q-item-section>
                        <q-item-label>{{ 5 * m }}%</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
              </q-popup-proxy>
            </q-chip>
          </div>
          <table>
            <tbody>
            <tr class="font-3-4r tw-six text-grey-7">
              <td>Insurance Premium (Mo)</td>
              <td>Payroll Tax</td>
              <td>Income Tax Savings</td>
              <td>Net Premium</td>
            </tr>
            <tr class="alt-font" v-for="r in 5" :key="`rate-${r}`">
              <td>{{ dollarString(premiums[r], '$', 0) }}</td>
              <td class="tw-six text-s6">-{{ dollarString(premiums[r] * .0765, '$', 0) }}</td>
              <td class="tw-six text-s6">-{{ dollarString(premiums[r] * taxRate, '$', 0) }}</td>
              <td>{{ dollarString(premiums[r] * (1 - (.0765 + taxRate)), '$', 0) }}</td>
            </tr>
            </tbody>
          </table>
        </div>

        <div class="q-pa-sm tw-six font-1r" v-if="!enrollment?.ichra">Choose which household members will participate and you'll shop coverage options in the next step</div>
      </div>
    </template>
  </div>
</template>

<script setup>
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import SingleContributionTable from 'components/enrollments/utils/SingleContributionTable.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {computed, ref} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {useRouter} from 'vue-router';
  import {ichraPolicy} from 'components/coverages/ichra/utils/ichra-policy';
  import {convertCmsPerson, personToPeople} from 'components/market/household/utils';
  import {LocalStorage} from 'symbol-auth-client';
  import {useEnrollments} from 'stores/enrollments';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useHouseholds} from 'stores/households';
  import {useShops} from 'stores/shops';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {taxBreakdown} from 'components/households/utils/tax-tables';

  const router = useRouter();

  const erStore = useEnrollments();
  const pplStore = usePpls();
  const hhStore = useHouseholds();
  const shopStore = useShops();
  const junkStore = useJunkDrawers();

  const props = defineProps({
    dark: Boolean,
    coverage: { required: true },
    plan: { required: false },
    person: { required: false },
    full: Boolean,
    enrollment: { required: false }
  })

  const { policy, policyId, loadPolicy, loading } = ichraPolicy({
    enrollment: computed(() => props.enrollment),
    coverage: computed(() => props.coverage)
  })

  const { item:ermt } = idGet({
    store: erStore,
    value: computed(() => props.enrollment)
  })
  const { item:prsn } = idGet({
    store: pplStore,
    value: computed(() => ermt.value.person)
  })
  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => prsn.value?.household)
  })
  const { item: shop } = idGet({
    store: shopStore,
    value: computed(() => ermt.value.shop)
  })

  const income = computed(() => {
    return taxBreakdown(hh.value, {})
  })


  const taxRate = ref(.2);
  const premiums = {
    1: 300,
    2: 500,
    3: 800,
    4: 1100,
    5: 1500
  }

  const opening = ref(false);
  const openIchra = async () => {
    opening.value = true;
    let shopId = ermt.value?.shop;
    const pplIds = Object.keys(hh.value.members || {})
    const participants = (ermt.value?.coverages || {})[props.coverage._id]?.participants || pplIds
    if (!shopId) {
      /** Create shop using household/enrollment stats */
      const pplRes = await pplStore.find({ query: { _id: { $in: pplIds } } })
          .catch(err => console.error(`Error finding people: ${err.message}`))

      let people = (pplRes?.data || []).map(a => personToPeople({ ...a, inactive: !participants.includes(a._id) }));

      const address = (ermt.value.address || prsn.value.address) || {}
      let place;
      if (address?.postal) {
        const dr = await junkStore.find({ query: { $limit: 1, itemId: `zips|${address.postal.substring(0, 3)}` } })
            .catch(err => console.error(`Error getting places drawer: ${err.message}`))
        if (dr) {
          const z = dr.data[0]?.data[address.postal] || {};
          place = { countyfips: z.fips, zipcode: address.postal, state: z.state }
        }
      }
      const newShop = {
        person: prsn.value._id || ermt.value.person,
        plan: props.plan._id,
        planYear: ermt.value.planYear,
        version: ermt.value.version,
        host: props.plan.host || LocalStorage.getItem('host_id'),
        enrollment: ermt.value._id,
        plan_coverage: props.coverage._id,
        stats: {
          people,
          ...personToPeople(prsn.value),
          city: address.city,
          place,
          risk: 3,
          income: income.value.magi
        },
        useAptc: true
      }
      const added = await shopStore.create(newShop);
      shopId = added._id
    } else if (participants.length) {
      const people = [...shop.value.stats?.people || []]
      let patch;
      for (let i = 0; i < people.length; i++) {
        if (!participants.includes(people[i]._id)) {
          if (!people[i].inactive) {
            patch = true;
            people[i].inactive = true;
          }
        }
      }
      if (patch) await shopStore.patch(shop.value._id, { $set: { ['stats.people']: people } })
    }
    if (shopId !== ermt.value.shop) await erStore.patch(ermt.value._id, { shop: shopId })
    const { href } = router.resolve({ name: 'shop', params: { shopId } });
    window.open(href, '_blank')
  }
</script>

<style lang="scss" scoped>
  .__t {

  }

  .__rate {
    table {
      text-align: right;
      border-collapse: collapse;
      width: 100%;

      td {
        padding: 4px 7px;
        border-bottom: solid .3px #999;
      }
    }
  }

  .__pc {
    margin: 10px 0;
    border: solid 2px var(--q-a10);
    border-radius: 15px;
    padding: 30px 20px;
  }
</style>
