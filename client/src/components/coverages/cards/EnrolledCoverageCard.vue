<template>
  <div class="_fw relative-position">
    <div class="tw-six font-3-4r text-ir-mid">{{policy?.carrierName}}</div>
    <div class="tw-six font-1r">{{ coverage?.name }}</div>
    <q-separator class="q-my-sm"></q-separator>
    <div class="row items-center q-py-sm">
      <type-chip :model-value="coverage?.type"></type-chip>
      <div class="q-px-sm tw-five font-3-4r">for</div>
      <covers-chip :model-value="coverage?.covered"></covers-chip>
      <table v-if="!coverage?.shop">
        <tbody>
        <tr>
          <td></td>
          <td>Premium</td>
          <td>Deductible</td>
          <td>Max OOP</td>
        </tr>
        <tr>
          <td>Costs</td>
          <td>{{ dollarString(getCoverageRate({coverage, enrollment, rate}), '$', 0) }}</td>
          <td>
            {{ dollarString(getDeductible(coverage, typeKey, { display: false }), '$', 0) }}
          </td>
          <td>
            {{ dollarString(getmoop(coverage, typeKey, { display: false }), '$', 0) }}
          </td>
        </tr>
        </tbody>
      </table>
      <group-shop-card v-else v-bind="{ coverage, plan: enrollment?.plan, person: enrollment?.person, enrollment }"></group-shop-card>
<!--      <ichra-card v-else v-bind="{ coverage, plan: enrollment?.plan, person: enrollment?.person, enrollment }"></ichra-card>-->
      <q-list separator dense>
        <q-item v-for="(doc, i) in coverage._fastjoin?.files?.documents || coverage.documents || []" :key="`doc-${i}`">
          <q-item-section avatar>
            <file-type-handler
                height="100px"
                width="75px"
                :file="doc"
                :url="getFile({ obj: coverage, path: ['documents'], index: i})"
            ></file-type-handler>
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ doc.info.name || 'Untitled' }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-btn dense flat icon="mdi-open-in-new" @click="openFile(doc, $router)"></q-btn>
          </q-item-section>
        </q-item>
      </q-list>

      <div class="row items-center q-pt-md" v-if="coverage?.ichra">
        <div class="font-7-8r tw-six">Your Policy:</div>
        <q-chip v-if="enrollment?.ichra && !policy?.premium && !(enrollment?.coverages || {})[coverage._id]?.optOut" clickable
                @click="openIchra" label="⚠️ Not Selected"></q-chip>
        <div class="__pt" v-else>
          <table>
            <tbody>
            <tr>
              <td>Issuer</td>
              <td>{{ policy.carrierName }}</td>
            </tr>
            <tr>
              <td>Name</td>
              <td>{{ policy.name }}</td>
            </tr>
            <tr>
              <td>Metal</td>
              <td>{{policy.metal}}</td>
            </tr>
            <tr>
              <td>Premium</td>
              <td class="alt-font text-accent tw-six">{{ dollarString(policy.premium, '$', 2)}}</td>
            </tr>
            <tr>
              <td>Deductible</td>
              <td class="alt-font">{{ dedString }}</td>
            </tr>
            <tr>
              <td>Max OOP</td>
              <td class="alt-font">{{ moopString }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import CoversChip from 'components/coverages/cards/CoversChip.vue';
  import TypeChip from 'components/coverages/cards/TypeChip.vue';
  // import IchraCard from 'components/coverages/ichra/cards/IchraCard.vue';
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import GroupShopCard from 'components/market/shop/enroll/GroupShopCard.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useCoverages} from 'stores/coverages';
  import {enrolledRate} from 'components/coverages/rates/utils';
  import {getCoverageRate, getDeductible, getmoop} from 'src/components/coverages/utils/display';
  import {dollarString} from 'src/utils/global-methods';
  import {getFile} from 'src/utils/fs-utils';
  import {openFile} from 'components/common/uploads/utils/files';
  import {useRouter} from 'vue-router';
  import {ichraPolicy} from 'components/coverages/ichra/utils/ichra-policy';

  const router = useRouter();
  const store = useCoverages();

  const props = defineProps({
    modelValue: { required: true },
    enrollment: { required: true }
  })
  const { item: coverage } = idGet({
    value: computed(() => props.modelValue),
    store
  })

  const { policy } = ichraPolicy({
    enrollment: computed(() => props.enrollment),
    coverage
  })

  const dedString = computed(() => {
    if(!policy.value.deductible) return 'N/A'
    const single = policy.value.deductible.medical?.single || 0
    return `Single: ${dollarString(single, '$', 0)} - Family: ${dollarString(policy.value.deductible.medical?.family || single, '$', 0)}`
  })
  const moopString = computed(() => {
    if(!policy.value.moop) return 'N/A'
    const single = policy.value.moop.medical?.single || 0
    return `Single: ${dollarString(single, '$', 0)} - Family: ${dollarString(policy.value.moop.medical?.family || single, '$', 0)}`
  })


  const { rate } = enrolledRate({ enrollment: computed(() => props.enrollment), coverage })

  const typeKey = computed(() => props.enrollment?.type);

  const openIchra = () => {
    const { href } = router.resolve({ name: 'ichra-enroll', params: { enrollmentId: props.enrollment._id } });
    window.open(href, '_blank')
  }

</script>

<style lang="scss" scoped>

  table {
    margin: 10px 0;
    width: 100%;
    border-collapse: collapse;

    td {
      border-bottom: solid .3px #999;
      text-align: right;
      padding: 6px 8px;
    }
  }

  .__pt {
    width: 100%;
    overflow-x: scroll;

    table {
      width: 100%;
      border-collapse: collapse;
      text-align: left;

      tr {
        td:first-child {
          font-weight: 600;
          color: #999;
          font-size: .8rem;
        }

        td {
          text-align: left;
          font-size: .9rem;
          border-bottom: solid .3px #999;
          padding: 6px 8px;
        }
      }
    }
  }
</style>

