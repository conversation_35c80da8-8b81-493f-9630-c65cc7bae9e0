<template>
  <div class="_fw q-pa-sm __cam_term">
    <div class="_fw">
      <div class="q-pa-sm tw-six font-1r">Remove Compensation Arrangements</div>
      <div class="row items-center q-pa-sm">
        <q-radio v-model="terminate" :val="true" label="Terminate"></q-radio>
        <q-radio v-model="terminate" :val="false" label="Remove Comp Only"></q-radio>
        <q-space></q-space>
        <q-checkbox @click="selectAll" :model-value="selected.length === ids?.length"></q-checkbox>
      </div>
      <div class="__c" v-for="(cam, i) in c$.data" :key="`cam-${i}`" @click="select(cam)">
        <div>
          <div class="q-pa-sm tw-five">{{ cam.name }}</div>
          <div class="__r">
            <div>Person</div>
            <div>
              <q-chip color="ir-bg2">
                <default-avatar :model-value="cam._fastjoin.person" :use-atc-store="useAtcStore"></default-avatar>
                <span>{{ cam._fastjoin.person?.name }}</span>
              </q-chip>
            </div>
          </div>
          <div class="__r">
            <div>Enrollment</div>
            <div class="flex items-center" v-if="(cam._fastjoin?.enrollments || [])[0]">
              <q-chip color="ir-bg2" text-color="ir-text">
                <span>{{ cam._fastjoin.enrollments[0].version }}</span>
              </q-chip>
              <status-chip :model-value="cam._fastjoin.enrollments[0].status"></status-chip>
            </div>
            <div v-else class="q-pa-sm font-7-8r text-italic">None</div>

          </div>
        </div>
        <div class="_fh flex flex-center q-pa-sm">
          <q-icon size="22px" color="green" name="mdi-checkbox-marked" v-if="selected.includes(cam._id)"></q-icon>
          <q-icon v-else name="mdi-checkbox-blank-outline" size="22px"></q-icon>
        </div>
      </div>
    </div>
    <div class="_fw q-py-md" v-if="selected.length">
      <div class="row justify-end" v-if="!confirm">
        <q-btn no-caps flat @click="setConfirm">
          <span v-if="terminate">Terminate {{ $possiblyPlural('People', selected) }}</span>
          <span v-else>Remove {{ $possiblyPlural('Compensation Arrangement', selected) }}</span>
          <q-icon class="q-ml-sm" color="red" name="mdi-delete"></q-icon>
        </q-btn>
      </div>
      <q-slide-transition>
        <div class="_fw" v-if="confirm">
          <div class="row justify-center q-py-sm">
            <inline-date label="Termination Date" v-model="termDate"></inline-date>
          </div>
          <div class="q-py-sm tw-six font-1r text-center">Enter the below letters to confirm</div>
          <div class="q-py-sm tw-six text-center font-1-1-2r text-ir-deep">{{confirmText}}</div>
          <div class="row justify-center">
            <q-input dense filled class="mw300" v-model="confirmEnter"></q-input>
          </div>
          <div class="row justify-center q-py-sm">
            <q-btn class="q-mx-sm" no-caps flat @click="confirm = false">
              <span class="q-mr-sm">Cancel</span>
              <q-icon name="mdi-close" color="red"></q-icon>
            </q-btn>
            <q-btn :disable="confirmEnter.toLowerCase() !== confirmText.toLowerCase()" class="q-mx-sm" no-caps flat>
              <span class="q-mr-sm">Confirm</span>
              <q-icon name="mdi-check-circle" color="green"></q-icon>
            </q-btn>
          </div>
        </div>
      </q-slide-transition>
    </div>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';

  import {loginPerson} from 'stores/utils/login';
  import {useCams} from 'stores/cams';
  import {HFind} from 'src/utils/hFind';
  import {useAtcStore} from 'stores/atc-store';
  import {computed, ref} from 'vue';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import InlineDate from 'components/common/dates/InlineDate.vue';

  const { person } = loginPerson()

  const camsStore = useCams();

  const selected = ref([]);
  const select = (cam) => {
    const idx = selected.value.indexOf(cam._id);
    if (idx > -1) selected.value.splice(idx, 1);
    else selected.value.push(cam._id);
  }
  const selectAll = () => {
    if (selected.value.length === props.ids.length) selected.value = [];
    else selected.value = props.ids;
  }

  const props = defineProps({
    ids: { required: true, type: Array }
  })

  const terminate = ref(false);
  const confirm = ref(false);
  const confirmText = ref('');
  const confirmEnter = ref('');

  const setConfirm = () => {
    confirm.value = true;
    confirmText.value = Math.random().toString(36).slice(2, 6);
  }

  const termDate = ref(new Date())
  const remove = async () => {
    if(terminate.value){
      await camsStore.patch(selected.value[0], { terminated: true, terminatedAt: termDate.value, terminatedBy: person.value._id }, { runJoin: {multi_term: { ids: selected.value }}})
    } else {
      await camsStore.remove(null, {query: { _id: { $in: selected.value }}})
    }
  }

  const { h$: c$ } = HFind({
    store: camsStore,
    params: computed(() => {
      return {
        query: {
          _id: { $in: props.ids || [] }
        },
        runJoin: {
          cams_enrollments: true
        }
      }
    })
  })

</script>

<style lang="scss" scoped>

  .__cam_term {
    width: 100%;
    display: grid;
    grid-template-rows: 1fr auto;

    > div {
      &:first-child {
        overflow-y: scroll;
      }
    }
  }
  .__c {
    width: 100%;
    border-bottom: solid 1px var(--ir-light);
    padding: 10px;
    background: white;
    transition: all .3s;
    cursor: pointer;
    display: grid;
    grid-template-columns: 1fr auto;

    &:hover {
      box-shadow: 0 2px 12px var(--ir-light);
    }
  }

  .__r {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;

    > div {
      padding: 5px;

      &:first-child {
        font-size: .8rem;
        font-weight: 600;
        color: var(--ir-mid);
      }
    }
  }
</style>
