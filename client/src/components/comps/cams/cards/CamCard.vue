<template>
  <div class="_fw">
    <div class="flex items-center">
      <div class="font-1r tw-six">Position:&nbsp;&nbsp;</div>
      <q-chip clickable color="primary">
          <span class="tw-six text-white font-1-1-8r alt-font">
            {{ cam?.name }}
          </span>
        <q-icon class="q-ml-sm" v-if="options?.length > 1" name="mdi-menu-down" color="primary" size="15px"></q-icon>
        <q-menu>
          <div class="w300 br10 bg-white">
            <q-list separator>
              <q-item-label header>Your positions</q-item-label>
              <q-item v-for="(pos, i) in options" :key="`pos-${i}`" clickable @click="idx = i">
                <q-item-section>
                  <q-item-label>{{ pos.name }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon :name="`mdi-checkbox-${idx === i ? 'marked' : 'blank'}-outline`"
                          :color="idx === i ? 'green' : 'ir-grey-6'"></q-icon>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-menu>
      </q-chip>
    </div>

    <q-separator color="transparent" class="q-my-sm"></q-separator>

    <pay-table :model-value="cam"></pay-table>
  </div>
</template>

<script setup>
  import PayTable from 'components/comps/cards/PayTable.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useCams} from 'stores/cams';
  const store = useCams();

  const props = defineProps({
    modelValue: { required: true },
    options: Array
  })

  const { item: cam } = idGet({
    store,
    value: computed(() => props.modelValue)
  })
</script>

<style lang="scss" scoped>

</style>
