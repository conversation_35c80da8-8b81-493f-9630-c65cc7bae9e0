<template>
  <div class="_fw relative-position">
    <div class="row items-center q-py-sm" v-if="comp?.person">
      <div class="tw-five font-3-4r">For: </div>
      <default-chip :model-value="comp.person" :store="pplsStore" :use-atc-store="useAtcStore"></default-chip>
    </div>
    <div class="row q-pb-sm">
      <q-chip color="white" square class="font-1r tw-six">{{ comp?.name }}</q-chip>
    </div>
    <table>
      <tr class="__r">
        <td>Pay:</td>
        <td>
          <pay-chip color="p0" :model-value="comp"></pay-chip>
        </td>
      </tr>
      <tr>
        <td>Extras:</td>
        <td>
          <extras-chip color="a0" :model-value="comp?.extras"></extras-chip>
        </td>
      </tr>
    </table>

    <div class="row justify-end q-pt-sm">
      <terms-chip color="white" :model-value="comp?.terms"></terms-chip>
    </div>

    <div class="row">
      <q-chip v-if="service === 'comps'" size="sm" color="white" clickable @click="$copyTextToClipboard(comp.key)">
        <q-icon name="mdi-label" color="primary"></q-icon>
        <span class="q-ml-xs">{{comp.key}}</span>
      </q-chip>
      <hire-date :edit="editing" v-else :model-value="comp"></hire-date>
    </div>

    <common-dialog setting="right" v-model="dialog">
      <div class="_fw q-pa-md">
      <comp-form
          :service="service"
          :model-value="comp"
          :org="comp.org"
      ></comp-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import TermsChip from 'src/components/comps/cards/TermsChip.vue';
  import ExtrasChip from 'src/components/comps/cards/ExtrasChip.vue';
  import PayChip from 'src/components/comps/cards/PayChip.vue';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import CompForm from 'src/components/comps/forms/CompForm.vue';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {$copyTextToClipboard, $successNotify} from 'src/utils/global-methods';
  import {dualStore} from 'components/comps/utils';
  import {useAtcStore} from 'stores/atc-store';
  import { usePpls } from 'stores/ppls';
  import HireDate from 'components/comps/cams/cards/HireDate.vue';

  const pplsStore = usePpls();

  const props = defineProps({
    modelValue: { required: true },
    service: { default: 'comps' },
    editing: Boolean
  })

  const srv = computed(() => props.service);

  const { store } = dualStore(srv)

  const { item: comp } = idGet({
    value: computed(() => props.modelValue),
    store: store.value
  })

  const dialog = ref(false);

  const remove = () => {
    store.value.remove(comp.value._id)
        .then(() => {
          $successNotify('Removed package')
        })
    store.value.removeFromStore(comp.value._id);
  }
</script>

<style lang="scss" scoped>
  table {
    text-align: right;
    width: 100%;
  }

  td {
    width:0.1%;
    white-space: nowrap;
    padding: 3px;
  }
 .__r {
   td {
     border-bottom: solid .5px rgba(0,0,0,.4);
     border-collapse: collapse;
   }
 }
</style>
