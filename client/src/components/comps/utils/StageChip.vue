<template>
  <q-chip v-bind="{ color: 'ir-bg2', clickable: true, ...$attrs}">
    <q-avatar v-if="!multiple" size="20px" :color="stages[mv]?.color || 'ir-grey-3'"></q-avatar>
    <span v-if="!multiple">{{ stages[mv]?.label || emptyLabel || picker ? 'Select Stage' : ' - ' }}</span>
    <span v-else>{{emptyLabel || picker ? 'Select Stage' : ' - '}}</span>
    <slot name="side">
      <q-icon v-if="picker" name="mdi-menu-down"></q-icon>
    </slot>
    <template v-if="picker">
      <q-popup-proxy>
        <div class="w300 mw100 q-pa-sm">
          <q-list dense separator>
            <q-item v-for="(k, i) in Object.keys(c.stages)" :key="`stage-${i}`" clickable @click="select(k)">
              <q-item-section avatar>
                <q-avatar size="25px" :color="c.stages[k].color"></q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ c.stages[k].label }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-popup-proxy>
    </template>
  </q-chip>
  <template v-if="picker && multiple">
    <q-chip v-bind="{ color: 'ir-bg2', ...$attrs}" v-for="(k, i) in mv" :key="`k-${i}`">
      <q-avatar size="20px" :color="stages[k]?.color || 'ir-grey-3'"></q-avatar>
      <span class="q-mr-xs">{{ stages[k]?.label || 'Unknown Stage' }}</span>
      <q-btn @click="remove(k)" dense flat size="sm" color="red" icon="mdi-close"></q-btn>
    </q-chip>
  </template>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useComps} from 'stores/comps';

  const compStore = useComps();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    comp: { required: false },
    picker: Boolean,
    multiple: Boolean,
    emptyLabel: String
  })

  const mv = computed(() => {
    if(props.multiple) return Array.isArray(props.modelValue) ? props.modelValue : []
    else return props.modelValue
  })

  const { item: c } = idGet({
    store: compStore,
    value: computed(() => props.comp)
  })
  const stages = computed(() => c.value.stages || {})

  const remove = (k) => {
    const obj = { ...props.modelValue };
    delete obj[k];
    emit('update:model-value', obj)
  }

  const select = (k) => {
    if(props.multiple) {
      const idx = mv.value.indexOf(k);
      if(idx > -1) {
        const arr = [...mv.value];
        arr.splice(idx, 1);
        emit('update:model-value', arr);
      } else emit('update:model-value', [...mv.value, k]);
    } else emit('update:model-value', k);
  }
</script>

<style lang="scss" scoped>

</style>
