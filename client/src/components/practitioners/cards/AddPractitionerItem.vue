<template>
  <q-item @click="dialog = !dialog" v-bind="{ clickable: true, dense: true, ...$attrs}">
    <slot name="avatar">
      <q-item-section avatar>
        <q-icon name="mdi-plus" color="primary"></q-icon>
      </q-item-section>
    </slot>
    <q-item-section>
      <q-item-label>{{label}}</q-item-label>
    </q-item-section>
    <slot name="side"></slot>

    <common-dialog size="smmd" v-model="dialog">
      <practitioner-form :model-value="practitioner" @update:model-value="emitUp"></practitioner-form>
    </common-dialog>
  </q-item>
</template>

<script setup>
  import PractitionerForm from 'components/practitioners/forms/PractitionerForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePractitioners} from 'stores/practitioners';
  import {useAtcStore} from 'stores/atc-store';

  const store = usePractitioners()
  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    label: { default: 'Add Practitioner', type: String }
  })

  const { item:practitioner } = idGet({
    store,
    value: computed(() => props.modelValue),
    useAtcStore
  })

  const dialog = ref(false);
  const emitUp = (val) => {
    emit('update:model-value', val)
    dialog.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
