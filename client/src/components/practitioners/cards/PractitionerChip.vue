<template>
  <default-chip
      v-if="practitioner?._id || defaultName"
      :model-value="practitioner"
      :bg-in="bg"
      :hide-avatar="hideAvatar"
      :default-name="defaultName"
      name-path="displayName"
      avatar-letter-path="firstName"
      :chip-attrs="chipAttrs"
      :use-atc-store="useAtcStore"
      @remove="rmv"
  >
    <template v-slot:menu="scope">
      <slot name="menu" :item="scope.item"></slot>
    </template>
    <template v-slot:right>
      <slot name="right"></slot>
    </template>
  </default-chip>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {usePractitioners} from 'stores/practitioners';
  import {getGenderColor} from 'components/households/utils';
  import {useAtcStore} from 'stores/atc-store';

  const store = usePractitioners();

  const emit = defineEmits(['remove']);
  const props = defineProps({
    modelValue: { required: true },
    chipAttrs: Object,
    hideAvatar: Boolean,
    defaultName: String
  })

  const { item:p } = idGet({
    store,
    value: computed(() => props.modelValue),
    useAtcStore
  })

  const rmv = () => {
    emit('remove', p.value)
  }

  const practitioner = computed(() => {
    const pr = p.value || { firstName: '' }
    return {
      displayName: `${pr.name_prefix || ''}${pr.name_prefix ? ' ' : ''}${pr.firstName || ''}${pr.lastName ? ' ' : ''}${pr.lastName || ''}${pr.credential ? ' ' + pr.credential : ''}`,
      ...pr,
    }
  })

  const bg = computed(() => {
    return getGenderColor(p.value);
  })
</script>

<style lang="scss" scoped>

</style>
