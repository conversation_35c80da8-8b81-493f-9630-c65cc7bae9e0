<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent pd5 pw2">

        <q-tab-panels class="_panel" v-model="tab" animated>
          <q-tab-panel class="_panel" name="base">
            <div class="row">
              <div class="col-12 col-md-6 q-pa-sm">
                <div class="font-1-1-8r tw-six q-pa-md alt-font">Health plan service bid</div>
                <div class="_form_grid">
                  <div class="_form_label">Plan</div>
                  <div class="q-pa-sm">
                    <div class="font-1r tw-five q-pa-sm">{{plan?.name}}</div>
                  </div>
                  <div class="_form_label">Sponsor</div>
                  <div class="q-pa-sm">
                    <org-chip :model-value="plan?.org"></org-chip>
                  </div>
                  <div class="_form_label">Role</div>
                  <div class="q-pa-sm">
                    <role-chip color="ir-grey-2" :model-value="offer?.role"></role-chip>
                  </div>
                  <div class="_form_label">Fee</div>
                  <div class="q-pa-sm">
                    <fee-chip :model-value="offer"></fee-chip>
                  </div>
                  <div class="_form_label">By</div>
                  <div class="q-pa-sm">
                    <default-chip :model-value="host" name-path="dba" :use-atc-store="useAtcStore"></default-chip>
                  </div>
                  <div class="_form_label">Contract</div>
                  <div class="q-pa-sm">
                    <q-chip clickable @click="tab = 'contract'" color="ir-bg2" text-color="ir-text" class="tw-five">
                      <q-icon name="mdi-file-document" color="a4" class="q-mr-sm"></q-icon>
                      <span>{{contract.name}}</span>
                    </q-chip>
                  </div>
                </div>

              </div>
              <div class="col-12 col-md-6 q-pa-sm">
                <div class="font-1-1-8r tw-six alt-font q-mt-md q-pa-md">About {{host?.dba}}</div>
                <div class="__c" v-if="videoId">
                  <q-video ratio="1.777" :src="video?.url"></q-video>
                </div>
                <div class="_fw q-pt-lg font-1r">
                  <div v-html="host?.description"></div>
                </div>
                <div class="_fw q-py-md">
                  <div class="_fw" v-for="(item, i) in items" :key="`item-${i}`">
                    <div class="row items-center">
                      <q-btn dense flat>
                        <q-icon v-bind="obj[item].icon"></q-icon>
                      </q-btn>
                      <template v-for="(s, idx) in host[item] || []" :key="`s-${i}-${idx}`">
                        <q-chip dense square color="transparent" :label="obj[item].format(s)"></q-chip>
                        <div v-if="idx < host[item].length - 1">|</div>
                      </template>
                    </div>
                    <q-separator v-if="i < items.length - 1" class="q-my-sm"></q-separator>
                  </div>
                </div>
              </div>
            </div>

          </q-tab-panel>
          <q-tab-panel class="_panel" name="contract">
            <div class="q-py-sm">
              <q-btn flat no-caps @click="tab = 'base'">
                <q-icon name="mdi-chevron-left" color="primary"></q-icon>
                <span class="tw-six q-ml-sm">Back</span>
              </q-btn>
            </div>
            <div class="_fw pw2">
              <div class="q-pa-md font-1-1-8r tw-six alt-font">Contract</div>
              <contract-display :model-value="offer.contract"></contract-display>
            </div>
          </q-tab-panel>
        </q-tab-panels>


        <div class="row justify-end q-py-lg" v-if="canEdit.ok">
          <remove-proxy-btn v-if="offer.status !== 'active'" remove-label="Approve Bid? By Approving this bid you agree to the associated contract terms of the attached contract" :icon="undefined" color="ir-text" two-step push class="tw-six" no-caps @remove="approveBid">
            <span>Approve Bid</span>
            <q-icon class="q-ml-sm" name="mdi-check-circle" color="green"></q-icon>
          </remove-proxy-btn>
          <q-btn v-else flat no-caps>
            <span class="tw-six">Offer Accepted</span>
            <q-icon class="q-ml-sm" color="green" name="mdi-check-circle"></q-icon>
          </q-btn>
        </div>


        </div>
    </div>
  </div>
</template>

<script setup>
  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import FeeChip from 'components/plans/team/cards/FeeChip.vue';
  import ContractDisplay from 'components/contracts/cards/ContractDisplay.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'stores/atc-store';
  import {computed, ref} from 'vue';
  import {useOffers} from 'stores/offers';
  import {useHosts} from 'stores/hosts';
  import {useRoute} from 'vue-router';
  import {usePlans} from 'stores/plans';
  import {useUploads} from 'stores/uploads';
  import {useContracts} from 'stores/contracts';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';

  const offerStore = useOffers();
  const hostStore = useHosts();
  const planStore = usePlans();
  const uploadStore = useUploads();
  const contractStore = useContracts();

  const route = useRoute()

  const { login } = loginPerson();

  const props = defineProps({
    modelValue: { required: false }
  })

  const tab = ref('base')

  const { item:offer } = idGet({
    store: offerStore,
    value: computed(() => props.modelValue || route.params.offerId)
  })

  const { item:contract } = idGet({
    store: contractStore,
    value: computed(() => offer.value.contract),
    def: {}
  })

  const { item:host } = idGet({
    store: hostStore,
    value: computed(() => offer.value?.host)
  })
  const { item:plan } = idGet({
    store: planStore,
    value: computed(() => offer.value?.plan)
  })

  const videoId = computed(() => host.value?.videos?.intro?.plan?.uploadId);

  const { item:video } = idGet({
    store: uploadStore,
    value: videoId
  })

  const obj = {
    'phones': {icon: { name: 'mdi-phone', color: 'accent'}, format: (v) => v.number.national},
    'emails': {icon: { name: 'mdi-email', color: 'primary' }, format:(v) => v},
    'locations': {icon: { name: 'mdi-map-marker', color: 'secondary' }, format:(v) => v.formatted}
  }
  const items = computed(() => {
    const list = [];
    const keys = Object.keys(obj)
    const hv = host.value || {}
    for(let i = 0; i < keys.length; i++){
      if(hv[keys[i]]) list.push([keys[i]])
    }
    return list;
  })

  const approveBid = async () => {
    await offerStore.patch(offer.value._id, { status: 'active' })
  }

  const { canEdit } = clientCanU({
    subject: offer,
    or: true,
    cap_subjects: computed(() => [plan.value.org, plan.value._id]),
    caps: computed(() => [[`orgs:${plan.value.org}`, ['orgAdmin']], [`orgs:${plan.value.org}`, ['WRITE']], [`plans:${plan.value._id}`, ['planAdmin']]]),
    login
  })
</script>

<style lang="scss" scoped>

  .__c {
    width: 100%;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 2px 2px 12px -2px #dedede;
  }
</style>
