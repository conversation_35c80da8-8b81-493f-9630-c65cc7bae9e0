<template>
  <div class="_fw">
    <div class="row">
      <div :class="`col-12 col-md-${stack ? '12' : '6'} q-pa-sm`">
        <div class="_form_grid _f_g_r">
          <template v-if="form?._id">
            <div class="_form_label">Avatar</div>
            <div class="q-pa-sm">
              <image-select-or-upload v-model="form.avatar" @update:model-value="autoSave('avatar')"></image-select-or-upload>
<!--              <image-form file-path="avatar" v-model="form.avatar" @update:model-value="autoSave('avatar')"></image-form>-->
            </div>
          </template>
          <div class="_form_label">DBA</div>
          <div class="q-pa-sm">
            <q-input @blur="addDba"
                     :error="!!searchRes.total"
                     :error-message="`${maybeDba} is not available`"
                     :hint="!hasSearched || searchRes.total ? undefined : `${maybeDba} is available!`" filled
                     :model-value="maybeDba" @update:model-value="setMaybeDba">
              <template v-slot:append>
                <q-spinner color="accent" v-if="loading"></q-spinner>
              </template>
            </q-input>
          </div>
          <template v-if="form?.dba">
            <div class="_form_label">Description</div>
            <div class="q-pa-sm">

              <q-editor
                  :id="form?._id || 'host-form'"
                  v-model="form.description"
                  @update:model-value="autoSave('description')"
                  min-height="30px"
                  dense
                  flat
                  content-class="bg-ir-grey-2 q-px-md"
                  :toolbar="[['bold', 'italic', 'strike', 'underline', 'code', 'link', 'unordered', 'ordered']]"
              ></q-editor>
              <div class="q-pa-sm row items-center">
                <div class="col-8">
                  <emoji-picker @update:model-value="pickEmoji"></emoji-picker>
                </div>
              </div>
            </div>
            <div class="_form_label">NPN</div>
            <div class="q-pa-sm">
              <q-input @blur="autoSave('npn')" dense filled v-model="form.npn"></q-input>
            </div>
          </template>

        </div>

      </div>
      <div :class="`col-12 col-md-${stack ? '12' : '6'} q-pa-sm`">
        <div class="_form_grid _f_g_r">
          <template v-if="form?.dba">
            <div class="_form_label">Subdomain</div>
            <div class="q-pa-sm">
              <subdomain-field
                  filled v-model="form.subdomain"
                  @available="setSubdomain"
                  @update:model-value="autoSave('subdomain')"
              ></subdomain-field>
            </div>


            <div class="_form_label">Roles</div>
            <div class="q-pa-sm">
              <role-chip empty-label="Select Role" @update:model-value="addRole" picker></role-chip>
              <role-chip v-for="(role, i) in form.roles" :key="`host-${role}`" :model-value="role">
                <template v-slot:right>
                  <q-btn size="sm" dense flat icon="mdi-close" color="red" @click="removeRole(i)"></q-btn>
                </template>
              </role-chip>
            </div>
            <div class="_form_label">Phones</div>
            <div class="q-pa-sm">
              <phone-input :input-attrs="{ dense: true, filled: true }"
                           @update:model-value="addItem('phones', $event)"></phone-input>
              <q-list separator>
                <q-item v-for="(ph, i) in form.phones || []" :key="`ph-${i}`">
                  <q-item-section avatar>
                    <q-icon name="mdi-phone" color="accent"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ ph.number?.national }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <remove-proxy-btn dense flat icon="mdi-close" name="Phone Number"
                                  @remove="removeItem('phones', i)"></remove-proxy-btn>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div class="_form_label">Emails</div>
            <div class="q-pa-sm">
              <email-field placeholder="Enter Email" :icon="false" hide-bottom-space dense filled @blur-value="addItem('emails', $event)"></email-field>
              <q-list separator>
                <q-item v-for="(em, i) in form.emails || []" :key="`ph-${i}`">
                  <q-item-section avatar>
                    <q-icon name="mdi-email" color="primary"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ em }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <remove-proxy-btn dense flat icon="mdi-close" name="Email"
                                  @remove="removeItem('emails', i)"></remove-proxy-btn>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div class="_form_label">Locations</div>
            <div class="q-pa-sm">
              <tomtom-autocomplete dense filled @update:model-value="addItem('locations', $event)"
                                   model-value="" :use-tomtom-geocode="useTomtomGeocode"></tomtom-autocomplete>
              <q-list separator>
                <q-item v-for="(ad, i) in form.locations || []" :key="`ph-${i}`">
                  <q-item-section avatar>
                    <q-icon name="mdi-map-marker" color="secondary"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ ad.formatted }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <remove-proxy-btn dense flat icon="mdi-close" name="Location"
                                  @remove="removeItem('locations', i)"></remove-proxy-btn>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </template>

        </div>
      </div>
    </div>
    <div class="_form_grid _f_g_r">
      <div></div>
      <div class="q-pa-sm">
        <div class="q-py-lg">
          <q-btn v-if="!form?._id" push class="_a_btn" no-caps label="Save Changes" @click="save"></q-btn>
          <q-btn v-else-if="Object.keys(patchObj || {}).length" push class="_a_btn" no-caps label="Save Changes"
                 @click="maybeSave()"></q-btn>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import SubdomainField from 'components/hosts/forms/SubdomainField.vue';
  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import EmojiPicker from 'components/common/input/EmojiPicker.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import ImageSelectOrUpload from 'components/common/uploads/images/ImageSelectOrUpload.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {useHosts} from 'stores/hosts';
  import {HForm, HSave} from 'src/utils/hForm';
  import {getRegexQuery, HQuery} from 'src/utils/hQuery';
  import {useTomtomGeocode} from 'stores/tomtom-geocode';


  const orgStore = useOrgs();
  const hostStore = useHosts();

  const props = defineProps({
    modelValue: { required: false },
    orgId: { required: false },
    stack: Boolean
  })

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => props.modelValue)
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => host.value?.org || props.orgId)
  })

  const formFn = (defs) => {
    return {
      description: '',
      ...defs
    }
  }
  const { form, save } = HForm({
    value: host,
    store: hostStore,
    formFn,
    beforeFn: (val) => {
      if (!val.dba) val.dba = org.value.name;
      if(!val.org) val.org = org.value._id
      return val;
    }
  })

  const { autoSave, maybeSave, patchObj } = HSave({
    form,
    save,
    store: hostStore,
    pause: ref(true)
  })

  const pickEmoji = (val) => {
    if (val) {
      form.value.description += val;
      autoSave('description');
    }
  }

  const addItem = (path, val) => {
    if (path === 'phones') {
      if ((form.value.phones || []).some(a => a.number?.e164 === val.number?.e164)) return;
    }
    form.value[path] ? form.value[path].push(val) : form.value[path] = [val];
    if (form.value._id) patchObj.value.$addToSet = {
      ...patchObj.value.$addToSet,
      [path]: (patchObj.value.$addToSet || {})[path] ? { $each: [...patchObj.value.$addToSet[path], val] } : { $each: [val] }
    }
  }
  const removeItem = (path, idx) => {
    if (idx > -1) {
      form.value[path].splice(idx, 1);
      autoSave(path);
    }
  }


  const maybeDba = ref('');
  watch(() => props.modelValue, (nv) => {
    if (nv?.dba) maybeDba.value = nv.dba;
  }, { immediate: true })

  const { search } = HQuery({})
  const loading = ref(false);
  const to = ref();
  const hasSearched = ref(false);
  const searchRes = ref({});
  const lastSearch = ref('');
  const setMaybeDba = async (val) => {
    maybeDba.value = val;
    if (to.value) clearTimeout(to.value);
    to.value = setTimeout(async () => {
      if (lastSearch.value !== val) {
        hasSearched.value = true;
        search.value.text = val;
        loading.value = true;
        searchRes.value = await hostStore.find({ query: { dba:getRegexQuery(maybeDba.value, false), $limit: 1 } })
        loading.value = false;
      }
    }, 1000)
  }


  const addRole = (val) => {
    form.value.roles = Array.from(new Set([...form.value.roles || [], val]))
    autoSave('roles')
  }
  const removeRole = (i) => {
    form.value.roles.splice(i, 1);
    autoSave('roles');
  }

  const addDba = () => {
    if (maybeDba.value && !searchRes.value.total) {
      form.value.dba = maybeDba.value;
      autoSave('dba', maybeDba.value)
    }
  }
  const subdomainTo = ref();
  const setSubdomain = () => {
    if (!form.value._id) {
      clearTimeout(subdomainTo.value);
      subdomainTo.value = setTimeout(() => {
        save()
      }, 1500)
    } else autoSave('subdomain');
  }

  watch(org, (nv) => {
    if (nv?.name && !form.value?.subdomain) form.value.subdomain = nv.name.split(' ').join('_').toLowerCase();
    if (nv?._id && !form.value?.org) form.value.org = nv._id;
  }, { immediate: true });

</script>

<style lang="scss" scoped>

</style>
