<template>
  <div>

  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useTeams} from 'stores/teams';

  const teamStore = useTeams();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue)
  })

</script>

<style lang="scss" scoped>

</style>
