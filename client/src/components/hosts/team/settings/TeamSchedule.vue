<template>
  <div class="_fw pw2 q-py-md">
    <div class="_fw mw800">
      <div class="q-pa-sm">
        <schedule-picker
            v-model="form.schedule"
            @update:model-value="autoSave('schedule')"
        ></schedule-picker>

        <div class="row justify-end q-pa-md" v-if="Object.keys(patchObj || {}).length">
          <q-btn class="_pl_btn tw-six" no-caps label="Save Changes" @click="save"></q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import SchedulePicker from 'components/common/calendar/forms/SchedulePicker.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useTeams} from 'stores/teams';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useCalendars} from 'stores/calendars';

  const teamStore = useTeams();
  const calendarStore = useCalendars();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue)
  })

  const { item:cal } = idGet({
    store: calendarStore,
    value: computed(() => team.value?.calendar)
  })
  const { form } = HForm({
    store: calendarStore,
    value: cal,
    beforeFn: (val) => {
      return {
        name: 'Team Calendar',
        owner: team.value._id,
        ownerService: 'teams',
        ...val
      }
    }
  })
  const { autoSave, patchObj } = HSave({
    form,
    store: calendarStore,
    save: () => console.log('save fn'),
    pause: ref(true),
    delay: 100
  })

  const save = () => {
    if (Object.keys(patchObj.value || {}).length) {
      calendarStore.patch(form.value._id, patchObj.value)
    }
  }

</script>

<style lang="scss" scoped>

</style>
