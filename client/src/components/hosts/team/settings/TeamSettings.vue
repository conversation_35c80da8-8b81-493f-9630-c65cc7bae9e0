<template>
  <div class="_fw">
    <div class="_fw pw2 q-py-md">
      <div class="_fw mw800">
        <div class="q-pa-sm">
          <team-form :model-value="team"></team-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import TeamForm from 'components/hosts/team/forms/TeamForm.vue';

  import {idGet} from 'src/utils/id-get';
  import {useTeams} from 'stores/teams';
  import {computed} from 'vue';

  const teamStore = useTeams();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue)
  })

</script>

<style lang="scss" scoped>

</style>
