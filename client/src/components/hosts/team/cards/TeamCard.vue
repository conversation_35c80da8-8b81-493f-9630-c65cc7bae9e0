<template>
  <div class="_fw">
    <div class="font-1r tw-six text-ir-deep">{{team?.name}}</div>
    <div class="font-7-8r tw-five text-ir-text">{{$possiblyPlural('Member', team?.refs)}}</div>
  </div>
</template>

<script setup>

  import {useTeams} from 'stores/teams';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {$possiblyPlural} from 'src/utils/global-methods';

  const teamStore = useTeams();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue),
    def: {}
  })
</script>

<style lang="scss" scoped>

</style>
