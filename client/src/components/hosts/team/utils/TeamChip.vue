<template>
  <q-chip v-bind="{clickable: true, color: 'ir-bg2', ...$attrs}">
    <slot name="default">
      <span v-if="team._id" class="tw-six">{{team.name}}</span>
      <span v-else>{{picker ? 'Select Team' : emptyLabel}}</span>
      <q-icon v-if="picker" class="q-ml-sm" name="mdi-menu-down"></q-icon>
    </slot>
    <slot name="right"></slot>
    <q-popup-proxy v-if="picker">
      <div class="bg-ir-bg1 text-ir-text w400 mw100 q-pa-sm">
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>

          <q-list separator>
            <q-item v-for="(t, i) in t$.data" :key="`t-${i}`" clickable @click="handleSelect(t)">
              <q-item-section>
                <q-item-label>{{t.name}}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>

  import {useTeams} from 'stores/teams';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';

  const teamStore = useTeams()

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    emptyLabel: { default: 'No Team' },
    modelValue: { required: true },
    emitValue: Boolean,
    picker: Boolean,
    hostId: { required: true }
  })

  const { item:team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue)
  })

  const handleSelect = (v) => {
    if(props.emitValue) emit('update:model-value', v._id)
    else emit('update:model-value', v)
  }

  const { search, searchQ } = HQuery({})
  const limit = ref(10)
  const { h$:t$ } = HFind({
    store: teamStore,
    limit,
    pause: computed(() => !props.picker || !props.hostId),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          host: props.hostId
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
