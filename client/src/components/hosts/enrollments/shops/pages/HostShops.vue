<template>
  <div class="_fw">
    <div class="q-pa-sm tw-six font-1r text-ir-deep">
      <div>Individual Enrollments</div>
      <div class="font-7-8r tw-four">These enrollments are not associated with a group plan</div>
    </div>

    <shops-table
        v-bind="{ params, host }"
        @select="$router.push({ name: 'host-shop', params: { shopId: $event._id }})"
    ></shops-table>

    <common-dialog
        setting="right"
        :model-value="!!$route.params.shopId"
        @update:model-value="(val) => val ? '' : $router.push({ name: 'host-shops' })">
      <div class="_fw q-pa-md">
        <shop-service v-if="$route.params.shopId" :shopId="$route.params.shopId"></shop-service>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import ShopsTable from 'components/market/shop/lists/ShopsTable.vue';
  import ShopService from 'components/hosts/enrollments/shops/cards/ShopService.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {computed} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {fakeId} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';


  const hostStore = useHosts();

  const hostId = computed(() => LocalStorage.getItem('host_id'))
  const { item:host } = idGet({
    store: hostStore,
    value: hostId
  })
  const params = computed(() => {
    return {
      query: {
        $sort: { updatedAt: -1 },
        enrollment: { $exists: false },
        host: hostId.value || fakeId
      }
    }
  })
</script>

<style lang="scss" scoped>


</style>
