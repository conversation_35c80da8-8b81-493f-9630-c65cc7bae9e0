<template>
  <div class="_fw">
    <in-app-ui
        :notabot="isAuthenticated"
        v-model:selected="selected"
        @vs="loadMore"
        :chats="i$.data"
        :pid="person._id"
    >
      <template v-slot:before-list>
        <div class="__top_search q-pa-sm">
          <q-btn v-if="$q.screen.width >= 800" dense flat size="sm" color="accent" icon="mdi-filter"></q-btn>
          <default-chip v-if="selectedPlan" avatar-path="_fastjoin.org.avatar" name-path="_fastjoin.org.name"
                        :model-value="selectedPlan" :use-atc-store="useAtcStore">
            <template v-slot:right>
              <q-btn class="q-ml-sm" dense flat size="sm" color="red" icon="mdi-close"
                     @click="selectedPlan = undefined"></q-btn>
            </template>
          </default-chip>
          <default-chip v-if="selectedTeam" :model-value="selectedTeam" :use-atc-store="useAtcStore">
            <template v-slot:right>
              <q-btn class="q-ml-sm" dense flat size="sm" color="red" icon="mdi-close"
                     @click="selectedTeam = undefined"></q-btn>
            </template>
          </default-chip>
          <q-chip v-if="selectedFp" color="ir-bg2">
            <span>{{ pplList.filter(a => a.participant?.fp === selectedFp)[0]?.participant?.name }}</span>
            <q-btn class="q-ml-sm" dense flat size="sm" color="red" icon="mdi-close"
                   @click="selectedFp = undefined"></q-btn>
          </q-chip>
          <q-chip v-if="selectedLogin" color="ir-bg2">
            <span>{{ pplList.filter(a => a.participant?.login === selectedLogin)[0]?.participant?.name }}</span>
            <q-btn class="q-ml-sm" dense flat size="sm" color="red" icon="mdi-close"
                   @click="selectedLogin = undefined"></q-btn>
          </q-chip>

        </div>
        <q-item v-if="i$.isFindPending">
          <q-item-section avatar>
            <ai-logo opaque></ai-logo>
          </q-item-section>
          <q-item-section>
            <q-item-label>Searching...</q-item-label>
          </q-item-section>
        </q-item>
      </template>
      <template v-slot:subjects>
        <div class="__subject" v-for="(subj, i) in subjects || []" :key="`subj-${i}`">
          <div class="__subj_title">
            <div>{{ subj.label || '' }}</div>
            <q-btn v-if="subj.search" dense flat size="xs" icon="mdi-magnify" @click="searching = subj.key"></q-btn>
          </div>

          <div class="_fw">
            <q-slide-transition>
              <div v-if="subj.search && searching === subj.key" class="_fw">
                <input v-on="subj.on"
                       class="__inp"
                       :value="subj.search.text">
              </div>
            </q-slide-transition>
          </div>

          <q-virtual-scroll
              class="__vs"
              :items="subj.items || []"
              v-slot="{ item, index }"
              @virtual-scroll="subj.scroll"
          >
            <div :key="`subj-${i}-${index}`" class="__subj_item"
                 @click="subj.click(item)">
              <div>
                <default-avatar v-if="subj.avatar" v-bind="{ ...subj.avatar(item), useAtcStore: useAtcStore }"></default-avatar>
              </div>
              <div class="__item_name">
                <div>{{ _get(item, subj.namePath || 'name') }}</div>
                <div v-if="subj.subPath">{{ _get(item, subj.subPath) }}</div>
              </div>
            </div>
          </q-virtual-scroll>
          <q-skeleton v-if="subj.loading" height="50px" width="100%"></q-skeleton>
        </div>
      </template>
    </in-app-ui>
  </div>
</template>

<script setup>
  import InAppUi from 'components/ims/in-app/InAppUi.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {HFind} from 'src/utils/hFind';
  import {useIms} from 'stores/ims';
  import {computed, ref, watch} from 'vue';
  import {useAtcStore} from 'stores/atc-store';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {fakeId} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  import {usePlans} from 'stores/plans';
  import {useTeams} from 'stores/teams';
  import {useHosts} from 'stores/hosts';
  import {HQuery} from 'src/utils/hQuery';
  import {_get} from 'symbol-syntax-utils';

  const { isAuthenticated, person } = loginPerson();

  const imsStore = useIms();
  const planStore = usePlans();
  const teamStore = useTeams();
  const hostStore = useHosts();

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  })

  const selected = ref();
  const selectedPlan = ref();
  const selectedTeam = ref();
  const selectedLogin = ref();
  const selectedFp = ref();

  const pplSearch = ref({ text: '' })

  const imsParams = ref({})
  const imsComputedParams = computed(() => {
    const query = {
      host: host.value._id || fakeId,
      $sort: { updatedAt: -1 }
    }
    if (selectedLogin.value) query['participant.login'] = selectedLogin.value
    if (selectedPlan.value) query.plan = selectedPlan.value._id
    if (selectedFp.value) query['participant.fp'] = selectedFp.value
    if (selectedTeam.value) query.team = selectedTeam.value._id
    return {
      runJoin: { ims_person: true },
      query
    }
  })
  const paramsTo = ref()
  const setParams = () => {
    clearTimeout(paramsTo.value);
    paramsTo.value = setTimeout(() => {
      imsParams.value = { ...imsComputedParams.value }
    }, 1000)
  }
  watch(imsComputedParams, (nv, ov) => {
    if (nv) {
      if (nv && !ov) return setParams()
      if (nv.query.host !== ov.query.host) return setParams()
      if (nv.query.plan !== ov.query.plan) return setParams()
      const checkOne = (path) => {
        // if (nv[path]?.$in) {
        //   if (!ov[path]?.$in) return setParams()
        //   if (nv[path].$in.some(a => !ov[path].$in.includes(a))) return true
        //   if (ov[path].$in.some(a => !nv[path].$in.includes(a))) return true
        // } else
        if (nv.query[path] !== ov.query[path]) return true
        return false
      }
      ['participant.login', 'participant.fp', 'team'].forEach(a => {
        if (checkOne(a)) return setParams()
      })
      if (nv['participant.name'] && nv['participant.name'] !== ov['participant.name']) return setParams()
    }
  }, { immediate: true, deep: true })

  const imLimit = ref(50);
  const { h$: i$, pageRecordCount: imPrCount, pagination: imPagination } = HFind({
    store: imsStore,
    pause: computed(() => !imsParams.value.host),
    limit: imLimit,
    // paginateApi: 'server',
    params: imsParams
  })

  const loadMore = () => {
    if (imPagination.value.currentPage < imPagination.value.pageCount) i$.toPage(imPagination.value.currentPage + 1)
  }

  const pplList = computed(() => {
    const byId = new Map();
    for (const im of i$.data) {
      const id = im.participant.login || im.participant.fp
      if (byId.has(id)) byId.get(id).push(im)
      else byId.set(id, [im])
    }
    const res = [];
    for (const [id, list] of byId.entries()) {
      res.push({ ...list[0], _fastjoin: { history: list.slice(1) } })
    }
    return res.filter(a => {
      const { email, name } = a.participant || {}
      return `${name || ''} ${email || ''}`.toLowerCase().includes(pplSearch.value.text.toLowerCase())
    });
  })

  const planLimit = ref(3);
  const { search: planSearch, searchQ: planQ } = HQuery({})
  const { h$: p$, pagination: planPagination } = HFind({
    store: planStore,
    limit: planLimit,
    pause: computed(() => !host.value._id),
    params: computed(() => {
      return {
        runJoin: { plan_org: true },
        query: {
          ...planQ.value,
          host: host.value._id,
          // [`team.${host.value._id}.id`]: host.value._id || fakeId
        }
      }
    })
  })

  const teamLimit = ref(3);
  const { search: teamSearch, searchQ: teamQ } = HQuery({})
  const { h$: t$, pagination: teamPagination } = HFind({
    store: teamStore,
    limit: teamLimit,
    params: computed(() => {
      return {
        query: {
          host: host.value._id || fakeId,
          ...teamQ.value
        }
      }
    })
  })


  const searching = ref('')
  const subjects = computed(() => {
    return [
      {
        key: 'plans',
        label: 'Plans',
        items: p$.data,
        namePath: 'name',
        search: planSearch.value,
        loading: p$.isFindPending,
        on: {
          input: (val) => planSearch.value.text = val.target.value
        },
        scroll: () => {
          if (planPagination.value.currentPage < planPagination.value.pageCount) p$.toPage(planPagination.value.currentPage + 1)
        },
        avatar: (v) => {
          return {
            modelValue: _get(v, '_fastjoin.org') || v,
            sizeIn: '30px',
            bgIn: 'primary'
          }
        },
        click: (item) => {
          selectedPlan.value = item;
        }
      },
      {
        key: 'teams',
        label: 'Teams',
        items: t$.data,
        namePath: 'name',
        loading: t$.isFindPending,
        search: teamSearch.value,
        scroll: () => {
          if (teamPagination.value.currentPage < teamPagination.value.pageCount) t$.toPage(teamPagination.value.currentPage + 1)
        },
        on: {
          input: (val) => teamSearch.value.text = val.target.value
        },
        avatar: (v) => {
          return {
            modelValue: v,
            sizeIn: '30px',
            bgIn: 'accent'
          }
        },
        click: (item) => {
          selectedTeam.value = item;
        }
      },
      {
        key: 'people',
        label: 'People',
        items: pplList.value,
        search: pplSearch.value,
        loading: i$.isFindPending,
        namePath: 'participant.name',
        subPath: 'participant.email',
        scroll: () => {
          console.log('haven\'t decided whether to $skip more people')
          // if(imPagination.value.page < imPagination.value.totalPages) imPagination.value.page++
        },
        click: (item) => {
          if (item.participant.login) selectedLogin.value = item.participant.login;
          else if (item.participant.fp) selectedFp.value = item.participant.fp;
        },
        on: {
          input: (val) => pplSearch.value.text = val.target.value
        }
      }
    ]
  })

</script>

<style lang="scss" scoped>
  .__subject {
    padding: 10px;
    width: 100%;

    .__subj_title {
      width: 100%;
      font-weight: 600;
      font-size: .7rem;
      color: var(--ir-mid);
      padding: 5px;
      display: grid;
      grid-template-columns: auto auto 1fr;
      align-items: center;
    }

    .__subj_item {
      width: 100%;
      padding: 5px;
      background: transparent;
      transition: all .2s;
      border-radius: 8px;
      display: grid;
      align-items: center;
      grid-template-columns: auto 1fr;
      cursor: pointer;

      > div {
        padding: 5px;
      }

      .__item_name {
        width: 100%;
        transition: all .2s;

        > div {
          &:first-child {
            width: 100%;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-weight: 500;
          }

          &:nth-child(2) {
            font-size: .8rem;
            color: var(--ir-mid);
          }
        }


      }

      &:hover {
        background: var(--ir-bg2);
      }
    }

    .__on {
      background: var(--ir-bg2);
    }
  }

  .__inp {
    width: 100%;
    appearance: none;
    border: none;
    outline: none;
    border-bottom: .2em solid var(--q-ir-light);
    background: var(--ir-bg2);
    border-radius: .2em .2em 0 0;
    padding: .4em;
    font-size: .8rem;
    margin-bottom: 3px;

    &:focus {
      border: none;
      outline: none;
    }
  }

  .__vs {
    max-height: min(300px, 50vh);
  }
</style>
