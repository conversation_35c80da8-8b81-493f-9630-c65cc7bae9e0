<template>
  <div class="_fw">
    <contract-builder
        v-model:search="search.text"
        subject-service="plans"
        :subject-options="p$.data"
        :aka="host?.dba"
    ></contract-builder>
  </div>
</template>

<script setup>

  import ContractBuilder from 'components/contracts/forms/ContractBuilder.vue';

  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {useHosts} from 'stores/hosts';
  import {usePlans} from 'stores/plans';

  const hostStore = useHosts();
  const planStore = usePlans();

  const hostId = computed(() => LocalStorage.getItem('host_id'))
  const { item: host } = idGet({
    store: hostStore,
    value: hostId
  })
  const query = computed(() => {
    return {
      $or: [
        { 'rfp.care_director.public': true },
        { 'rfp.plan_guide.public': true },
        { 'rfp.compliance.public': true },
        { 'rfp.finance.public': true },
        { 'rfp.care_director.hosts': { $in: [hostId]} },
        { 'rfp.plan_guide.hosts': { $in: [hostId]} },
        { 'rfp.compliance.hosts': { $in: [hostId]} },
        { 'rfp.finance.hosts': { $in: [hostId]} }
      ]
    }
  })
  const { search, searchQ } = HQuery({ query })
  const { h$: p$ } = HFind({
    store: planStore,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
