<template>
  <div class="_fw q-py-md pw2">

    <div class="_f_l _f_chip">Videos</div>
    <div class="_fw" v-for="(cat, i) in hostVideoCategories" :key="`cat-${i}`">
      <div class="q-pa-sm tw-six font-1r">{{ cat.label }}</div>
      <video-section :host="host" :model-value="(host.videos || {})[cat.key]" :cat="cat"></video-section>
    </div>

    <div class="row">
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="_f_l _f_chip">Enrollment Shop Statuses</div>
        <div class="q-pa-xs font-7-8r">Create statuses for tracking users shopping for plans via plan enrollment or public enrollment</div>

        <div class="q-py-sm _fw row items-center">
          <q-chip color="ir-bg2" clickable @click="addingStatus = true">
            <template v-if="addingStatus">
              <input placeholder="Name Status" @input="statusAdd.label = $event.target.value" :style="{ width: `${$max(8, statusAdd.label.length)}ch`}">
              <q-avatar class="q-ml-sm" size="20px" :color="statusAdd.color">
                <q-popup-proxy v-model="statusColorPicker">
                  <div class="w500 mw100">
                    <color-name-picker v-model="statusAdd.color"></color-name-picker>
                  </div>
                </q-popup-proxy>
              </q-avatar>
            </template>
            <template v-else>
              <span class="q-mr-sm">Add Status</span>
              <q-icon name="mdi-plus" color="primary"></q-icon>
            </template>
          </q-chip>
          <q-btn dense flat size="sm" color="primary" icon="mdi-plus" v-if="addingStatus" @click="addStatus"></q-btn>
          <shop-status-chip v-for="(k, i) in Object.keys(host.shopStatuses || {})" :key="`k-${i}`" :host="host" :model-value="k">
            <template v-slot:side>
              <remove-proxy-btn size="xs" class="q-ml-sm" dense flat icon="mdi-close" color="red" remove-label="Remove Status? <br>Any enrollments sorted by this status will no longer be searchable by status" @remove="removeStatus(k)"></remove-proxy-btn>
            </template>
          </shop-status-chip>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import VideoSection from 'components/hosts/videos/forms/VideoSection.vue';
  import ShopStatusChip from 'components/hosts/enrollments/shops/utils/ShopStatusChip.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import ColorNamePicker from 'components/common/colors/ColorNamePicker.vue';

  import {useHosts} from 'stores/hosts';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';

  import {hostVideoCategories} from '../videos/utils/dict';
  import {$errNotify, $max} from 'src/utils/global-methods';

  const hostStore = useHosts();

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  })

  const addingStatus = ref(false);
  const statusColorPicker = ref(false);
  const newStatus = () => {
    return {
      label: '',
      color: 'ir-blue'
    }
  }
  const statusAdd = ref(newStatus());
  const addStatus = async () => {
    if(!statusAdd.value.label || !statusAdd.value.color) return $errNotify('Must have name and color')
    const key = statusAdd.value.label.split(' ').join('_').toLowerCase()
    const obj = { ...host.value.shopStatuses, [key]: statusAdd.value};
    hostStore.patchInStore(host.value._id, { shopStatuses: obj })
    const updated = await hostStore.patch(host.value._id, { $set: { [`shopStatuses.${key}`]: statusAdd.value }})
    if(updated){
      statusAdd.value = newStatus()
      addingStatus.value = false;
    }
  }
  const removeStatus = (k) => {
    const obj = {...host.value.shopStatuses}
    delete obj[k];
    hostStore.patchInStore(host.value._id, { shopStatuses: obj })
    hostStore.patch(host.value._id, { $unset: { [`shopStatuses.${k}`]: '' }})
  }

</script>

<style lang="scss" scoped>
  input {
    border: none;
    width: 8ch;
    box-sizing: content-box;
    padding: 0px;
    background-color: transparent;

    &:focus {
      border: none;
      outline: none;
    }
  }
</style>
