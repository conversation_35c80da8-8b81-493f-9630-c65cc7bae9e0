<template>
  <div :class="`__ci text-${dark ? 'white' : 'black'}`" v-if="org?._id && !pickOrg">
    <default-avatar avatar-path="useAvatar" :dark="dark" :model-value="{...host, orgName: org.name, useAvatar: host?.avatar || org.avatar }" name-path="dba" backup-name-path="orgName" size-in="30px" :use-atc-store="useAtcStore"></default-avatar>
    <div class="__t q-px-sm tw-six">
      {{ $limitStr(host?.dba || org.name, 18, '...') }}
    </div>

    <q-icon size="15px" name="mdi-menu-down"></q-icon>

    <q-menu>
      <div class="_fw mw500 q-pa-md">
        <q-list separator>
          <q-item-label header>Host accounts for {{org.name}} <q-btn class="_i_i" size="sm" flat dense icon="mdi-close" @click="pickOrg = true"></q-btn></q-item-label>
          <default-item
              v-for="(opt, i) in h$.data"
              :key="`opt-${i}`"
              name-path="dba"
              backup-name-path="backupName"
              :model-value="{...opt, backupName: 'No DBA' }"
              @update:model-value="setHost(opt)"
          ></default-item>
        </q-list>
      </div>
    </q-menu>
  </div>
  <org-context-item v-else></org-context-item>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useAtcStore} from 'stores/atc-store';
  import {useHosts} from 'stores/hosts';
  import {useOrgs} from 'stores/orgs';
  import {HFind} from 'src/utils/hFind';
  import {useRouter} from 'vue-router';
  import {$limitStr} from 'src/utils/global-methods';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  const hostStore = useHosts();
  const orgStore = useOrgs();
  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const router = useRouter();

  const props = defineProps({
    size: { default: '35px' },
    dark: { type: Boolean, default: false },
  })

  const pickOrg = ref(false);

  const setHost = (val) => {
    envStore.setHostId(val._id);
    router.go();
  }

  const { item:org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const { item:host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  })

  const { h$ } = HFind({
    store: hostStore,
    pause: computed(() => !org.value),
    params: computed(() => {
      return {
        query: { org: org.value?._id }
      }
    })
  })
</script>

<style lang="scss" scoped>
  .__ci {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    cursor:pointer;

    .__t {
      text-decoration: none;
      transition: all .2s;
    }

    &:hover {
      .__t {
        text-decoration: underline;
      }
    }
  }
</style>
