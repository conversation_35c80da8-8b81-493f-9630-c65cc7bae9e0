<template>
  <div>
    <div class="_frm">
      <div class="_lbl">
        Company
      </div>
      <div class="_bod">
        <name-logo-only :id="gps?.org"></name-logo-only>
      </div>
    </div>

    <div class="_frm">
      <div class="_lbl">Anonymous<br><span>Openly share - or do it anonymously</span></div>
      <div class="_bod">
        <q-checkbox label="Hide my identity" :model-value="!!shop.anon"
                    @update:model-value="setShop('anon', $event)"></q-checkbox>
        <div v-if="!person._id">
          <q-tab-panels class="_panel" animated :model-value="!person._id && register">
            <q-tab-panel class="_panel" :name="true">
              <div class="row q-py-sm">
                <q-btn dense flat size="sm" color="accent" name="mdi-chevron-left" @click="register=false"></q-btn>
                <auth-card></auth-card>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" :name="false">
              <div class="_fw row justify-end q-py-xs">
                <q-btn size="sm" flat class="tw-five" @click="register = true" label="Login/Register" color="accent"
                       no-caps></q-btn>
              </div>
              <div class="_fw q-py-xs">
                <div class="row items-center q-py-xs">
                  <div class="col-6">
                    <q-input dense filled @blur="autoSavePerson('firstName')" v-model="personForm.firstName"
                             label="First Name"></q-input>

                  </div>
                  <div class="col-6">
                    <q-input dense filled @blur="autoSavePerson('lastName')" v-model="personForm.lastName"
                             label="Last Name"></q-input>
                  </div>
                </div>
                <div class="_fw q-py-xs">
                  <email-field dense filled v-model="personForm.email" @blur="autoSavePerson('email')"></email-field>
                </div>
                <div class="_fw q-py-xs">
                  <phone-input :input-attrs="{ dense: true, filled: true }" v-model="personForm.phone"
                               @blur="autoSavePerson('phone')"></phone-input>
                </div>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </div>
      </div>
    </div>

    <div class="_frm">
      <div class="_lbl">Your Link<br><span>Use this to let others see their own results for this plan</span></div>
      <div class="_bod">
        <link-card :url="getUrl()"></link-card>
      </div>
    </div>

    <div class="q-pt-md _fw">
      <div class="font-1r text-ir-mid tw-six alt-font">Share In Bulk</div>
      <div class="font-7-8r text-ir-mid alt-font">Pre-populate employee data and make it easier to compare</div>

      <q-tab-panels class="_panel" animated v-model="shareTab">
        <q-tab-panel class="_panel" name="base">
          <div class="__ee_table">
            <div class="_fw q-py-sm">
              <q-chip color="transparent" clickable @click="shareTab = 'table'">
                <span class="q-mr-sm">Add People</span>
                <q-icon name="mdi-plus" color="primary"></q-icon>
              </q-chip>
            </div>
            <table class="__ees">
              <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Link</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="ee in gps?.employees || []" :key="`ee-${ee._id}`">
                <td>{{ ee.firstName + ' ' + ee.lastName }}</td>
                <td>{{ ee.email }}</td>
                <td>
                  <input readonly @click="$copyTextToClipboard(getUrl({ shopId: ee.sim }))" class="__scinp"
                         :value="getUrl({ shopId: ee.sim })">
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </q-tab-panel>
        <q-tab-panel class="_panel" name="table">
          <div class="_fw q-py-sm">
            <q-chip color="transparent" clickable @click="shareTab = 'base'">
              <q-icon name="mdi-chevron-left" color="primary"></q-icon>
              <span class="q-ml-sm">Back</span>
            </q-chip>
          </div>
          <div class="__tbl">

            <q-btn v-if="sArray.length" flat size="sm" no-caps class="tw-six" @click="remove">
              <q-icon class="q-mr-sm" name="mdi-delete" color="red"></q-icon>

              <span>Remove {{ $possiblyPlural('Row', sArray) }}</span>
            </q-btn>
            <table :class="`__inp alt-font ${errsIn ? '__ers' : ''}`">
              <thead>
              <tr>
                <th style="text-align: right" class="cursor-pointer text-right">
                  <q-icon size="20px" :color="allOn ? 'primary' : 'ir-grey-6'"
                          :name="`mdi-checkbox-${allOn ? 'marked' : 'blank-outline'}`"
                          @click="selectAll(!allOn)"></q-icon>
                </th>
                <th v-for="(req, i) in reqs" :key="`req-${i}`">
                  {{ req.label }}
                  <q-tooltip>
                    <div class="text-center mw300 text-xxs tw-five">{{ req.tooltip }}</div>
                  </q-tooltip>
                </th>
              </tr>
              </thead>
              <tbody>
              <!--          <tr>-->
              <!--            <td class="bg-ir-grey-3">Ex:</td>-->
              <!--            <td v-for="(req, i) in reqs" :key="`ex-${i}`">-->
              <!--              {{ req.format(req.ex, 0, req.key) }}-->
              <!--            </td>-->
              <!--          </tr>-->
              <!--          first row-->
              <tr>
                <td class="cursor-pointer" @click.stop="selected[0] = !selected['0']">
                  <span v-if="!selected['0']">1</span>
                  <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
                </td>
                <td v-for="(req, idx) in reqs" :key="`ex-0-${idx}`" @click="setFocus([0, idx])" :id="`col-0-${idx}`"
                    :class="useErrs[`0-${idx}`] ? '__err' : ''">
                  <q-tooltip v-if="useErrs[`0-${idx}`]">
                    <div class="w300 tw-six text-xs">Err: {{ useErrs[`0-${idx}`] }}</div>
                  </q-tooltip>
                  <input
                      class="text-right"
                      :id="`inp-0-${idx}`"
                      @paste="handlePaste(0, idx, $event)"
                      @keydown="keyDown($event)"
                      :value="req.format((csvData[0] || [])[idx], 0, req.key)"
                      @input="setData(0, idx, $event.target.value)"
                      @blur="checkData(0, idx)"
                  >
                </td>

              </tr>
              <!--          ROWS 2 - end-->
              <tr v-for="(row, i) in [...csvData].slice(1)" :key="`row-${i + 1}`" :id="`row-${i + 1}`">
                <td class="cursor-pointer" @click.stop="selected[i+1] = !selected[i+1]">
                  <span v-if="!selected[i+1]">{{ i + 2 }}</span>
                  <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
                </td>
                <td
                    v-for="(req, idx) in reqs"
                    :key="`col-${i + 1}-${idx}`"
                    :id="`col-${i+1}-${idx}`"
                    :class="useErrs[`${i+1}-${idx}`] ? '__err' : ''"
                    @click="setFocus([i+1, idx])"
                >
                  <q-tooltip v-if="useErrs[`${i+1}-${idx}`]">
                    <div class="mw300 tw-six">Err: {{ useErrs[`${i + 1}-${idx}`] }}</div>
                  </q-tooltip>

                  <input
                      class="text-right"
                      :id="`inp-${i+1}-${idx}`"
                      @paste="handlePaste(i+1, idx, $event)"
                      @keydown="keyDown($event)"
                      :value="req.format((csvData[i+1] || [])[idx], i+1, req.key)"
                      @input="setData(i+1, idx, $event.target.value)"
                      @blur="checkData(i+1, idx)"
                  >
                </td>
              </tr>
              </tbody>
            </table>

          </div>
          <div v-if="csvData.length && saveBtn" class="row justify-end q-pt-lg q-px-md">
            <q-btn class="_pl_btn tw-six text-xs" no-caps push @click="save"
                   :disable="loading || !!Object.keys(useErrs || {}).length">
              <span class="q-mr-sm">{{ saveBtnLabel || 'Share Now' }}</span>
              <q-spinner color="white" v-if="loading"></q-spinner>
              <q-icon v-else name="mdi-chevron-right" color="white"></q-icon>
            </q-btn>
          </div>
        </q-tab-panel>
      </q-tab-panels>

    </div>
  </div>
</template>

<script setup>
  import NameLogoOnly from 'components/orgs/forms/NameLogoOnly.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import AuthCard from 'components/auth/AuthCard.vue';

  import {computed, nextTick, ref, watch} from 'vue';
  import LinkCard from 'components/common/links/LinkCard.vue';
  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {HForm, HSave} from 'src/utils/hForm';
  import {$copyTextToClipboard, $errNotify, $possiblyPlural, getRootDomain} from 'src/utils/global-methods';
  import {useRouter} from 'vue-router';
  import {getReqs, censusIndexByKey, fromCensus, toCensus} from '../utils';
  import {multiCellArray} from 'src/utils/csv';
  import {useShops} from 'stores/shops';
  import {useGps} from 'stores/gps';

  const router = useRouter();

  const pplStore = usePpls();
  const shopStore = useShops();
  const gpsStore = useGps();

  const props = defineProps({
    shop: { required: true },
    gps: { required: true },
    saveBtnLabel: String,
    saveBtn: Boolean,
    errsIn: Object
  })

  const { person } = loginPerson();

  const register = ref(false);

  const { item: prsn } = idGet({
    store: pplStore,
    value: computed(() => props.gps?.person)
  })

  const { form: personForm, save: savePerson } = HForm({
    store: pplStore,
    notify: false,
    value: prsn
  })

  const onChange = () => {
    if (personForm.value.firstName && personForm.value.lastName) {
      savePerson()
    }
  }

  const { autoSave: autoSavePerson } = HSave({
    form: personForm,
    store: pplStore,
    save: savePerson,
    onChange,
    pause: computed(() => !personForm.value._id)
  })

  const patchObj = ref({})
  const to = ref()
  const setShop = (path, val) => {
    patchObj.value[path] = val;
    shopStore.patchInStore(props.shop._id, patchObj.value)
    clearTimeout(to.value);
    to.value = setTimeout(async () => {
      const p = { ...patchObj.value };
      patchObj.value = {};
      await shopStore.patch(props.shop._id, p)
          .catch(err => {
            console.error(`Error updating shop: ${err.message}`)
            patchObj.value = p;
          })
    }, 2000)
  }

  const getUrl = (params) => {
    const { href } = router.resolve({
      name: 'compare-share',
      params: { gpsId: props.gps._id, ...params },
      query: { sentBy: props.shop._id }
    });
    return getRootDomain() + href;
  }

  const shareTab = ref('base')
  const employees = computed(() => props.gps?.employees || []);

  const { reqs, errs, useErrs, csvData, reqName } = getReqs()

  watch(employees, (nv, ov) => {
    if (nv && nv.length !== ov?.length) csvData.value = toCensus(nv, reqs.value);
  }, { immediate: true })

  const focused = ref([0, 0]);
  const hasFocused = ref(false);
  const lastTab = ref(false);
  const csvDialog = ref(false);
  const setFocus = (val, lt) => {
    focused.value = val;
    hasFocused.value = true;
    const el = document.getElementById(`inp-${val[0]}-${val[1]}`);
    if (el) el.focus();
    lastTab.value = lt;
  }

  const selected = ref({});
  const sArray = computed(() => {
    let res = [];
    for (const k in selected.value) {
      if (selected.value[k]) res.push(k)
    }
    return res;
  })

  const allOn = computed(() => sArray.value.length === csvData.value.length)

  const versions = ref([]);
  const loading = ref(false);

  const trySave = async (auto) => {
    if (!Object.keys(useErrs.value).length) {
      const employees = fromCensus(csvData.value, props.gps?.employees || [])
      await gpsStore.patch(props.gps._id, { employees })
      if (!auto) shareTab.value = 'base'
      return;

    } else $errNotify('Correct all data errors to continue')
  }
  const saveTo = ref();
  const maybeSave = () => {
    if (saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(() => {
      if (props.gps?._id) {
        trySave(true)
      }
    }, 5000);
  }
  const changeTo = ref()
  const changing = ref(false);
  const trackChange = (val) => {
    if (!changing.value) {
      versions.value.push(JSON.parse(JSON.stringify(val)));
      changing.value = true;
    }
    clearTimeout(changeTo.value);
    changeTo.value = setTimeout(() => {
      if (!changing.value) versions.value.push(JSON.parse(JSON.stringify(val)));
      maybeSave();
    }, 500);
  }

  const checkData = (row, col) => {
    if (!props.full && !reqs.value[col].required) {
      delete errs.value[row];
    } else if (!csvData.value[row] || !((csvData.value[row] || []).filter(a => !!a)).length) {
      delete errs.value[row];
      // if(csvData.value.length > 1) csvData.value.splice(row, 1);
    } else reqs.value[col].check(csvData.value[row][col], row, col);
  }

  const checkAll = () => {
    const data = csvData.value;
    for (let i = 0; i < data.length; i++) {
      for (let idx = 0; idx < Math.min(data[i].length, reqs.value.length); idx++) {
        checkData(i, idx)
      }
    }
  }

  const setData = (row, col, value) => {
    trackChange(csvData.value);

    // Ensure the row exists
    while (csvData.value.length <= row) {
      csvData.value.push([]); // Add an empty array for missing rows
    }

    for (let i = 0; i < col; i++) {
      const v = csvData.value[row][i];
      if (!v && v !== 0) csvData.value[row][i] = ''
    }

    // Set the value at the specified row and column
    csvData.value[row][col] = reqs.value[col]?.rev(value);
  }


  const handlePaste = (row, col, evt) => {
    evt.preventDefault()
    const d = multiCellArray(evt.clipboardData.getData('text').trim());
    // console.log('paste', row, col, d);

    /** function for iterating through a single row, filling blank cells, handling skipped cells due to hidden fields (non required - reqs[idx].required), and pasted values beginning in non-zero cols and extending partially or beyond the visible cols */
    const pasteRow = (rowIdx) => {
      /** set the first where the paste occurred */
      for (let idx = 0; idx < d[rowIdx].length; idx++) {

        // const skp = skip[idx + col] || 0
        const si = idx + col

        /** idx - col for the pasted value because that data only applies to the visible cells and - col is for non zero start paste */
        setData(row + rowIdx, si, d[rowIdx][idx])
        checkData(row + rowIdx, si)
      }
    }


    for (let i = 0; i < d.length; i++) {
      if (!Array.isArray(csvData.value[i + row])) csvData.value[i + row] = [];
      pasteRow(i + row);
    }
  }

  const remove = () => {
    const arr = Object.keys(selected.value);

    for (let i = arr.length; i > -1; i--) {
      if (selected.value[arr[i]]) {
        csvData.value.splice(Number(arr[i]), 1);
        selected.value[arr[i]] = false
      }
    }
    trackChange(csvData.value);
  }

  const selectAll = (val) => {
    if (val) {
      for (let i = 0; i < csvData.value.length; i++) {
        selected.value[i] = true;
      }
    } else selected.value = {};
  }

  const save = async () => {
    loading.value = true
    try {
      checkAll()
      await trySave()
    } catch (e) {
      console.error(`Error bulk sharing gps: ${e.message}`)
    } finally {
      loading.value = false;
    }
  }

  const undoIdx = ref(-1);

  const undo = () => {
    if (versions.value.length) {
      if (undoIdx.value < 0) undoIdx.value = versions.value.length - 1;
      csvData.value = [...versions.value[undoIdx.value]];
      undoIdx.value--
      maybeSave();
    }
  }

  const redo = () => {
    if (undoIdx.value < versions.value.length - 1) {
      undoIdx.value++;
      csvData.value = versions.value[undoIdx.value];
      maybeSave();
    }
  }

  const keyDown = (e) => {
    if (e.keyCode === 9) {
      e.preventDefault();
      let i = focused.value[0];
      let idx = focused.value[1];

      const lastVisible = Number(censusIndexByKey[reqs.value[reqs.value.length - 1].key]);
      if (idx >= lastVisible) {
        if (i >= csvData.value.length - 1) {
          csvData.value.push([]);
          // console.log('pushed', csvData.value)
        }
        i += 1;
        idx = Number(censusIndexByKey[reqs.value[0].key]);
      }
      else idx++
      nextTick(() => setFocus([i, idx], true));
    } else if (e.key === 'z') {
      if (e.metaKey || e.ctrlKey) {
        e.preventDefault();
        if (e.shiftKey) redo();
        else undo();
      }
    } else if (['Enter', 'ArrowDown'].includes(e.key)) {
      e.preventDefault();
      if (hasFocused.value) {
        const row = focused.value[0];
        if (row < csvData.value.length - 1) {
          setFocus([focused.value[0] + 1, focused.value[1]])
        } else {
          const l = csvData.value.length;
          csvData.value.push([]);
          for (let i = 0; i <= focused.value[1]; i++) {
            csvData.value[l][i] = '';
          }
          nextTick(() => setFocus([l, focused.value[1]]))
        }
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (focused.value[0] > 0) {
        setFocus([focused.value[0] - 1, focused.value[1]])
      }
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      const lastVisible = Number(censusIndexByKey[reqs.value[reqs.value.length - 1].key]);
      if (focused.value[1] <= lastVisible - 1) {
        let idx = focused.value[1] + 1;
        if (!props.full) for (let i = idx; i < reqs.value.length; i++) {
          if (reqs.value[i].required) {
            idx = i;
            break;
          }
        }
        setFocus([focused.value[0], idx])
      } else setFocus([focused.value[0], Number(censusIndexByKey[reqs.value[0].key])])
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault();
      const firstVisible = Number(censusIndexByKey[reqs.value[0].key]);
      if (focused.value[1] > firstVisible) {
        let idx = focused.value[1] - 1;
        if (!props.full) for (let i = idx; i >= 0; i--) {
          if (reqs.value[i].required) {
            idx = i;
            break;
          }
        }
        setFocus([focused.value[0], idx])
      } else setFocus([focused.value[0], Number(censusIndexByKey[reqs.value[reqs.value.length - 1].key])])
    }
  }

</script>

<style lang="scss" scoped>
  .__tbl {
    width: 100%;
    overflow-x: scroll;
  }

  input {
    border: none;
    width: 5em;
    box-sizing: content-box;
    padding: 0px;
    background-color: transparent;

    &:focus {
      border: none;
      outline: none;
    }
  }

  .__scinp {
    border: solid 1px var(--ir-mid);
    padding: 0px;
    width: 15ch;
  }

  .__ee_table {
    width: 100%;
    overflow-x: scroll;
    padding: 20px 8px;

    table {
      width: 100%;
      border-collapse: collapse;

      tr {
        th {
          color: var(--ir-mid);
        }

        td, th {
          border-bottom: solid 1px var(--ir-light);
          padding: 4px 8px;
          font-size: .9rem;
          text-align: left;
        }
      }
    }
  }

  .__inp {
    width: 100%;
    border-collapse: collapse;


    tr {
      th {
        padding: 2px 4px;
        text-align: right;
        //border: solid 1px #999;
        font-size: .7rem;
        color: var(--q-ir-grey-7);
      }

      td {
        font-size: .8rem;
        padding: 4px 8px;
        text-align: right;
        border: solid 1px var(--ir-light);
        background: white;

        &:first-child {
          background: var(--q-ir-grey-2) !important;
        }
      }

    }
  }

  .__ers {
    tr {
      td {
        background: var(--q-ir-red-1) !important;
      }
    }
  }

  .__err {
    background: var(--q-ir-red-1) !important;
    color: var(-q-ir-red-10) !important;
  }

  .__errs {
    padding: 0;
    width: 100%;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: all .3s ease;
  }
</style>
