<template>
  <q-page>
    <div class="row justify-center">
      <div class="_cent pd10">

        <div class="_fw q-pb-md">
          <div class="_fw row items-center">
            <div class="col-8 q-pa-sm col-md-grow">
              <name-logo-only
                  placeholder="Your Company Name"
                  :input-class="gps.companyName ? 'font-1r tw-six' : 'font-1r tw-six text-accent'"
                  :id="gps.org"
                  @update:image-in="setImg"
                  @update:name-in="setName"
                  :image-in="gps.companyAvatar"
                  :name-in="gps.companyName"
                  @update:id="setOrgId"
              ></name-logo-only>
            </div>
            <div class="col-4 q-pa-sm col-md-shrink">
              <div class="_fw q-py-sm">
                <q-checkbox :model-value="!!form.ale" @update:modelValue="setAle" label="50+ full time employees"></q-checkbox>
              </div>
            </div>

          </div>
        </div>
        <q-stepper class="_panel" animated header-nav flat v-model="tab">
          <q-step class="_panel" active-icon="mdi-file" icon="mdi-file" name="plans" title="Plans"></q-step>
          <q-step class="_panel" active-icon="mdi-face-man" icon="mdi-face-man" name="employees"
                  title="Employees"></q-step>
          <q-step class="_panel" active-icon="mdi-checkbox-marked-circle" icon="mdi-checkbox-marked-circle"
                  name="results"
                  title="Results"></q-step>
        </q-stepper>

        <q-tab-panels animated v-model="tab">
          <q-tab-panel class="_panel" name="plans">
            <div class="font-1r tw-six text-ir-deep q-pa-md">Add coverages to compare against the market</div>
            <div class="_fw pw2">

              <gps-coverage-manager
                  v-model="form"
              ></gps-coverage-manager>
            </div>

          </q-tab-panel>
          <q-tab-panel class="_panel" name="employees">
            <div class="_fw pw2">
              <employee-table @update:modelValue="setTab('results')" :model-value="gps"></employee-table>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="results">
            <compare-results  :model-value="gps"></compare-results>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>

<!--    <q-btn label="Patch EEs" @click="patchEEs"></q-btn>-->

  </q-page>
</template>

<script setup>
  import EmployeeTable from 'components/compare/group/tables/EmployeeTable.vue';
  import GpsCoverageManager from 'components/compare/group/plans/GpsCoverageManager.vue';
  import CompareResults from 'components/compare/group/results/CompareResults.vue';
  import NameLogoOnly from 'components/orgs/forms/NameLogoOnly.vue';

  import {idGet} from 'src/utils/id-get';
  import {useGps} from 'stores/gps';
  import {useRoute, useRouter} from 'vue-router';
  import {computed, onMounted, ref} from 'vue';
  import {HForm, HSave} from 'src/utils/hForm';

  const gpsStore = useGps();
  const router = useRouter();
  const route = useRoute();

  const { item: gps } = idGet({
    store: gpsStore,
    routeParamsPath: 'gpsId'
  })

  const { form, save } = HForm({
    store: gpsStore,
    value: gps,
    afterFn: (val) => {
      router.push({ ...route, params: { ...route.params, gpsId: val._id } })
    }
  })

  const { autoSave } = HSave({
    form,
    store: gpsStore,
    save,
    pause: computed(() => !form.value?._id)
  })

  // const patchEEs = () => {
  //   gpsStore.patch(gps.value._id, {
  //     employees: gps.value.employees.map(a => {
  //       const { sim, ...rest } = a;
  //       return rest;
  //     })
  //   }, { runJoin: { skip_employees: true }})
  // }

  const tab = ref('plans');


  const setTab = (val) => {
    router.push({ ...route, query: { ...route.query, tab: val } });
  }

  const setImg = (val) => {
    console.log('set img', val);
    form.value.companyAvatar = val
    autoSave('companyAvatar', val)
  }
  const setName = (val) => {
    console.log('set name', val);
    if (gps.value.companyName !== val) {
      form.value.companyName = val
      autoSave('companyName', val)
    }
  }
  const setOrgId = (val) => {
    if (gps.value.org !== val) {
      form.value.org = val
      autoSave('org', val)
    }
  }

  const setAle = (val) => {
    form.value.ale = val;
    autoSave('ale', val)
  }

  onMounted(() => {
    if (route.query.tab) tab.value = route.query.tab;
  })

</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    padding: 20px 12px;
    border-radius: 12px;
    box-shadow: 0 2px 6px var(--ir-light);
    background: white;
  }

  .q-stepper__step-inner {
    padding: 0 !important;
  }
</style>
