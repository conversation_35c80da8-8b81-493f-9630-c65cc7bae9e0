<template>
  <div class="_fw q-my-md pw1">
    <div class="row">
      <div class="col-12 col-md-6 q-px-sm q-py-lg">
        <div class="__c">
          <care-account-form :model-value="ca"></care-account-form>
        </div>
      </div>
      <div class="col-12 col-md-6 q-px-sm q-py-lg">
        <div class="q-pa-md tw-six font-1r text-ir-deep">Permissions</div>

        <div class="_form_grid _f_g_r">
          <div class="_form_label">Managers</div>

          <div class="q-pa-sm">
            <div class="__cap">Managers manage all account details</div>
            <budget-members
                :store="caStore"
                path="managers"
                model-value=""
            ></budget-members>
          </div>
          <div class="_form_label">Approvers</div>
          <div class="q-pa-sm">
            <div class="__cap">Approvers approve account spend that requires approval</div>
            <budget-members
                :store="caStore"
                path="approvers"
                model-value=""
            ></budget-members>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-6 q-px-sm q-py-lg">
        <div class="q-pa-md tw-six font-1r text-ir-deep">Connected Accounts</div>

        <bank-account-list :org="ca.owner"></bank-account-list>

      </div>
      <div class="col-12 col-md-6 q-px-sm q-py-lg">
        <div class="q-pa-md tw-six font-1r text-ir-deep">Account Features</div>
        <feature-form :moov-acct="moov_account"></feature-form>

      </div>
    </div>
  </div>
</template>

<script setup>
  import BudgetMembers from 'components/accounts/issuing/components/budgets/forms/BudgetMembers.vue';
  import CareAccountForm from 'components/care-accounts/forms/CareAccountForm.vue';
  import BankAccountList from 'components/accounts/lists/BankAccountList.vue';
  import FeatureForm from 'components/accounts/treasury/capabilities/FeatureForm.vue';

  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useCareAccounts} from 'stores/care-accounts';
  import {LocalStorage} from 'symbol-auth-client';
  import {useBanking} from 'stores/banking';

  const caStore = useCareAccounts();
  const bankStore = useBanking();

  const caId = computed(() => LocalStorage.getItem('care_account_id'));
  const { item: ca } = idGet({
    store: caStore,
    value: caId,
    params: ref({
      runJoin: { with_wallet: true }
    })
  })

  const moov_account = ref({})

  watch(ca, async (nv) => {
    if (nv && nv.moov_id) {
      if (!nv._fastjoin?.account && nv.moov_id !== moov_account.value.accountID) {
        moov_account.value = await bankStore.get(nv.moov_id, {
          banking: {
            moov: {
              method: 'get_account',
              args: [nv.moov_id]
            }
          }
        })
            .catch(err => {
              console.error(`Error getting account: ${err.message}`)
              return moov_account.value;
            })
        if (moov_account.value.accountID) caStore.patchInStore(nv._id, {
          _fastjoin: {
            ...nv._fastjoin,
            account: moov_account.value
          }
        })
      } else if(nv._fastjoin?.account) moov_account.value = { ...nv._fastjoin?.account }
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__cap {
    font-style: italic;
    font-size: .8rem;
    color: #999;
  }
</style>
