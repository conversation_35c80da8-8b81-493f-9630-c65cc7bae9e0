<template>
  <div class="flex items-center _fw">
    <q-img fit="contain" class="q-ma-xs w30 h30" :src="icon"></q-img>
    <div class="q-pa-sm text-grey-7">
      <div>{{faName(care_account)}}</div>
      <div class="tw-six text-primary num-font">{{dollarString((fa?.balance?.cash?.usd || 0) / 100, '$', 2)}}</div>
    </div>
  </div>
</template>

<script setup>
  import icon from 'src/assets/common_cent_grey.svg'
  import {computed, ref} from 'vue';
  import {faName} from 'src/components/accounts/treasury/utils';
  import {dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {useCareAccounts} from 'stores/care-accounts';
  import {useAtcStore} from 'stores/atc-store';

  const caStore = useCareAccounts();
  const props = defineProps({
    modelValue: { required: true },
  })

  const { item:care_account } = idGet({
    store: caStore,
    value: computed(() => props.modelValue),
    params: ref({ runJoin: { with_wallet: true }}),
    refreshWhen: computed(() => !props.modelValue?._fastjoin?.fa),
    useAtcStore
  })

  const fa = computed(() => care_account.value?._fastjoin?.fa)
</script>

<style lang="scss" scoped>

</style>
