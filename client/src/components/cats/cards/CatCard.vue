<template>
  <div class="_fw relative-position">
    <div class="font-1r tw-six q-pa-sm q-pt-md">
      <q-icon class="t-r-a" size="22px" color="accent" name="mdi-folder"></q-icon>
      <div>{{cat.name}}</div>
      <div class="font-3-4r tw-five">{{cat.description || ''}}</div>
    </div>

    <div class="q-pa-sm tw-six">
      <span class="text-accent">{{cat.procedures?.length || 0}}</span> {{$pluralExtension('Billable Item', cat.procedures)}}
    </div>
  </div>
</template>

<script setup>
  import {useCats} from 'stores/cats';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {$pluralExtension} from 'src/utils/global-methods';

  const catStore = useCats()

  const props = defineProps({
    modelValue: { required: true },
  })

  const { item: cat } = idGet({
    store: catStore,
    value: computed(() => props.modelValue)
  })
</script>

<style lang="scss" scoped>

</style>
