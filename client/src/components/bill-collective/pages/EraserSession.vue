<template>
  <div class="_fw __bg">
    <div class="row justify-center pw2 pd10">
      <div class="_sent">

        <q-tab-panels animated class="_panel" v-model="tab">
          <q-tab-panel class="_panel" name="analysis">

            <div class="text-md tw-six text-p12 q-px-md">Let's make that bill <span
                class="text-xs text-primary text-uppercase">smaller</span>
            </div>

            <div class="__c">

              <div class="_form_grid pw2">
                <div class="_form_label">Medical Provider</div>
                <div class="q-pa-sm">
                  <div class="__prv">
                    <div>
                      <provider-chip v-if="provider._id" :model-value="provider">
                        <template v-slot:right>
                          <q-btn v-if="provider._id" color="red" class="q-ml-sm" dense flat icon="mdi-close" size="sm"
                                 @click="selectProvider(undefined)"></q-btn>
                        </template>
                      </provider-chip>
                      <q-input v-else dense filled v-model="providerSearch.text">
                        <template v-slot:prepend>
                          <q-icon name="mdi-magnify"></q-icon>
                        </template>
                      </q-input>
                    </div>
                    <div>
                      <provider-type-chip class="tw-four" picker v-model="providerType"></provider-type-chip>

                    </div>
                  </div>

                  <q-slide-transition>
                    <div class="_fw q-pt-xs" v-if="providerSearch.text?.length > 2 && p$.total">
                      <q-chip color="transparent" text-color="accent">
                        <span>{{ $possiblyPlural('Likely Match', p$.total, '', 'es') }}</span>
                      </q-chip>
                      <div class="_fw">
                        <div class="row items-center">
                          <provider-chip
                              clickable
                              @click="selectProvider(p)"
                              v-for="(p, i) in p$.data"
                              :key="`p-${i}`"
                              :model-value="p"
                          ></provider-chip>
                        </div>
                      </div>
                    </div>
                  </q-slide-transition>

                </div>


                <div class="_form_label">Insurance</div>
                <div class="q-pa-sm">
                  <q-chip v-if="carrierName" color="ir-grey-2">
                    <span>{{ carrierName }}</span>
                    <q-btn class="q-ml-sm" dense flat size="sm" color="red" icon="mdi-close"
                           @click="setCarrier()"></q-btn>
                  </q-chip>
                  <carrier-input
                      v-else
                      placeholder="Enter Carrier Name..."
                      dense
                      filled
                      @update:model-value="setCarrier"
                  ></carrier-input>
                </div>

                <div class="_form_label">Location</div>
                <div class="q-pa-sm">
                  <state-chip
                      picker
                      :model-value="region"
                      @update:model-value="envStore.setLocation({
                      ...location, region: $event, lngLat: getStateLngLat($event)})"/>

                  <city-picker
                      v-if="region"
                      :state="region"
                      :model-value="city"
                      @update:model-value="setCity"
                      @state="setStateData"
                  ></city-picker>

                  <q-chip v-if="msas?.length > 1" color="ir-grey-2" clickable>
                    <span>{{ msa || 'Hospital Area' }}</span>
                    <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
                    <q-menu>
                      <div class="w300 mw100 q-pa-sm bg-white">
                        <q-list separator>
                          <q-item v-for="(m, i) in msas" :key="`m-${i}`" clickable @click="setMsa(m)">
                            <q-item-section>
                              <q-item-label>{{ m }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </q-menu>
                  </q-chip>
                </div>
              </div>


            </div>


            <div class="__c">
              <div class="_fw">
                <div class="row">
                  <div class="col-12 col-md-6 pw2 _fh q-pb-lg">

                    <div class="_form_grid">
                      <div class="_form_label">Amount Analyzed</div>
                      <div class="q-pa-sm">
                        <q-chip color="ir-grey-2"><span class="font-1-1-8r tw-nine text-ir-grey-8 text-ir-grey-8">{{
                            dollarString(totalPrice / 100, '$', 0)
                          }}</span>
                        </q-chip>
                      </div>
                      <div class="_form_label">Projected Savings</div>
                      <div class="q-pa-sm">
                        <q-chip color="ir-grey-2"
                                class="text-primary tw-eight"><span
                            class="font-1-1-2r">{{ dollarString((totalPrice - projected) / 100, '$', 0) }}</span>
                        </q-chip>
                      </div>
                      <div></div>
                      <div class="q-pa-sm">

                        <div class="text-xxs">Based on our pricing data, this is what we think you should be able to
                          save.
                        </div>

                        <div class="q-pt-lg row">
                          <q-btn @click="tab = 'contact'" class="tw-six" no-caps color="primary" push glossy>
                            <span>Get Started</span>
                            <q-icon class="q-ml-sm" name="mdi-chevron-right"></q-icon>
                          </q-btn>
                        </div>
                      </div>
                    </div>

                  </div>
                  <div class="col-12 col-md-6">

                    <div class="pw1">
                      <q-chip square color="ir-grey-2" class="tw-six tex-a10">Bill Insights</q-chip>
                    </div>
                    <div class="_fw pw2">
                      <div class="_fw tw-six font-7-8r q-pt-sm text-a10">
                        {{ !selfPay ? 'Insurance pays' : 'Self-pay' }}
                      </div>
                      <div class="font-7-8r" v-if="!selfPay">This means the carrier negotiates your prices if you
                        want these bills to accumulate against your deductible/max. We can still help by detecting
                        billing overlap, ie fraud, waste, and abuse - which is often 15-25% of your total bill.
                      </div>
                      <div class="font-7-8r" v-else>As a self-pay patient you have maximum ability to negotiate your
                        pricing. There is a very high degree of success in achieving significant discounts for self-pay
                        bills.
                      </div>
                      <div v-for="(c, i) in Object.keys(billFeedback.categories)" :key="`cat-${i}`">
                        <div class="_fw tw-six font-7-8r q-pt-sm text-a10">
                          {{ dollarString((billFeedback.categories[c].pct || 0) * 100, '', 0) }}%
                          {{ billFeedback.categories[c].name }}
                        </div>
                        <div class="font-7-8r">{{ billFeedback.categories[c].bill_hint }}</div>
                      </div>
                    </div>
                  </div>
                </div>


              </div>
            </div>

            <div class="__c _fw mnh200">

              <q-chip square color="ir-grey-2" class="tw-six" label="Your Bill"></q-chip>
              <div :class="`__load flex flex-center ${loading ? '' : '__hide'}`">
                <ai-logo size="40px"></ai-logo>
              </div>
              <div class="_fw q-py-md">
                <div class="__g" v-if="$q.screen.gt.sm">
                  <div v-for="k in Object.keys(codeItems)" :key="`header-${k}`">{{ codeItems[k].label }}</div>
                </div>
                <div class="__item __g" v-for="(code, i) in priceByCode" :key="`code-${i}`">
                  <div v-for="k in Object.keys(codeItems)" :key="`cell-${k}`">
                    <q-tooltip v-if="codeItems[k].tooltip">
                      <span class="text-xxs tw-five">{{ codeItems[k].value(code).tooltip }}</span>
                    </q-tooltip>
                    <span v-if="$q.screen.lt.md">{{ codeItems[k].label }}:&nbsp;</span>
                    <span :class="`alt-font ${codeItems[k].class}`">{{ codeItems[k].value(code).format }}</span>
                  </div>
                  <div>
                    <remove-proxy-btn size="sm" icon="mdi-close" name="Line" @remove="removeLine(code)"></remove-proxy-btn>
                  </div>
                </div>
              </div>
            </div>


          </q-tab-panel>
          <q-tab-panel class="_panel" name="contact">

            <div class="row">
              <q-chip color="transparent" clickable @click="tab = 'analysis'">
                <q-icon color="accent" name="mdi-chevron-left" class="q-mr-sm"></q-icon>
                <span>Back to your bill</span>
              </q-chip>
            </div>
            <div class="__c">
              <eraser-contact-flow :savings="totalPrice - projected" :session="session?._id"
                                   :total="totalPrice"></eraser-contact-flow>
            </div>
          </q-tab-panel>
        </q-tab-panels>


      </div>
    </div>
  </div>
</template>

<script setup>
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import StateChip from 'components/common/geo/pickers/StateChip.vue';
  import CityPicker from 'components/common/geo/pickers/CityPicker.vue';
  import ProviderTypeChip from 'components/providers/cards/ProviderTypeChip.vue';
  import EraserContactFlow from 'components/bill-collective/cards/EraserContactFlow.vue';
  import CarrierInput from 'components/bill-collective/utils/CarrierInput.vue';

  import {computed, watch, ref} from 'vue';
  import {SessionStorage} from 'symbol-auth-client';
  import {useRoute} from 'vue-router';
  import {$limitStr, $possiblyPlural, dollarString, fakeId} from 'src/utils/global-methods';
  import {usePrices} from 'stores/prices';
  import {usePriceEstimates} from 'stores/price-estimates';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useProviders} from 'stores/providers';
  import {storeToRefs} from 'pinia';
  import {useEnvStore} from 'stores/env';
  import {getStateCode, getStateLngLat, getStateName} from 'components/common/geo/data/states';
  import {useJunkDrawers} from 'stores/junk-drawers';

  const priceStore = usePrices();
  const peStore = usePriceEstimates();
  const eraserStore = useBillErasers();
  const providerStore = useProviders();
  const envStore = useEnvStore();
  const junkStore = useJunkDrawers();

  const route = useRoute();

  const { item: session } = idGet({
    store: eraserStore,
    routeParamsPath: 'session'
  })

  const { item: provider } = idGet({
    store: providerStore,
    value: computed(() => session.value?.provider)
  })

  const savePctFactor = Math.random() * .15
  const saveNoInsurance = .575
  const saveInsurance = .25;
  const savePercent = ref(saveNoInsurance - savePctFactor)

  const analysis = ref({})
  const priceEstimates = ref({})
  const codes = computed(() => analysis.value?.codes || []);

  const carrierName = computed(() => analysis.value?.insurance || '')
  const selfPay = computed(() => !carrierName.value || carrierName.value.toLowerCase().includes('self-'))

  watch(selfPay, (nv, ov) => {
    if (nv !== ov) {
      if (!nv) savePercent.value = saveInsurance - savePctFactor;
      else savePercent.value = saveNoInsurance - savePctFactor;
    }
  }, { immediate: true });

  import {bill_session} from '../utils/session';
  import {idGet} from 'src/utils/id-get';
  import {useBillErasers} from 'stores/bill-erasers';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  const { lngLat, region, location } = storeToRefs(envStore)

  const tab = ref('analysis');
  const providerType = ref();
  const sesh = ref({})
  const zipData = ref({})
  const stateData = ref({});
  const loading = ref(false);
  const ptf = ref({})
  const msa = ref()
  const city = ref()
  const msas = ref([])
  const dbPrices = ref({ data: [] })
  const marketPrices = ref({ data: [] })

  const getNumber = (v) => {
    if (typeof v === 'number') return v;
    if (typeof v === 'string') return Number(v.replace(/[^\d.]/g, ''))
    return undefined
  }

  const hospitalPrices = computed(() => {
    const arr = ptf.value.data || [];
    if (!arr.length) return {};
    const obj = {};
    for (let i = 0; i < arr.length; i++) {
      obj[arr[i].code] = arr[i].hospitals
    }
    return obj;
  })

  const { setPriceByCode, priceByCode, billFeedback } = bill_session({
    codes,
    hospitalPrices,
    marketPrices,
    priceEstimates,
    ptf
  });

  const totalPrice = computed(() => {
    let total = 0;
    for (let i = 0; i < codes.value.length; i++) {
      total += codes.value[i].total || 0
    }
    return total;
  })

  const market_rate = computed(() => {
    let rt = 0;
    for (const k of priceByCode.value) {
      const { rate, total, qty } = k || {};
      rt += (rate * (qty || 1)) || total || 0;
    }
    return rt;
  })

  const projected = computed(() => Math.min(market_rate.value, totalPrice.value * (1 - savePercent.value)))

  const { search: providerSearch, searchQ } = HQuery({ keys: ['name', 'legalName'] })

  const { h$: p$ } = HFind({
    store: providerStore,
    pause: computed(() => !providerSearch.value.text?.length),
    params: computed(() => {
      const query = providerSearch.value.text ? {
        ...searchQ.value
      } : { _id: fakeId }
      if (providerType.value) query.primaryType = providerType.value;
      return {
        _search: {
          text: providerSearch.value.text,
          location: {
            lngLat: lngLat.value || getStateLngLat(region.value),
            km: 50,
            path: 'geo'
          },
        },
        query
      }
    })
  })


  const getPrices = async () => {
    const list = [];
    for (let i = 0; i < codes.value.length; i++) {
      if (codes.value[i].procedureCode || codes.value[i].description) list.push({
        code: codes.value[i].procedureCode,
        description: codes.value[i].description
      })
    }
    // console.log('finding pe store', list, codes.value);

    if (list.length) {
      loading.value = true;
      // console.log('finding pe store', list);
      const prcs = await peStore.find({
        query: {},
        runJoin: { price_check: { rerun: false, searchList: list } }
      })
          .catch(err => console.log(`Error getting price check: ${err.message}`))
      loading.value = false;
      // console.log('prcs', prcs);
      if (prcs) {
        priceEstimates.value = prcs;
        setPriceByCode();
        SessionStorage.setItem(`bill_eraser:${route.params.session}`, {
          ...sesh.value,
          analysis: analysis.value,
          prices: priceEstimates.value,
          savePercent: savePercent.value
        })
      }
    }
  }

  const loadMarketPrices = async (tries = 0) => {
    if (codes.value.length) {
      const c = [];
      const r = [];
      for (let i = 0; i < codes.value.length; i++) {
        const { rxcui, code, billing_code } = codes.value[i];
        if (rxcui) r.push(rxcui);
        if (code || billing_code) c.push(code || billing_code);
      }

      loading.value = true;
      marketPrices.value = await priceStore.find({
        query: { eraser: { $ne: session.value._id }, source: { $nin: ['vision', 'bill', 'upload'] } },
        runJoin: { code_pricing: { codes: c, rxcuis: r } }
      })
          .catch(err => {
            console.error(`Error loading market prices: ${err.message}`)
            return marketPrices.value;
          })
      loading.value = false;
      setPriceByCode()
    } else if (tries < 5) {
      setTimeout(() => {
        loadMarketPrices(tries + 1);
      }, 2000);
    }
  }

  const patchLast = ref({})
  const patchTo = ref()
  const updateSession = async (patch) => {
    patchLast.value = { ...patchLast.value, ...patch };
    for (const k in patch.$unset || {}) {
      if (patchLast.value[k]) delete patchLast.value[k];
    }
    if (patchTo.value) clearTimeout(patchTo.value);
    patchTo.value = setTimeout(async () => {
      eraserStore.patch(route.params.session, patchLast.value)
    }, 3000)
  }

  const setCarrier = (val) => {
    const { insurance } = analysis.value || {}
    if (val !== insurance && ((!!val && !!insurance) || (!val && !!insurance) || (!insurance && !!val))) {
      analysis.value.insurance = val;
      updateSession({ carrier: val })
    }
  }

  const lastMsa = ref();

  const searchHospitals = async () => {
    if (!msa.value) return;
    loading.value = true;
    const state = getStateName(region.value);
    lastMsa.value = msa.value;
    ptf.value = await peStore.find({
      query: {},
      runJoin: {
        'pt_search': {
          configs: codes.value.filter(a => !!a.procedureCode).map(a => {
            return {
              // code: '74177',
              code: a.procedureCode || a.billing_code,
              state,
              msa: msa.value
            }
          })
        }
      }
    })
        .catch(err => {
          console.error(`Error getting price files: ${err.message}`);
        })
    loading.value = false;
    setPriceByCode()
  }
  const setMsa = (val) => {
    msa.value = val;
    if (val) updateSession({ msa: val })
    if (providerType.value === 'hospital' && val !== lastMsa.value) searchHospitals();
  }

  const setCounty = (val) => {
    const countyMsas = (stateData.value.msas || {})[val] || [];
    let msaName
    if (countyMsas.length > 1) msas.value = countyMsas;
    else msaName = countyMsas[0]
    if (msaName && lastMsa.value !== msaName) {
      setMsa(val)
    }
  }
  const setZip = async (val) => {
    if (!val) return;
    if (zipData.value.zip !== val) {
      const drawers = await junkStore.find({ query: { $limit: 1, itemId: `zips|${val.slice(0, 3)}` } })
      if (drawers.total) {
        zipData.value = { ...drawers.data[0].data[val], zip: val };
      } else return;
    }
    city.value = zipData.value.city;
    setCounty(zipData.value.county);
    envStore.setLocation({
      ...location.value,
      lngLat: zipData.value.lngLat,
      zip: zipData.value.zip,
      city: zipData.value.city,
      region: zipData.value.state_name || val.state
    })
    updateSession({ zip: val, state: zipData.value.state_name || val.state })
    analysis.value = { ...analysis.value, ...getAnalysisObj() }
    SessionStorage.setItem(`bill_eraser:${session.value._id}`, {
      ...sesh.value,
      analysis: analysis.value,
      savePercent: savePercent.value
    })

  }
  const setCity = (val) => {
    city.value = val;
    setTimeout(() => {
      const data = (stateData.value?.cities || {})[val] || {};
      if (Array.isArray(data)) setZip(data[0]);
      else if (data.zip) {
        zipData.value = { ...data, city: val }
        setZip(data.zip);
      }
    }, 100)

  }


  const setStateData = (val) => {
    stateData.value = val;
    const code = getStateCode(val.name);
    if (code && code !== session.value.state) updateSession({ state: code });
    const county = zipData.value.county;
    if (county && zipData.value.state_name === val.name) {
      setCounty(county);
    }
  }

  const getAnalysisObj = () => {
    return {
      provider: session.value.providerName,
      state: session.value.state,
      zip_code: session.value.zip
    }
  }
  const setAnalysis = () => {
    const analysisObj = {
      ...getAnalysisObj()
    }
    analysisObj.codes = [];
    for (let i = 0; i < dbPrices.value.data.length; i++) {
      const price = dbPrices.value.data[i];
      const spl = (price.notes || '').split('|').filter(a => a.includes('qty:'))[0]
      const obj = { qty: spl?.split(':')[1] || 1, _id: price._id }
      if (price.code?.length === 5) {
        obj.procedureCode = price.code;
        obj.code = price.code;
        obj.billing_code = price.billing_code
      } else obj.billing_code = price.billing_code || price.code;
      obj.price = price.price;
      obj.total = (price.price || 0) * (obj.qty || 1)
      obj.description = price.description
      analysisObj.codes.push(obj);
    }
    if (analysisObj.provider && analysisObj.provider !== 'n/a') {
      providerSearch.value.text = analysisObj.provider;
    }
    analysis.value = analysisObj;
    SessionStorage.setItem(`bill_eraser:${route.params.session}`, {
      ...sesh.value,
      prices: priceEstimates.value,
      analysis: analysis.value,
      savePercent: savePercent.value
    })
    if (analysis.value.provider?.toLowerCase().includes('hospital')) providerType.value = 'hospital';
    if (analysis.value.state) envStore.setLocation({ ...location.value, region: getStateName(analysis.value.state) })

  }

  const selectProvider = async (p) => {
    if (p) {
      eraserStore.patchInStore(session.value._id, { provider: p._id, providerName: p.name })
      if (p.primaryType?.toLowerCase().includes('hospital') || p?.name?.toLowerCase().includes('hospital')) providerType.value = 'hospital';
      else providerType.value = p.primaryType;
      const zip = (p.locations || [])[0]?.postal
      if (zip) setZip(zip)
    } else {
      eraserStore.patchInStore(session.value._id, { provider: '', providerName: '' })
    }
    if (provider.value._id || dbPrices.value.data?.length) {
      updateSession(p ? { provider: p._id, providerName: p.name || p.legalName } : {
        $unset: {
          provider: '',
          providerName: ''
        }
      })
    }
    SessionStorage.setItem(`bill_eraser:${session.value._id}`, {
      ...sesh,
      analysis: analysis.value,
      provider: p?._id,
      savePercent: savePercent.value
    })
  }

  watch(providerType, (nv, ov) => {
    if (nv === 'hospital' && nv !== ov && !ptf.value.total) {
      searchHospitals()
    }
  }, { immediate: true });
  watch(session, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      console.log('watched session');
      loading.value = true;

      // console.log('save save?', nv.savePercent, savePercent.value)
      if (nv.savePercent) savePercent.value = nv.savePercent;
      else await eraserStore.patch(nv._id, { savePercent: savePercent.value })
          .catch(err => console.error(err.message))
      // console.log('nv.savePercent', nv.savePercent);
      analysis.value = nv.analysis;
      priceEstimates.value = nv.prices;
      if (nv.providerName?.toLowerCase().includes('hospital')) providerType.value = 'hospital';
      if (nv.state) {
        console.log('nv state', nv.state);
        envStore.setLocation({ ...location.value, state: getStateName(nv.state) })
      }
      dbPrices.value = await priceStore.find({ query: { $limit: 100, eraser: route.params.session } })
          .catch(err => {
            console.log(`Error searching session prices: ${err.message}`)
            return { data: [] }
          })
      loading.value = false
      if (dbPrices.value.total) {
        setAnalysis()
      }
      getPrices()
      setTimeout(() => {
        loadMarketPrices()
      }, 1000);
    }
  }, { immediate: true });

  const removeLine = (code) => {
    const priceMatch = priceStore.getFromStore(code._id).value
    if (priceMatch.total) {
      for (let i = 0; i < priceMatch.data.length; i++) {
        const p = priceMatch.data[i];
        priceStore.patchInStore(p._id, { eraser: undefined })
        if (p.provider) priceStore.patch(p._id, { $unset: { eraser: '' } })
        else priceStore.remove(p._id, { admin_pass: true })
      }
    }
  }


  const codeItems = {
    procedureCode: {
      label: 'Code/ID',
      class: '',
      value: (v) => {
        const code = v.procedureCode || v.billing_code;
        return {
          v: code,
          format: code
        }
      }
    },
    description: {
      label: 'Description',
      class: '',
      tooltip: true,
      value: (v) => {
        const desc = v.description || ''
        return {
          v: desc,
          format: $limitStr(desc, 40, ''),
          tooltip: desc
        }
      }
    },
    price: {
      label: 'Price',
      class: 'tw-six',
      value: (v) => {
        const price = (getNumber(v.price) || (getNumber(v.total) / getNumber(v.qty || 1))) / 100
        return {
          v: price,
          format: dollarString(price, '$', 0),
        }
      }
    },
    qty: {
      label: 'Qty',
      class: '',
      value: (v) => {
        return {
          v: v || 1,
          format: dollarString(v.qty || 1, '', 0)
        }
      }
    },
    total: {
      label: 'Total',
      class: 'tw-six text-a7',
      value: (v) => {
        const total = (getNumber(v.total) || (getNumber(v.price) * (getNumber(v.qty || 1)))) / 100
        return {
          v: total,
          format: dollarString(total, '$', 0),
        }
      }
    },
    benchmark: {
      label: 'Best Rate',
      class: 'text-p7 tw-six',
      tooltip: true,
      value: (v) => {
        const { rate, qty } = v;
        const final = rate ? (getNumber(rate) / 100) * getNumber(qty) : undefined
        const total = (getNumber(v.total) || (getNumber(v.price) * (getNumber(v.qty || 1)))) / 100
        const match = final === total;
        return {
          v: final,
          format: match ? 'Pending' : dollarString(final, '$', 0),
          tooltip: match ? 'This provider does not have a lower published price - negotiation is required (we do that)' : 'Lowest verified price for this procedure - this is our target for negotiation'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .__bg {
    background: linear-gradient(180deg, white, var(--q-p0), white);
  }

  .__load {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    z-index: 20;
    background: rgba(255, 255, 255, .5);
    backdrop-filter: blur(20px);
    overflow: hidden;
    transition: all .3s;
  }

  .__hide {
    opacity: 0;
    pointer-events: none;
  }

  .__g {
    width: 100%;
    display: grid;
    grid-template-columns: 10% 35% 10% 10% 10% 20% 5%;
    font-size: var(--text-xxs);
    align-items: end;

    > div {
      padding: 10px;
      border-bottom: solid .5px #999;
      text-align: right;
    }

    &:last-child {
      > div {
        border-bottom: none;
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .__g {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto auto auto auto auto;

      > div {
        text-align: left;
        border-bottom: none;
        padding: 3px 10px;

        &:first-child {
          padding-top: 20px;
        }

        &:last-child {
          border-bottom: solid .3px #666;
          padding-bottom: 20px;
        }
      }
    }
  }

  .__c {
    position: relative;
    padding: 30px min(2vw, 30px);
    border-radius: 10px;
    margin: 10px 0;
    box-shadow: 0 2px 6px #dedede;
    width: 100%;
    background: rgba(255, 255, 255, .8);
  }

  .__prv {
    width: 100%;
    display: grid;
    grid-template-columns: min(300px, 50%) 1fr;
  }

  @media screen and (max-width: 700px) {
    .__prv {
      grid-template-columns: 100%;
      grid-template-rows: auto auto;

      > div {
        padding: 3px 0;
      }
    }
  }
</style>
