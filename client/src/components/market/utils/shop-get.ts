import {idGet} from 'src/utils/id-get';
import {computed, nextTick, ref, Ref, watch} from 'vue';
import {SessionStorage} from 'symbol-auth-client';
import {useShops} from 'stores/shops';
import {useRoute} from 'vue-router';
import {_get, _set} from 'symbol-syntax-utils';
import {getStats} from 'components/market/shop/utils/index';
import {useAtcStore} from 'stores/atc-store';

const defShop = (v?:any) => {
    return {
        aptc: 0,
        coverage_scores: {},
        coverage_scores_ptc: {},
        spend: 0,
        simsCount: 0,
        coverages: [],
        ...v
    }
}

export const shopGet = (opts:any) => {
    const shopStore:any = useShops();
    const route = useRoute();

    const { item:shop } = idGet({
        store: shopStore as any,
        value: computed(() => {
            if(opts?.shop?.value) return opts.shop.value;
            else return route.params.shopId || SessionStorage.getItem('shop_id') || defShop()
        }),
        useAtcStore,
        ...opts?.params
    })

    const patchObj:Ref<any> = ref({});
    const shopTo = ref();
    const maybeSave = (path, val) => {
        if (shopTo.value) clearTimeout(shopTo.value);
        if (path.includes('$unset')) {
            shopStore.patch(shop.value._id, { $unset: { [path]: '' } })
        } else {
            // if(path !== 'household_size'){
            //     const hh_size = stats.value.people?.length
            //     if(hh_size && hh_size !== stats.value.household_size) patchObj.value.household_size = Math.max(stats.value.household_size || 1, stats.value.people?.length || 1);
            // }
            const spl = path.split('.');
            if (spl.length > 1) {
                const obj = _set(shop.value[spl[0]], spl.slice(1), val);
                shopStore.patchInStore(shop.value._id, { [spl[0]]: obj });
            } else {
                shopStore.patchInStore(shop.value._id, { [path]: val })
            }
            const adders:any = {};
            if(['people', 'gender', 'age', 'inactive'].includes(path.split('stats.')[0])) adders.peopleEdited = new Date()
            patchObj.value.$set = { ...patchObj.value.$set, [path]: val, ...adders };
            shopTo.value = setTimeout(async () => {
                if(shop.value._id) {
                    // console.log('patching', patchObj.value);
                    const updated = await shopStore.patch(shop.value._id, patchObj.value)
                        .catch(err => console.error(`Could not update shop session: ${err.message}`))
                    if (updated) patchObj.value = {}
                }
            }, 2000)
        }

    }

    const stats = ref(getStats({}))

    watch(shop, (nv) => {
        if (nv._id) {
            stats.value = getStats(nv.stats)
        }
        if(opts?.onWatch) opts.onWatch(nv)

    }, { immediate: true, deep: true })

    const setStat = (path:string, val:any, cb?:(...args:any) => void) => {
        // console.log('set stat', path, val);
        if(_get(stats.value, [path]) !== val) {
            const spl = path.split('.');
            let obj = val;
            if (spl.length > 1) obj = _set(stats.value[spl[0]] || {}, spl.slice(1), val);
            const changePath = JSON.stringify(obj) === JSON.stringify(stats.value[spl[0]]) ? undefined : spl[0]
            // console.log('set state path', path, obj);
            stats.value[path] = obj;
            stats.value.household_size = stats.value.people?.length || 1
            // stats.value = _set(stats.value, path, val);
            shopStore.patchInStore(shop.value._id, {stats: {...shop.value.stats, [path]: obj}})
            maybeSave(`stats.${path}`, val);
            if(['people', 'gender', 'age', 'inactive'].includes(path)) {
                stats.value.peopleEdited = new Date()
                maybeSave('stats.peopleEdited', new Date())
            }
            // SessionStorage.setItem('pricing_stats', stats.value);
            if (cb && typeof cb === 'function') nextTick(() => cb(changePath, true));
        }
    }

    const hh = computed(() => {
        const children = [];
        const self = { gender: stats.value.gender || 'male', age: stats.value.age, smoker: stats.value.smoker, relation: 'self' };
        let spouse;
        const ppl = stats.value.people || []
        for(let i = 0; i < ppl.length; i++){
            if(ppl[i].child || ppl[i].relation === 'child' || ppl[i].age < 18 || (Math.abs(ppl[i].age - stats.value.age) > 16)){
                children.push(ppl[i])
            } else spouse = ppl[i]
        }
        return { self, spouse, children }
    })

    return {
        hh,
        shop: computed(() => shop.value || {}),
        route,
        shopStore,
        maybeSave,
        patchObj,
        stats,
        setStat
    }
}
