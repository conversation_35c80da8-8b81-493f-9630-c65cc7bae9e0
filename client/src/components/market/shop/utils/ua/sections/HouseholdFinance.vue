<template>
  <div class="font-7-8r">
    <div class="_form_grid">
      <div class="_form_label">Filing Status</div>
      <div class="q-pa-sm">
        I plan to file an income tax return as the following household status
        <div class="_fw">
          <status-chip picker :model-value="hh.filingAs" @update:model-value="setHh('filingAs', $event)"></status-chip>
        </div>
      </div>
      <div class="_form_label">Calculate MAGI</div>
      <div class="q-pa-sm relative-position">
        <div class="q-pb-sm text-italic">List all income for your household</div>
        <div class="__cover" v-if="magi"></div>
        <household-income @update:deductions="deductions = $event" :person="person"></household-income>

        <div class="q-py-md tw-six">Estimated MAGI: <span class="text-accent font-1-1-4r alt-font">{{dollarString(dollas.magi, '$', 0)}}</span></div>
      </div>
      <div class="_form_label">Enter MAGI (override)</div>
      <div class="q-pa-sm flex items-center mw500">
        <money-input prefix="$" v-model="magi" @blur="addMagi" filled dense></money-input>
        <q-btn dense flat v-if="magi" color="green" icon="mdi-check-circle"></q-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
  import HouseholdIncome from 'components/households/cards/HouseholdIncome.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import StatusChip from 'components/households/forms/StatusChip.vue';

  import {taxBreakdown} from 'components/households/utils/tax-tables';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useHouseholds} from 'stores/households';
  import {dollarString} from 'symbol-syntax-utils';

  const hhStore = useHouseholds();

  const props = defineProps({
    person: { required: true }
  })

  const { item:hh } = idGet({
    store: hhStore,
    value: computed(() => props.person?.household)
  })

  const magi = ref(0)
  const deductions = ref({})

  const addMagi = () => {
    if(magi.value && magi.value !== hh.value.magi){
      hhStore.patch(hh.value._id, { magi: magi.value })
    } else if(!magi.value && hh.value.magi){
      hhStore.patch(hh.value._id, { $unset: { magi: '' }})
    }
  }

  watch(hh, (nv) => {
    if(nv){
      if(nv.magi) magi.value = nv.magi;
      if(nv.deductions) deductions.value = nv.deductions;
    }
  }, { immediate: true })

  const dollas = computed(() => {
    return taxBreakdown(hh.value, { deductions: deductions.value })
  })

  const saveTo = ref();
  const patchObj = ref({})
  const maybeSave = () => {
    if(saveTo.value) clearTimeout(saveTo.value)
    if(Object.keys(patchObj.value).length){
      saveTo.value = setTimeout(async () => {
        await hhStore.patch(hh.value._id, patchObj.value)
        patchObj.value = {}
      }, 3000)
    }
  }
  const setHh = (path, val) => {
    hhStore.patchInStore(hh.value._id, { [path]: val })
    maybeSave()
  }
</script>

<style lang="scss" scoped>
  .__cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 4;
    background: rgba(255,255,255,.5);
    backdrop-filter: blur(5px);
  }
</style>
