<template>
  <q-page id="AiShop" class="__aishop">
    <div v-if="shop.plan" class="_fw bg-ir-bg1">
      <div class="row justify-center q-py-lg q-px-md">
        <div class="_cent">
          <div class="mw600 flex items-center">
            <div v-if="org.avatar" class="q-pa-sm">
              <default-avatar :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
            </div>
            <div class="q-pa-sm">
              <div class="text-sm tw-six">{{ plan.name }}</div>
              <div class="text-xs">by {{ org.name || '' }}</div>

            </div>
          </div>
        </div>
      </div>
    </div>
    <template v-if="enrollment.status === 'complete'">
      <div class="__block flex flex-center">
        <div>Your enrollment is complete</div>
      </div>
    </template>
    <template v-else>
      <div class="row justify-center">
        <div class="_cent relative-position">
          <!--        <div class="__switch">-->
          <!--          <q-toggle size="lg" keep-color :model-value="dark" @update:model-value="envStore.setDark(!dark, $q.dark.set)"-->
          <!--                    color="accent" :icon="!dark ? 'mdi-weather-sunny' : 'mdi-moon-waning-crescent'"></q-toggle>-->
          <!--        </div>-->
        </div>
      </div>

      <div class="__top" :style="`background: radial-gradient(${dark ? 'var(--ir-bg1)' : 'var(--q-a1)'}, var(--ir-bg) 70%);`">
        <div class="row justify-center _fh items-center">
          <div class="__strip" v-if="!dark"></div>

          <div class="_sent pw2 pd8 q-mt-lg">

            <div class="row justify-center q-pb-lg">
              <div>

                <div class="text-center q-pt-md text-sm tw-five q-pb-sm">Stop choosing the wrong insurance plan
                </div>

                <div class="text-center text-xxl q-py-md __title _l1-3">
                  See the <span class="__clip">best health plan</span> for you - by the <span class="__clip">math</span>
                </div>
                <div class="row justify-center q-py-xl">
                  <div class="__inp" @click="scrollDown">
                    <ai-logo opaque :dark="dark"></ai-logo>
                    <q-tab-panels
                        :transition-duration="1000"
                        animated
                        v-model="inputIdx"
                        class="_panel"
                        transition-next="slide-up"
                        transition-prev="slide-up">
                      <q-tab-panel v-for="(inp, i) in inputVals" :key="`inp-${i}`" class="_panel" :name="i">
                        {{ inp }}
                      </q-tab-panel>
                    </q-tab-panels>
                    <div class="__btn">
                      <q-icon size="13px" name="mdi-arrow-right"></q-icon>
                    </div>
                  </div>
                </div>


                <!--              <q-separator dark class="q-my-sm"></q-separator>-->

              </div>

            </div>

            <div class="row">
            <div class="col-12 col-md-4 q-pa-sm" v-for="(step, i) in steps" :key="`step-${i}`" @pointerenter="hover = i" @pointerleave="hover = -1">
                <div class="__c">
                  <div class="row justify-center">
                  <div class="flex items-center">
                    <q-icon class="font-2r __clip" :name="step.icon"></q-icon>
                    <div class="font-1r tw-six alt-font q-ml-sm">{{ step.label }}
                    </div>
                  </div>
                  </div>

                  <div :class="`text-center font-7-8r tw-five q-pt-xs text-ir-deep __hider ${hover === i ? '' : '__off'}`">{{ step.text }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <ai-shop :plan="plan" :enrollment="enrollment"></ai-shop>

    </template>

  </q-page>
</template>

<script setup>
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import AiShop from 'components/market/shop/AiShop.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {useRoute} from 'vue-router';
  import {SessionStorage} from 'symbol-auth-client';
  import {darkness} from 'src/utils/env/darkness';
  import {useAtcStore} from 'stores/atc-store';
  import {useEnrollments} from 'stores/enrollments';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useShops} from 'stores/shops';
  import {useOrgs} from 'stores/orgs';
  import {useEnvStore} from 'stores/env';

  const shopStore = useShops();
  const planStore = usePlans();
  const enrollmentStore = useEnrollments();
  const orgStore = useOrgs();
  const envStore = useEnvStore();

  const route = useRoute();
  const { dark } = darkness()

  const { item: shop } = idGet({
    store: shopStore,
    value: computed(() => route.params.shopId)
  })

  const hover = ref(-1)

  const inputVals = ['How much should I pay in premiums?', 'What should my deductible be?', 'Should I use a premium tax credit?', 'Should I use a health share?', 'Which health shares are best?', 'Do I want/need a network?']
  const inputIdx = ref(0);

  const { item: enrollment } = idGet({
    store: enrollmentStore,
    value: computed(() => shop.value.enrollment || route.query.enrollmentId || SessionStorage.getItem('enrollment_id'))
  })

  watch(() => enrollment.value._id, (nv) => {
    if (nv) {
      if (shop.value.enrollment && !enrollment.value.shop) {
        enrollmentStore.patch(enrollment.value._id, { shop: shop.value._id })
      }
    }
  }, { immediate: true });

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => shop.value.plan || enrollment.value?.plan || envStore.getPlanId)
  })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => plan.value.org)
  })

  const steps = [
    {
      icon: 'mdi-home-outline',
      label: 'Add your household',
      text: 'Add location, age(s), and health risk profile so we use data that matches your real life'
    },
    {
      icon: 'mdi-abacus',
      label: 'View realistic outcomes',
      text: 'We run thousands of simulations against each plan to compare and display outcomes'
    },
    {
      icon: 'mdi-magnify-scan',
      label: 'Nail down details',
      text: 'Once you know what plans will work best for you - we can double check the fine details'
    }
  ]

  const animate = () => {
    setTimeout(() => {
      if (inputIdx.value < inputVals.length - 1) inputIdx.value = inputIdx.value + 1
      else inputIdx.value = 0;
      animate()
    }, 5000)
  }


  onMounted(() => {
    if (route.query.enrollmentId) SessionStorage.setItem('enrollment_id', route.query.enrollmentId);
    if (route.query.planId) envStore.setPlanId('plan_id', route.query.planId);
    animate();
  })

  const scrollDown = () => {
    let dist = window.scrollY + window.innerHeight * .5
    const el = document.getElementById('TheStatPicker');
    if (el) dist = el.getBoundingClientRect().top + window.scrollY - window.innerHeight * .35
    window.scrollTo({
      top: dist,
      behavior: 'smooth'
    })
  }


</script>

<style lang="scss" scoped>
  .__clip {
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .__aishop {
    background: var(--ir-bg);
    color: var(--ir-text);
    position: relative;

    .__switch {
      position: absolute;
      top: 2vw;
      right: 2vw;
      z-index: 10;
    }

    .__top {
      //background: linear-gradient(90deg, black, #101010, black);
      color: var(--ir-text);
      position: relative;

      //.__strip {
      //  position: absolute;
      //  top: 88%;
      //  left: 20%;
      //  width: 60%;
      //  height: 20px;
      //  min-height: 10px;
      //  border-radius: 15px;
      //  overflow: hidden;
      //  background: var(--ir-bg-grad);
      //}

      > div {
        min-height: 50vh;
      }

      .__title {
        font-weight: 600;
        color: var(--ir-off);
        padding: 0 10px;
      }
    }

    //AI SHOP PAGE
    .__inp {
      text-align: left;
      width: 500px;
      max-width: 94vw;
      border-radius: 45px;
      padding: 10px 14px;
      background: var(--ir-light);
      color: var(--ir-off);
      font-weight: 400;
      font-size: var(--text-xs);
      display: grid;
      grid-template-columns: auto 1fr auto;
      align-items: center;

      > div:nth-child(2) {
        padding: 0 10px !important;
      }

      .__btn {
        height: 20px;
        width: 20px;
        border-radius: 50%;
        display: grid;
        align-items: center;
        justify-content: center;
        background: var(--ir-mid);
        color: var(--ir-light);
      }
    }

    .__sim_head {
      padding: 20px;
      border-radius: 12px;
      color: var(--ir-off);
      margin: 10px 0;
      width: 800px;
      max-width: 100%;
    }

    .__c {
      border-radius: 10px;
      //background: var(--ir-bg2);
      padding: 15px;
      height: 100%;
    }
  }

  .__block {
    width: 100%;
    height: 80vh;
    font-size: var(--text-lg);
    font-weight: 500;
  }

  .__hider {
    max-height: 200px;
    overflow: hidden;
    transition: all .3s;
    opacity: 1;
  }

  .__off {
    max-height: 0;
    opacity: 0;
  }
</style>
