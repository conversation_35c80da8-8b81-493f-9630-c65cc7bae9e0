<template>
  <div class="__aich" :id="id">
    <div class="row justify-center q-pb-lg">
      <div class="_sent pd5">

        <div class="q-py-lg text-lg tw-six text-center">Step 1: Tell us about yourself</div>
        <div class="__stats">
          <div class="row items-start">
            <div class="col-12">

              <div class="__second _fw q-py-md __s">
                <div class="__dsbl flex flex-center" v-if="loading">
                  <ai-logo opaque size="30px"></ai-logo>
                </div>
                <div class="row justify-center">
                  <div class="__st relative-position _oh">
                    <div class="__ovr"></div>

                    <div class="text-sm tw-six _fw relative-position z2">

                      <div class="row items-center">
                        <div class="col-12">
                          <zip-picker class="text-xs" @zip-data="setZipData" :model-value="place.zipcode">
                            <template v-slot:left>
                              <q-icon name="mdi-map-marker" color="red" class="q-mr-sm"></q-icon>
                            </template>
                          </zip-picker>
                        </div>
                      </div>


                      <stat-picker :enrollment="enrollment" @stats="stats.value = $event;" :model-value="shop"
                                   @reload="resetMarket"></stat-picker>

                      <div class="q-py-md _fw">
                        <risk-profile
                            :age="stats.age"
                            :model-value="stats.risk"
                            :people="hhToCms"
                            @update:model-value="setStat('risk', $event, resetMarket)"
                        ></risk-profile>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>


      </div>
    </div>

    <div class="row justify-center pd5 bg-ir-bg1">

      <div class="_cent __explore">


        <template v-if="shop._id">
          <div class="q-py-lg text-center text-lg tw-six">Step 2: Compare your options</div>

          <div class="_fw">
            <div class="row justify-center">
              <div class="_sent pw2">

                <div class="text-sm text-center">We ran <span id="SimsCount" class="__clip alt-font tw-six">{{
                    dollarString(simsRun, '', 0)
                  }}</span> simulations. Here's what we found:
                </div>
                <div class="q-py-sm"></div>
                <div class="_fw text-xxs tw-five __toc_lines">
                  <div>
                    <div>
                      <q-badge dense square color="ir-bg">
                      <span
                          class="tw-six alt-font text-ir-text text-xs">{{
                          Object.keys(shop.coverage_scores || {}).length + 143
                        }}</span>
                      </q-badge>
                    </div>
                    <div>
                      plans for your zip code
                    </div>

                  </div>

                  <div>
                    <div>
                      <q-badge dense square color="ir-bg">
                      <span
                          class="tw-six alt-font text-ir-text text-xs">{{
                          Object.keys(shop.coverage_scores_ptc || {}).length
                        }}</span>
                      </q-badge>
                    </div>
                    <div>
                      made the cut for deep analysis
                    </div>
                  </div>

                  <div>
                    <div>
                      <q-badge dense square color="ir-bg">
                      <span
                          :class="`tw-six alt-font text-${riskColors[stats.risk]} text-xs`">{{ dollarString(shop?.spend, '$', 0) }}</span>
                      </q-badge>
                    </div>
                    <div>
                      avg annual medical bills simulated
                    </div>
                  </div>

                </div>
                <div class="row justify-end">
                  <q-chip clickable color="transparent" class="tw-six cursor-pointer text-xxs"
                          @click="simDialog = true">Learn More
                  </q-chip>
                </div>

              </div>
            </div>
          </div>

          <div class="row justify-center">
            <div class="_sent pw2">


              <div class="__pck">
                <div class="row items-center">
                  <div class="_lbl" @pointerenter="hover = 'types'">
                    <div>Plan Types</div>
                    <div :class="`${hover !== 'types' ? '' : '__soff'}`">
                      <q-badge class="font-1-1-4r tw-six alt-font" color="ir-orange">
                        {{ activeList.length }}
                      </q-badge>
                    </div>
                  </div>
                  <q-space/>
                  <q-btn dense flat icon="mdi-close" color="red" v-if="hover === 'types'" @click="hover = ''"></q-btn>
                </div>
                <div :class="`_bod ${hover === 'types' ? '' : '__off'}`">
                  <type-manager
                      :model-value="activeTypes"
                      @toggle="toggleType"
                  ></type-manager>
                </div>
              </div>
              <div class="__pck" v-if="!plan?.ale || plan_coverage.ichra"
              >
                <div class="row items-center">
                  <div class="_lbl" @pointerenter="hover = 'ptc'">
                    <div>Premium Tax Credit</div>
                    <div :class="`${hover !== 'ptc' ? '' : '__soff'}`">
                      <q-badge :color="productTypes.aca.color" class="tw-six alt-font font-1-1-4r">
                        {{ dollarString((aptc || 0) * mult, '$', 0) }}
                      </q-badge>
                    </div>
                  </div>
                  <q-space/>
                  <q-btn dense flat icon="mdi-close" color="red" v-if="hover === 'ptc'" @click="hover = ''"></q-btn>
                </div>
                <div :class="`_bod ${hover === 'ptc' ? '' : '__off'}`">
                  <div class="row items-center">
                    <q-chip text-color="white" :color="productTypes.aca.color">
                      <span class="font-1-1-4r tw-six alt-font q-mr-sm">{{
                          dollarString((aptc || 0) * mult, '$', 0)
                        }}</span>
                      <q-icon class="q-ml-sm font-1-1-4r" name="mdi-information"></q-icon>

                      <q-popup-proxy :breakpoint="50000">
                        <div class="_fw mw600 q-pa-md bg-white">
                          <ptc-explainer
                              :model-value="shop.useAptc"
                              @update:model-value="toggleAptc"
                              :loading="loading"
                              :ptc="(aptc || 0) * mult"
                              :mult="mult"
                          ></ptc-explainer>
                        </div>
                      </q-popup-proxy>
                    </q-chip>
                  </div>
                  <div class="row items-center q-px-sm no-wrap q-pt-sm">
                    <div class="font-1r">
                      <span :class="`text-${productTypes['aca'].color} tw-six`">These&nbsp;</span> plans are
                      eligible for the premium tax credit
                    </div>
                  </div>
                  <div class="flex items-center">
                    <q-checkbox :disable="loading" :dark="dark" :model-value="shop.useAptc"
                                @update:model-value="toggleAptc"></q-checkbox>
                    <div @click="toggleAptc(!shop.useAptc)" class="tw-five font-1r text-ir-deep"> included in
                      outcomes <span
                          v-if="shop.useAptc" class="text-primary tw-six">(YES)</span><span v-else
                                                                                            class="text-secondary">(NO)</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="__pck" v-if="shop.plan && (subsidy || employer)"
              >
                <div class="row items-center">
                  <div class="_lbl" @pointerenter="hover = 'er'">
                    <div>Employer Contributions</div>
                    <div :class="`${hover !== 'er' ? '' : '__soff'}`">
                      <q-badge color="accent" class="tw-six alt-font font-1-1-4r">{{
                          dollarString((subsidy + employer) * mult, '$', 0)
                        }}
                      </q-badge>
                    </div>
                  </div>
                  <q-space/>
                  <q-btn dense flat icon="mdi-close" color="red" v-if="hover === 'er'" @click="hover = ''"></q-btn>
                </div>
                <div :class="`_bod font-1r ${hover === 'er' ? '' : '__off'}`">
                  <div v-if="subsidy">
                    <div><b>Premium Subsidy:&nbsp;</b> <span
                        class="text-accent alt-font tw-six font-1-1-2r">{{
                        dollarString((subsidy || 0) * mult, '$', 0)
                      }}</span>
                    </div>
                    <div class="font-1r q-pb-sm">Can only be used on these coverages</div>
                  </div>
                  <div><b>Flex Allowance:&nbsp;</b> <span
                      class="text-accent alt-font tw-six font-1-1-4r">{{
                      dollarString((employer || 0) * mult, '$', 0)
                    }}</span>
                  </div>
                  <div class="font-1r q-pb-sm">This allowance is fully yours - use it here, elsewhere, or cash it
                    in. Your employer is part of just 1% of employers who allow this.
                  </div>


                </div>
              </div>

              <div class="__pck">
                <div class="row items-center">
                  <div class="_lbl" @pointerenter="hover = 'bills'">
                    <div>Annual Medical Bills</div>
                    <div :class="`${hover !== 'bills' ? '' : '__soff'}`">
                      <q-badge :color="riskColors[stats.risk]" class="tw-six alt-font font-1-1-4r">
                        {{ dollarString(slidingSpend, '$', 0) }}
                      </q-badge>
                    </div>
                  </div>
                  <q-space/>
                  <q-btn dense flat icon="mdi-close" color="red" v-if="hover === 'bills'" @click="hover = ''"></q-btn>
                </div>
                <div :class="`_bod ${hover === 'bills' ? '' : '__off'}`">

                  <div class="_form_grid">
                    <div class="_form_label">Household Amount</div>
                    <div class="q-pa-sm">
                      <div class="q-px-md q-pb-xl">
                        <spend-slider
                            :risk="stats.risk"
                            :people="stats.people"
                            :age="stats.age"
                            :model-value="slidingSpend"
                            @update:model-value="slidingSpend = $event">
                        </spend-slider>
                      </div>
                    </div>

                    <div class="_form_label">Event Count</div>
                    <div class="q-px-sm">
                      <div class="flex items-center">
                        <q-btn :disable="event_count < 1" dense flat icon="mdi-menu-down"
                               @click="event_count--"></q-btn>
                        <q-chip color="ir-grey-2" square class="tw-six font-1r">{{ event_count }}</q-chip>
                        <q-btn class="q-mr-md" dense flat icon="mdi-menu-up" :disable="event_count > 10"
                               @click="event_count++"></q-btn>

                        <div class="font-7-8r text-ir-deep q-py-sm">Condition/event count affects how deductibles/copays
                          are
                          applied
                        </div>

                      </div>
                    </div>
                  </div>


                </div>
              </div>

            </div>
          </div>

        </template>

        <div class="_fw pd6 pw2 text-sm text-ir-mid tw-six" v-else>
          <div v-if="stats.place?.countyfips" class="text-center">
            <span>Once your household profile looks accurate</span>
            <div class="row justify-center q-pt-md">
              <q-btn @click="resetMarket('', false,true)" rounded push class="__but" :disable="loading" no-caps>
                <template v-if="!loading">
                  <span class="tw-five">Run the simulation</span>
                  <q-icon class="q-ml-sm" name="mdi-chevron-right"></q-icon>
                </template>
                <template v-else>
                  <span class="tw-five q-mr-sm">Running Simulation</span>
                  <q-spinner-dots color="white"></q-spinner-dots>
                </template>
              </q-btn>
            </div>
          </div>
          <div v-else class="text-center">
            Zip code is required to run a simulation
          </div>
        </div>

        <div class="_fw q-py-md">
          <div class="row" v-if="!byId.average?.length">
            <div class="col-12 col-md-4 q-pa-xs" v-for="i in 3" :key="`skd-${i}`">
              <q-skeleton class="_fw h200"></q-skeleton>
            </div>
          </div>
          <ideal-structure
              v-else
              v-model:selected="selected"
              @update:spend="slidingSpend = $event"
              :shop="shop"
              :spend-in="slidingSpend"
              :enrollment="enrollment"
              :def_key="def_key"
              :tax_rate="tax_rate"
              :active-types="activeTypes"
              :all="byId.all"
              :event_count="event_count"
          >
            <template v-slot:filter>
              <span></span>
            </template>
          </ideal-structure>
        </div>

        <div class="row justify-center">
          <div class="_sent q-py-md">
            <div class="_fw row justify-center __ai_chip" v-if="shop._id">
              <div :class="`AiChip ${chatOn ? '__on' : ''}`">
                <shop-ai-chip
                    v-model="chatOn"
                    dark
                    :prompts="['Which option is the most similar to my current plan?', 'How does my plan compare to the other options?', 'How does the premium tax credit affect things?', 'When would I want to choose a health share?']"
                    color="a10"
                    class="tw-five text-xs alt-font text-white"
                    :shop="shop"
                ></shop-ai-chip>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <div class="_fw pd10">

      <div class="text-center q-pb-lg">
        <div class="text-lg tw-six">More helpful charts to help you decide</div>
      </div>

      <result-category
          @update:spend="slidingSpend = $event"
          :shop="shop"
          :def_key="def_key"
          :tax_rate="tax_rate"
          :enrollment="enrollment"
          v-if="shop._id"
          @update:selected="selected = $event"
          :by-id="byId"
          :active-types="activeTypes"
      ></result-category>
    </div>

    <div v-if="!shop._id" class="_fw h300 row items-center justify-center">
      <ai-logo opaque :dark="dark"></ai-logo>
    </div>

    <div class="row justify-center __cb">
      <div class="_xsent pd12 pw2">
        <div class="text-center text-lg tw-six text-white q-pb-md">Need to talk to a human?</div>
        <contact-bar dark></contact-bar>
      </div>
    </div>


    <q-dialog maximized v-model="simDialog">
      <div class="_fa flex flex-center __d" @click="simDialog = !simDialog">
        <div class="w500 mw100 q-pa-lg" @click.stop="undefined">
          <div class="row justify-center q-pb-lg">
            <ai-logo size="80px" :dark="dark"></ai-logo>
          </div>
          <div class="text-xs tw-five">We use billing data from the healthcare spend of millions of
            Americans matching your household profile and run thousands of probability simulations for each coverage
            option to see what your total spend would be - premiums + out of pocket - and rank the results you see here.
          </div>
        </div>
      </div>
    </q-dialog>

    <popup-sleeve :model-value="!!selected" @update:model-value="togglePopup">
      <div class="__popup_wrap bg-ir-bg text-ir-text _fh">
        <coverage-viewer
            :shop="shop"
            :open="openCheckout"
            :age="stats.age"
            :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
            :by-id="byId"
            :model-value="selected"
            :enrollment="enrollment"
        ></coverage-viewer>
      </div>
    </popup-sleeve>

    <turnstile-popup v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile" v-model="notabot"
                     @update:model-value="resetMarket()"></turnstile-popup>

    <count-down @close="countdown = 0;" :countdown="countdown" @reset="resetMarket('', true)"></count-down>

  </div>
</template>

<script setup>
  import StatPicker from './cards/StatPicker.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import PopupSleeve from 'components/market/utils/PopupSleeve.vue';
  import CoverageViewer from 'components/market/shop/cards/CoverageViewer.vue';
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import ZipPicker from 'components/common/geo/pickers/ZipPicker.vue';
  import RiskProfile from 'components/market/shop/utils/RiskProfile.vue';
  import CountDown from 'components/market/shop/utils/CountDown.vue';
  import TypeManager from 'components/market/shop/utils/TypeManager.vue';
  import PtcExplainer from 'components/market/shop/cards/PtcExplainer.vue';
  import SpendSlider from 'components/market/shop/utils/SpendSlider.vue';
  import IdealStructure from 'components/market/shop/cards/IdealStructure.vue';

  import {dollarString} from 'symbol-syntax-utils';
  import {SessionStorage} from 'symbol-auth-client';
  import {computed, onMounted, ref, watch} from 'vue';
  import {shopGet} from 'components/market/utils/shop-get';
  import {darkness} from 'src/utils/env/darkness';
  import {useRouter} from 'vue-router';
  import {
    genCmsHousehold,
    placeManager,
    setLocation,
    shopHousehold
  } from 'components/market/household/utils';
  import {loginPerson} from 'stores/utils/login';
  import {erSubsidy} from 'components/market/shop/utils/subsidy';
  import {fakeId} from 'src/utils/global-methods';
  import {sessionFamily} from 'components/households/utils/session-family';
  import {useEnvStore} from 'stores/env';
  import {getStateCode} from 'components/common/geo/data/states';
  import {rankById, byIdObj, planTypeManager} from './utils/rank-by-id'
  import {shopResetId} from './utils/reset-id';
  import {countdownUtils} from 'components/market/shop/utils/countdown';
  import {idGet} from 'src/utils/id-get';
  import {useCoverages} from 'stores/coverages';
  import {riskColors} from 'components/market/shop/utils';
  import {productTypes} from 'components/market/utils';
  import ResultCategory from 'components/market/shop/cards/ResultCategory.vue';
  import ShopAiChip from 'components/market/shop/ai/ShopAiChip.vue';
  import ContactBar from 'components/market/shop/closing/ContactBar.vue';

  const { isAuthenticated } = loginPerson()

  const envStore = useEnvStore()
  const coverageStore = useCoverages();

  const { dark } = darkness()
  const router = useRouter();

  const props = defineProps({
    id: { default: 'AiShop' },
    plan: { required: false },
    enrollment: { required: false }
  })

  const setShop = ref()
  const { shop, shopStore, route, maybeSave, stats, setStat, hh } = shopGet({ shop: setShop });
  const { person, household: house, hhToCms } = shopHousehold(shop, hh)
  const sf = sessionFamily(envStore, { shop })

  const slidingSpend = ref(1000);
  const event_count = ref(1);
  const hover = ref('')

  const def_key = computed(() => {
    const len = hh.value.children.length;
    if (hh.value.spouse) {
      if (len) return 'family'
      return 'plus_spouse'
    } else if (len === 1) return 'plus_child'
    else if (len) return `plus_child__${Math.min(3, len)}`
    return 'single'
  })

  const selected = ref();
  const togglePopup = (val) => {
    if (!val) {
      selected.value = undefined;
    }
  }

  const simsRun = computed(() => (Object.keys(shop.value?.coverage_scores || {}).length || 1) * 2.39 * (shop.value?.simsCount || 100) * 10)

  const fullPlan = computed(() => props.plan || {})
  const fullEnrollment = computed(() => props.enrollment || {});

  const loading = ref(false);

  const simDialog = ref(false);

  const byId = ref(byIdObj())
  const chatOn = ref(false)

  const mult = computed(() => shop.value.mult || 12)
  const updateMult = (val) => {
    maybeSave('mult', val)
  }

  const { checkPlace, place } = placeManager(stats, (val, path) => setStat(val, path, resetMarket))

  const setZipData = (data) => {
    if (data?.fips) {
      const newLoc = setLocation(data, envStore)
      const newPlace = { state: getStateCode(newLoc.region), countyfips: newLoc.fips, zipcode: newLoc.postal }
      setStat('place', newPlace, resetMarket)
      envStore.setPlace(newPlace)
      // setTimeout(() => {
      //   console.log('next place', stats.value.place)
      // }, 500)
    }
  }

  const setById = () => {
    byId.value = rankById(shop.value, activeTypes.value);
  }

  const { activeTypes, toggleType, activeList } = planTypeManager((val) => byId.value = rankById(shop.value, val))

  const planCoverageId = computed(() => shop.value.plan_coverage);
  const { employer, subsidy } = erSubsidy({
    plan: fullPlan,
    enrollment: fullEnrollment,
    coverageId: planCoverageId,
    person,
    household: house
  })

  const { item: plan_coverage } = idGet({
    store: coverageStore,
    value: planCoverageId,
    onLoad: (val) => {
      if (val?.ichra && shop.value.useAptc) maybeSave('useAptc', false);
    }
  })

  const tax_rate = computed(() => plan_coverage.value.ichra ? .06 : 0);

  //TODO: create an affordability calculation where part-time employees can still see the tax credit
  const toggleAptc = (val) => {
    if (plan_coverage.value?.ichra || props.plan?.ale) {
      if (shop.value.useAptc) maybeSave('useAptc', false);
      return;
    }
    if (val !== shop.value.useAptc) {
      maybeSave('useAptc', val);
      setById()
    }
  }

  const notabot = ref(false);
  const showTurnstile = ref(true);


  const { getResetId, lastResetId, validPlace, resetTo } = shopResetId()

  const { resetCountdown, countdown, interval } = countdownUtils()


  const resetMarket = async (path, rerun, force) => {

    if (resetTo.value) clearTimeout(resetTo.value);
    if (interval.value) clearInterval(interval.value);

    countdown.value = 0;
    const shopId = shop.value._id || SessionStorage.getItem('shop_id') || route.params.shopId;

    resetTo.value = setTimeout(async () => {
      countdown.value = 0;
      if (interval.value) clearInterval(interval.value);
      if (!notabot.value && !isAuthenticated.value) return;
      if (route.params.shopId && !shopId) {
        if (sf.age.value) setStat('age', sf.age.value)
        if (sf.gender.value) setStat('gender', sf.gender.value)
        const addingPpl = [];
        if (sf.spouse.value) addingPpl.push({ age: sf.spouse.value[0], gender: sf.spouse.value[1], relation: 'spouse' })
        if (sf.deps.value.length) {
          for (const dep of sf.deps.value) {
            addingPpl.push({ age: dep[0], gender: dep[1], relation: 'child', child: true })
          }
        }
        if (addingPpl.length) setStat('people', addingPpl)
        if (sf.income.value) setStat('income', sf.income.value)
        setTimeout(() => resetMarket(path, rerun), 500)
        return;
      }
      loading.value = true;
      try {
        const household = house.value?._id ? {
          people: hhToCms.value,
          income: stats.value.income
        } : genCmsHousehold(stats.value);
        const household_size = (stats.value.people?.length || 1) + 1
        if (stats.value.place?.countyfips) {
          envStore.setPlace(stats.value.place);
        }
        await checkPlace()


        const resetId = getResetId(shop.value);
        if (!lastResetId.value) lastResetId.value = shop.value.resetId || '';
        const idMismatch = resetId !== lastResetId.value

        const shouldRerun = validPlace(place.value) && ((shop.value._id && !shop.value.spend_dist) || idMismatch || force)
        console.log('should rerun?', shouldRerun, path, rerun, shop.value._id);
        if (!shouldRerun) {
          console.log('no rerun')
          countdown.value = 0;
          setById()
        } else {
          console.log('yes rerun');
          if (shopId && !force && !rerun) return resetCountdown(() => resetMarket('', true))
          console.log('beyond force')
          lastResetId.value = resetId;
          const planId = fullEnrollment.value?.plan || fullPlan.value?._id
          const mr = await shopStore.get(shop.value?._id || route.params.shopId || fakeId, {
            query: { $limit: 150, household, household_size, place: place.value },
            runJoin: {
              cost_sim: {
                tax_rate: planId ? .06 : 0,
                skip_aptc: !!fullPlan.value.ale,
                data: {
                  resetId,
                  person: fullEnrollment.value?.person || person.value?._id || shop.value?.person,
                  enrollment: fullEnrollment.value?._id,
                  plan: planId,
                  gps: shop.value?.gps
                },
                stats: stats.value,
                household,
                risk: stats.value.risk || 5
              }
            }
          })
              .catch(err => {
                console.error(`Error fetching shop data: ${err.message}`)
                loading.value = false;
                return shop.value || {}
              })

          if (mr._id) {
            setShop.value = mr;
            SessionStorage.setItem('shop_id', mr._id);
            const { href } = router.resolve({ ...route, params: { ...route.params, shopId: mr._id } })
            window.history.pushState({}, '', href)
            await shopStore.get(mr._id);
            setById()
          }
        }

      } catch (e) {
        console.error(`Could not reset market: ${e.message}`)
        loading.value = false;
      } finally {
        loading.value = false;

        if (shop.value._id) {
          const { href } = router.resolve({ ...route, params: { ...route.params, shopId: shop.value._id } })
          window.history.pushState({}, '', href)
        }
        //SET STATS
        // if(shop.value._id) stats.value = getStats(shop.value?.stats)
        toggleAptc(!!shop.value?.useAptc)
      }
    }, force ? 100 : shopId ? 2000 : 200)
    // if (shopId && !force) resetCountdown()
  }


  watch(() => shop.value._id, (nv, ov) => {
    if (nv && nv !== ov) {
      setById()
    }
  }, { immediate: true });

  const aptc = computed(() => shop.value.aptc || 0);

  const openCheckout = () => {
    router.push({ name: 'shop-checkout', params: { shopId: shop.value._id } })
  }
  onMounted(() => {
    if (shop.value._id) lastResetId.value = getResetId(shop.value);
    const hostId = route.query.hostId
    setTimeout(() => {
      if (shop.value._id) {
        const patchObj = {};
        let run;
        if (hostId && !shop.value.host) {
          patchObj.host = hostId;
          run = true;
        }
        if (!shop.value.person && person.value._id) {
          patchObj.person = person.value._id
          run = true;
        }
        if (run) shopStore.patch(shop.value._id, patchObj)
      }
    }, 2000)
  })
</script>

<style lang="scss" scoped>
  .__but {
    font-weight: 600;
    font-family: var(--alt-font);
    color: white;
    font-size: var(--text-sm);
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));

  }

  .__clip {
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .__popup_wrap {
    width: 100%;
    z-index: 1000;
  }

  .__aich {
    color: var(--ir-text);


    .__stats {
      width: 100%;
      color: var(--ir-text);
      //background: var(--ir-bg-grad);
      background: radial-gradient(var(--ir-bg1) -20%, var(--ir-bg2));
      padding: 40px 2vw;
      //margin: max(2vw, 20px) 0;
      border-radius: min(1.4vw, 20px);
    }

    .__s {
      width: 100%;
      padding: 20px 2vw;
    }

    .__second {
      position: relative;
    }

    .__dsbl {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      z-index: 10;
      pointer-events: none;
      background: rgba(255, 255, 255, .5);
      backdrop-filter: blur(5px);
    }

    .__ovr {
      z-index: 1;
      position: absolute;
      top: 14%;
      left: 0;
      width: 100%;
      height: 38%;
      //background: linear-gradient(90deg, var(--ir-bg2), var(--ir-p), var(--ir-a), var(--ir-s), var(--ir-bg2));
    }

    .__st {
      width: 100%;
      max-width: 700px;
      z-index: 2;
    }

    .__pt {
      border-radius: 10px;
      margin-top: 60px;
      padding: min(3vw, 20px) 0;

      > div {
        height: 100%;
        width: 100%;
        //border-radius: inherit;
        background: var(--ir-bg);
      }
    }


    .__allowance {
      padding-top: 0;
      cursor: pointer;
      font-weight: 500;

      > div {
        padding: 5px 0;
      }
    }

    .__pi {
      border-radius: 10px;
      padding: 8px;
      margin: 10px 0;
    }

    .__ex {
      width: 100%;
      padding: 10px 0;

      > div {
        display: grid;
        align-items: center;
        grid-template-rows: auto auto;

        > div {
          font-size: var(--text-xxs);
          padding: 0 5px;

          &:first-child {
            font-weight: 600;
            color: var(--ir-off);
            padding: 15px 5px 5px 5px;
          }
        }
      }
    }


  }

  .__d {
    backdrop-filter: blur(5px);

    > div {
      background: var(--ir-bg2);
      color: var(--ir-text);
      border-radius: 10px;
    }
  }

  .__explore {
    padding: 30px max(2vw, 15px);
  }

  .__toc_lines {
    > div {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 8px 0;

      // left text
      > div:first-child {
        flex: 0 0 auto;
        order: 0;
      }

      // the dotted/dashed leader (generated in the row)
      &::before {
        content: "";
        order: 1;
        flex: 1 1 auto;
        border-bottom: 1px dashed var(--ir-mid); // change to dotted if you prefer
        margin: 0 0.5rem;
        // keeps the line vertically centered even if line-height varies
        transform: translateY(0.05em);
      }

      // right page number
      > div:nth-child(2) {
        flex: 0 0 auto;
        order: 2;
        margin-left: 0.5rem;
        text-align: right;
        white-space: nowrap;
      }
    }
  }

  .__pck {
    border-radius: 10px;
    background: white;
    box-shadow: 0 2px 6px var(--ir-light);
    padding: 15px;
    margin: 15px 0;
    position: relative;
    display: block;

    ._lbl {
      padding: 6px 0;
      font-weight: 600;
      font-family: var(--alt-font);
      font-size: 1rem;
      color: var(--ir-deep);
      border-radius: 10px 10px 0 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      > div {
        &:first-child {
          flex: 0 0 auto;
          order: 0;
        }

        &:nth-child(2) {
          transition: all .3s;
          max-width: 200px;
          opacity: 1;
          padding-left: 15px;
        }
      }
    }

    ._bod {
      padding: 10px 1vw;
      transition: all .3s;
      opacity: 1;
      max-height: 1000px;
    }

    .__off {
      opacity: 0;
      max-height: 0;
      padding: 0;
      overflow: hidden;
    }

    .__soff {
      opacity: 0 !important;
      max-width: 0 !important;
      padding: 0 !important;
      overflow: hidden !important;
    }
  }

  .__cb {
    background: linear-gradient(130deg, var(--q-a10), black);
  }

  .__ai_chip {
    width: 100%;

    .AiChip {
      padding: 30px 1vw;
      border-radius: 15px;
      background: linear-gradient(130deg, var(--q-p10), var(--q-a10), var(--q-s10));
      box-shadow: 0 2px 8px var(--ir-light);
      width: 1500px;
      max-width: 100%;
      transition: all .3s;
      display: grid;
      grid-template-rows: 1fr auto;
    }

    .__on {
      width: 750px;
    }
  }
</style>
