<template>
  <div class="_fw">
    <div v-if="!ermt.shop" class="tw-six q-py-sm">Choose your own coverage</div>

    <div class="q-pa-md  _fw" v-else>
      <div class="q-pt-sm q-pb-md">Your Selected Coverage
        <q-btn v-if="ermt.status === 'open'" dense flat icon="mdi-pencil-box" color="accent" class="_i_i"
               @click="openShop"></q-btn>
      </div>
      <div v-if="coverage.ichra && coverageObj.individual_coverage" class="tw-six">
      </div>
      <div class="__pc">
        <div class="__load flex flex-center" v-if="loading">
          <ai-logo></ai-logo>
        </div>
        <policy-card v-if="coverageObj.policy" :enrollment="enrollment" :dark="dark" :model-value="policy"
                     :aptc="shop.aptc"></policy-card>
        <coverage-card
            v-else-if="coverageObj.individual_coverage"
            :model-value="coverageObj.fullCoverage || coverageObj.individual_coverage"
            :dark="dark"
            :enrollment="enrollment"
        ></coverage-card>
      </div>
    </div>

    <single-contribution-table v-bind="{ coverage, plan, person:prsn, enrollment }"></single-contribution-table>

    <div class="_fw q-py-md" v-if="enrollment.status === 'open'">
      <div class="tw-six font-1r q-pa-md text-center">We analyzed the options for you - see how they ranked
      </div>
      <div class="row justify-center">
        <q-btn @click="openShop" color="accent" no-caps class="tw-six" push>
          <ai-logo opaque dark></ai-logo>
          <span class="q-ml-xs">Shop Now</span>
        </q-btn>
      </div>
    </div>


  </div>
</template>

<script setup>
  import SingleContributionTable from 'components/enrollments/utils/SingleContributionTable.vue';
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {useRouter} from 'vue-router';
  import {idGet} from 'src/utils/id-get';
  import {computed, nextTick, ref, watch} from 'vue';
  import {useHouseholds} from 'stores/households';
  import {usePpls} from 'stores/ppls';
  import {convertCmsPerson, personToPeople} from 'components/market/household/utils';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {useShops} from 'stores/shops';

  import {useEnrollments} from 'stores/enrollments';
  import {taxBreakdown} from 'components/households/utils/tax-tables';
  import {useMarketplace} from 'stores/marketplace';
  import {LocalStorage} from 'symbol-auth-client';

  const router = useRouter();
  const hhStore = useHouseholds();
  const pplStore = usePpls();
  const junkStore = useJunkDrawers();
  const shopStore = useShops();
  const erStore = useEnrollments();
  const mStore = useMarketplace();

  const props = defineProps({
    dark: Boolean,
    coverage: { required: true },
    plan: { required: true },
    person: { required: false },
    enrollment: { required: true, default: () => ({}) }
  })

  const { item: ermt } = idGet({
    store: erStore,
    value: computed(() => props.enrollment)
  })
  const { item: prsn } = idGet({
    store: pplStore,
    value: computed(() => ermt.value.person)
  })
  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => prsn.value?.household)
  })
  const { item: shop } = idGet({
    store: shopStore,
    value: computed(() => ermt.value.shop)
  })

  const coverageObj = computed(() => (ermt.value.coverages || {})[props.coverage?._id] || {})

  const income = computed(() => {
    return taxBreakdown(hh.value, {})
  })

  const cmsHh = computed(() => {
    const people = [convertCmsPerson(prsn.value), ...(shop.value.stats?.people || []).filter(a => !a.inactive).map(a => convertCmsPerson(a))];
    const place = { ...shop.value.stats?.place };
    return { household: { people, income: shop.value.stats?.income }, place }
  })
  const policy = ref({});
  const loading = ref(false);
  const loadPolicy = async (tries = 0) => {
    if (coverageObj.value.fullPolicy) return policy.value = coverageObj.value.fullPolicy
    loading.value = true;
    if (shop.value._id) {
      nextTick(async () => {
        const p = await mStore.get(coverageObj.value.policy, {
          runJoin: {
            get_plan: {
              household: cmsHh.value.household,
              place: cmsHh.value.place,
              id: coverageObj.value.policy
            }
          }
        })
            .catch(err => console.error(`Error getting cms policy - ${err.message}`))
        if (p?._id) {
          policy.value = p;
          const $set = {
            [`coverages.${props.coverage._id}.fullPolicy`]: p
          }
          if(!p.off_exchange && shop.value?.aptc) $set[`coverages.${props.coverage._id}.aptc`] = shop.value.aptc
          await erStore.patch(ermt.value._id, {
            $set
          })
        }
        loading.value = false;
      })
    } else if (tries < 10) setTimeout(() => loadPolicy(tries + 1), 500);
  }
  watch(() => coverageObj.value.policy, async (nv) => {
    if (nv && !policy.value._id) {
      loadPolicy(0)
    }
  }, { immediate: true });

  const opening = ref(false)
  const openShop = async () => {
    opening.value = true;
    let shopId = ermt.value?.shop;
    const pplIds = Object.keys(hh.value.members || {})
    const participants = (ermt.value?.coverages || {})[props.coverage._id]?.participants || pplIds
    if (!shopId) {
      /** Create shop using household/enrollment stats */
      const pplRes = await pplStore.find({ query: { _id: { $in: pplIds } } })
          .catch(err => console.error(`Error finding people: ${err.message}`))

      let people = (pplRes?.data || []).map(a => personToPeople({ ...a, inactive: !participants.includes(a._id) }));

      const address = (ermt.value.address || prsn.value.address) || {}
      let place;
      if (address?.postal) {
        const dr = await junkStore.find({ query: { $limit: 1, itemId: `zips|${address.postal.substring(0, 3)}` } })
            .catch(err => console.error(`Error getting places drawer: ${err.message}`))
        if (dr) {
          const z = dr.data[0]?.data[address.postal] || {};
          place = { countyfips: z.fips, zipcode: address.postal, state: z.state }
        }
      }
      const newShop = {
        person: prsn.value._id || ermt.value.person,
        plan: props.plan._id,
        planYear: ermt.value.planYear,
        version: ermt.value.version,
        host: props.plan.host || LocalStorage.getItem('host_id'),
        enrollment: ermt.value._id,
        plan_coverage: props.coverage._id,
        stats: {
          people,
          ...personToPeople(prsn.value),
          city: address.city,
          place,
          risk: 3,
          income: income.value.magi
        },
        useAptc: true
      }
      const added = await shopStore.create(newShop);
      shopId = added._id
    } else if (participants.length) {
      const people = [...shop.value.stats?.people || []]
      let patch;
      for (let i = 0; i < people.length; i++) {
        if (!participants.includes(people[i]._id)) {
          if (!people[i].inactive) {
            patch = true;
            people[i].inactive = true;
          }
        }
      }
      if (patch) await shopStore.patch(shop.value._id, { $set: { ['stats.people']: people } })
    }
    if (shopId !== ermt.value.shop) await erStore.patch(ermt.value._id, { shop: shopId })
    const { href } = router.resolve({ name: 'shop', params: { shopId } });
    window.open(href, '_blank')
  }
</script>

<style lang="scss" scoped>
  .__pc {
    position: relative;

    .__load {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      background: rgba(255, 255, 255, .5);
      backdrop-filter: blur(5px);
      z-index: 4;
    }
  }
</style>
