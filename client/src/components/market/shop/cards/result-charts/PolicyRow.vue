<template>
  <div class="__i alt-font" :style="{ borderLeft: `solid 3px var(--q-${pt.color})`}">
    <div class="main-font">
      <div class="__top text-xxs q-pb-xs">
        <div v-if="mv.carrierLogo" class="q-pr-sm">
          <q-img :dark="dark" size="25px" class="h20 w20" :color="`ir-grey-${dark ? '10' : '3'}`" :src="logo">
          </q-img>
        </div>
        <div>{{ mv.carrierName }}</div>
      </div>
      <div class="text-xxs tw-six __t">{{ shortName }}
        <q-tooltip class="text-xxs tw-six">{{ mv.name }}</q-tooltip>
      </div>
    </div>
    <div>{{ dollarString(zeroMax(score.premium - ptc) * (1 / (13 - mult)), '$', 0) }}<span class="text-xxs tw-four">{{mult === 12 ? '/yr' : '/mo'}}</span></div>
    <div>{{ dollarString((score.average - zeroMax(score.premium - ptc)) * (1 / (13 - mult)), '$', 0) }}</div>
    <div class="tw-six text-primary">{{ dollarString(score.average * (1 / (13 - mult)), '$', 0) }}</div>
    <div>{{ dollarString(((score.top1 || 0) / simsCount) * 100, '', 0) }}%</div>
    <div>{{ dollarString(((score.top3 || 0) / simsCount) * 100, '', 0) }}%</div>
    <div>{{ dollarString(((score.last || 0) / simsCount) * 100, '', 0) }}%</div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useUploads} from 'stores/uploads';
  import {dollarString} from 'symbol-syntax-utils';
  import { productTypes } from 'src/components/market/utils';

  const uploadStore = useUploads()

  const props = defineProps({
    useAptc: Boolean,
    modelValue: { required: true },
    score: { required: true },
    rank: { type: Number, required: true },
    dark: Boolean,
    mult: { default: 1 },
    simsCount: { default: 1 }
  })

  const zeroMax = (val) => Math.max(0, val);
  const mv = computed(() => props.modelValue || {})
  const ptc = computed(() => mv.value.off_exchange ? 0 : props.useAptc ? props.score?.aptc || 0 : 0)

  const pt = computed(() => {
    const key = mv.value.acaPlan ? 'aca' : mv.value.type
    return productTypes[key] || productTypes.other
  })

  const { item: upload } = idGet({
    store: uploadStore,
    value: computed(() => mv.value.carrierLogo?.uploadId),
  })

  const logo = computed(() => {
    const url = upload.value?.url || mv.value.carrierLogo
    if(typeof url === 'string') return url
    return ''
  })

  const shortName = computed(() => {
    if (!mv.value.carrierName) return mv.value.name;
    else {
      const regex = new RegExp(mv.value.carrierName, 'gi');
      return mv.value.name.replace(regex, '').split('  ').join(' ')
    }
  })

</script>

<style lang="scss" scoped>

  .__i {
    width: 100%;
    padding: 0;
    display: grid;
    grid-template-columns: 25% 12fr 12fr 12fr 12fr 12fr 12fr;
    align-items: center;

    > div {
      padding: 10px;
      font-size: var(--text-xs);
    }

    .__top {
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;

      > div {

        &:nth-child(2) {
          text-align: left;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .__t {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
