import {useEnvStore} from 'stores/env';
import {storeToRefs} from 'pinia';
import {computed, ComputedRef, Ref, ref} from 'vue';
import {getStateCode, getStateName} from 'components/common/geo/data/states';
import {useJunkDrawers} from 'stores/junk-drawers';
import {getAge} from 'components/enrollments/ichra/utils/index';
import {usePpls} from 'stores/ppls';
import {idGet} from 'src/utils/id-get';
import {useHouseholds} from 'stores/households';
import {loginPerson} from 'stores/utils/login';
import {AnyRef} from 'src/utils/types';
import {HFind} from 'src/utils/hFind';
import {useMarketplace} from 'stores/marketplace';
import {useCoverages} from 'stores/coverages';
import {useRoute} from 'vue-router';
import {useAtcStore} from 'stores/atc-store';

export const personToPeople = (p: any) => {
    const age = p.age || p.age === 0 ? p.age : getAge(p.dob)
    const obj = {
        _id: p._id,
        age,
        child: age < 18 || p.dependent,
        smoker: (p.monthsSinceSmoked || 100) < 12,
        gender: p.gender,
        inactive: p.inactive
    }
    if(p.relation) obj.relation = p.relation
    else if(obj.child) obj.relation = 'child'
    return obj;
}

export const getCmsPerson = (age, child, smoker, gender?: string) => {
    return {
        age,
        child: !!child,
        smoker: !!smoker,
        gender: gender === 'female' ? 'Female' : 'Female'
    }
}

export const cmsDateFormat = (date: any) => {
    let dt = date;
    try {
        dt = new Date(date).toISOString().split("T")[0]
    } catch (e: any) {
        console.log(`Error creating cms date: ${e.message}`)
    }
    return dt;
}
export const convertCmsPerson = (p: any) => {
    const dob = cmsDateFormat(p.dob);
    const age = p.age || p.age === 0 ? p.age : getAge(p.dob);
    return {
        age,
        dob,
        child: !!(p.dependent || p.relation === 'child' || age < 18),
        smoker: !!(p.smoker || (p.monthsSinceSmoked || 100) < 12),
        gender: p.gender?.toLowerCase() === 'male' ? 'Male' : 'Female'
    }
}

export const genCmsHousehold = (stats: any) => {
    // let people = stats.people;
    // if (!people?.length) {
    const people = [getCmsPerson(stats.age, false, !!stats.smoker, stats.gender), ...(stats.people || []).filter(a => !a.inactive).map(a => convertCmsPerson(a))]

    // if (stats.spouse) people.push(getCmsPerson(stats.age, false, !!stats.smoker));
    // for (let i = 0; i < stats.plus; i++) {
    //     people.push(getCmsPerson(10, true, false))
    // }
    // } else {
    //     /** set stats from people array */
    //     let self = false;
    //     for (let i = 0; i < people.length; i++) {
    //         const { age, child } = people[i];
    //         if (age > stats.age) stats.age = age;
    //         if (age > 18 && !child) {
    //             if (!self) self = true;
    //             else stats.spouse = true;
    //         } else stats.plus = (stats.plus || 0) + 1;
    //     }
    // }
    return {income: stats.income, people, place: stats.place}
}

export const setLocation = (zd:any, envStore:any) => {
    const newLoc = { region: getStateName(zd.state), city: zd.city, county: zd.county, postal: zd.zip, lngLat: zd.lngLat, country: 'US', fips: zd.fips }
    envStore.setLocation(newLoc)
    return newLoc
}

export const isNewPlace = (newPlace, oldPlace) => {
    if(newPlace && !oldPlace) return true;
    if(!newPlace) return false;
    const newId = `${newPlace.state}|${newPlace.countyfips}|${newPlace.zipcode}`
    const { state, countyfips, zipcode } = oldPlace || {}
    const oldId = `${state}|${countyfips}|${zipcode}`
    return newId !== oldId
}
export const placeManager = (stats: Ref<any> | ComputedRef<any>, setStat: (key: string, val: any, cb?: () => void) => void) => {
    const envStore = useEnvStore();
    const junkStore = useJunkDrawers();

    const setStatFn = setStat ? setStat : () => console.log('no set stat provided to place manager');
    const {city, region} = storeToRefs(envStore)
    const zipData: Ref<{ fips?: string, zip?: string, state?: string } & any> = ref({});
    const jd: Ref<any> = ref({})

    const useCity = computed(() => stats.value.city || city.value)

    const stateCode = computed(() => getStateCode(stats.value.place?.state) || getStateCode(region.value || 'NC'))

    const place = computed(() => {
        return {
            state: stateCode.value || zipData.value.state || envStore.place?.state,
            countyfips: stats.value.place.countyfips || zipData.value.fips || envStore.place?.countyfips,
            zipcode: stats.value.place.zipcode || zipData.value.zip || envStore.place?.zipcode
        }
    })

    const checkPlace = async () => {
        const {postal, fips, region} = envStore.location || {}
        if (postal && (!fips || !region)) {
            if (!jd.value || jd.value.data?.zip !== postal) {
                const jds = await junkStore.find({query: {$limit: 1, itemId: `zips|${postal.substring(0, 3)}`}})
                    .catch(err => {
                        console.error(`place manager - could not get zip drawer: ${err.message}`)
                        return {data: []}
                    })
                jd.value = jds.data[0]
            }
            zipData.value = {...(jd.value.data || {})[postal], zip: postal};
            envStore.setPlace(place.value)
            if(place.value?.countyfips && isNewPlace(place.value, stats.value.place)) {
                setStatFn('place', place.value)
            }
        }
    }

    return {
        envStore,
        city,
        region,
        useCity,
        zipData,
        jd,
        place,
        stateCode,
        checkPlace,
    }
}

export const shopStatPerson = (id: string, shop: any) => {
    const { stats = {} } = shop || {};
    if(id === shop.person) return { age: stats.age, gender: stats.gender, smoker: stats.smoker, relation: 'self', inactive: stats.inactive }
    return {...(stats?.people || []).filter(a => a._id === id)[0]}
}

type CmsPerson = { age: number, smoker: boolean, gender: string, child: boolean } & any
type HH = {
    children: Array<CmsPerson>,
    spouse: CmsPerson,
    self: CmsPerson
}
type PolicyOptions = {
    place: AnyRef,
    checkPlace?: () => void,
}
export const shopHousehold = (shop: AnyRef<any>, hh: AnyRef<HH>, policyOptions?: PolicyOptions) => {
    const route = useRoute()
    const pplStore: any = usePpls();
    const hhStore = useHouseholds();
    const {person: lPerson} = loginPerson()

    const stats = computed(() => shop.value?.stats || {})

    const {item: person} = idGet({
        store: pplStore as any,
        value: computed(() => shop.value._fastjoin?.person || shop.value.person || lPerson.value?._id),
        useAtcStore
    })
    const {item: house} = idGet({
        store: hhStore as any,
        value: computed(() => person.value?.household),
        params: ref({runJoin: {total_income: true}}),
        useAtcStore
    })
    const hhMemberIds = computed(() => Object.keys(house.value?.members || {}));

    const inStore = computed(() => pplStore.findInStore({query: {_id: {$in: hhMemberIds}, $limit: 25}}))
    const {h$: p$} = HFind({
        store: pplStore,
        limit: computed(() => hhMemberIds.value.length),
        pause: computed(() => inStore.value.total >= hhMemberIds.value.length),
        params: computed(() => {
            return {
                query: {
                    _id: {$in: hhMemberIds.value}
                }
            }
        })
    })

    const actualHh = computed(() => {
        if (!house.value?._id) return hh.value
        const children = [];
        let spouse;
        const byId = {};
        for(const p of p$.data) byId[p._id] = p;
        for(const p of inStore.value.data || []) {
            if(!byId[p._id]) byId[p._id] = p;
        }
        for (const k in byId) {
            if(house.value.person === k) continue;
            const p = byId[k]
            const mbr = (house.value.members || {})[p._id]
            const prsn = personToPeople({...shopStatPerson(p._id, shop.value), ...p, ...mbr})
            const age = getAge(p.dob) || p.age || prsn.age || 0;
            prsn.age = age;
            if(!prsn.relation) prsn.relation = p.dependent || age < 18 ? 'child' : (Math.abs(age - shop.value.stats?.age) < 16) ? 'spouse' : 'child'
            if (prsn.relation === 'child') children.push(prsn)
            else spouse = prsn
        }
        return {
            self: personToPeople({...person.value, ...shopStatPerson(person.value._id, shop.value), relation: 'self'}),
            spouse,
            children
        }
    })

    const hhToCms = computed(() => {
        const arr = [actualHh.value.self, ...actualHh.value.children]
        if (actualHh.value.spouse) arr.push(actualHh.value.spouse);
        return arr.filter(a => !a.inactive);
    })

    const hhChanges = computed(() => {
        if (!house.value?._id) return false;
        let change = false;
        for (const k of ['age', 'smoker']) {
            if ((hh.value.spouse && !actualHh.value.spouse) || (actualHh.value.spouse && !hh.value.spouse)) change = true;
            else if ((hh.value.self || {})[k] !== (actualHh.value.self || {})[k]) change = true;
            else if ((hh.value.spouse || {})[k] !== (actualHh.value.spouse || {})[k]) change = true;
        }
        if (!change) change = hh.value.children.length !== actualHh.value.children.length

        return change;
    })

    /**LOAD SHOP POLICY*/

    const marketStore = useMarketplace();
    const coverageStore = useCoverages();

    const policy = ref({})

    const loadTo = ref()
    const loadPlans = async () => {
        if (!policyOptions?.place) {
            console.log('Could not load plans for shop session - no place provided')
            return;
        }
        if (loadTo.value) clearTimeout(loadTo.value)
        loadTo.value = setTimeout(async () => {
            const nv = shop.value;
            // console.log('load plans', nv);
            if (nv.policy) {
                let hhArgs = nv.stats;
                if (house.value?._id) {
                    const people = [...actualHh.value.children];
                    if (actualHh.value.spouse) people.push(actualHh.value.spouse);
                    hhArgs = {...actualHh.value.self, people}
                }

                const cmsHh = genCmsHousehold(hhArgs);
                cmsHh.income = stats.value.income
                if (policyOptions?.checkPlace) await policyOptions.checkPlace()
                // console.log('got househld', household);
                policy.value = await marketStore.get(route.params.policyId || nv.policy, {
                    runJoin: {
                        get_plan: {
                            id: route.params.policyId || nv.policy || nv.coverage,
                            household: cmsHh,
                            place: policyOptions.place.value
                        }
                    }
                } as any)
            } else if (nv.coverage) policy.value = await coverageStore.get(nv.coverage);
        }, 1000)

    }

    return {
        policy,
        loadPlans,
        hhToCms,
        person,
        hhChanges,
        household: house,
        actualHh,
        hhMemberIds
    }

}
