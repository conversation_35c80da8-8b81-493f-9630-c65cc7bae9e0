<template>
  <div class="_fw relative-position">
    <div class="__chat_wrap">
      <div>
        <slot name="top"></slot>
      </div>
      <div class="__chat_main">
        <div :class="`__subjects ${subjectsOn ? '' : '__subjects_off'}`">
          <div class="row" v-if="$q.screen.width < 800">
            <q-chip clickable color="transparent" @click="subjectDrawer = false">
              <q-icon color="primary" name="mdi-chevron-left" class="q-mr-sm"></q-icon>
              <span>Close</span>
            </q-chip>
          </div>
          <slot name="subjects"></slot>
        </div>
        <div class="__body">
          <div>
            <slot name="body-top"></slot>
          </div>
          <div class="_fa">
            <q-tab-panels class="_panel _fh" :model-value="!!selected" animated transition-prev="fade-in" transition-next="fade-out">
              <q-tab-panel class="_panel _fh" :name="true">
                <div class="__chat">
                  <div class="row items-center q-py-sm">
                    <q-btn color="accent" dense flat size="sm" icon="mdi-arrow-left" @click="emit('update:selected', undefined); subjectDrawer = true"></q-btn>
                    <q-chip color="ir-bg2" v-if="plan._id">
                      <default-avatar :model-value="plan.org" :store="orgStore" :use-atc-store="useAtcStore"></default-avatar>
                      <span>{{ $limitStr(plan.name, 20, '...') || plan.name }}</span>
                      <q-tooltip>{{ plan.name }}</q-tooltip>
                    </q-chip>
                    <q-chip color="ir-bg2" v-if="selected?.participant">
                      <default-avatar
                          :model-value="{...participant, ...selected.participant}"
                          :use-atc-store="useAtcStore"></default-avatar>
                      <span>{{participant?.name || selected.participant.name}}</span>
                    </q-chip>
                    <div class="q-px-sm">|</div>
                    <avatar-row v-if="p$.total > 1" :model-value="p$.data" :use-atc-store="useAtcStore"></avatar-row>
                    <q-chip color="ir-bg2" v-else-if="p$.total">
                      <default-avatar :model-value="p$.data[0]" :use-atc-store="useAtcStore"></default-avatar>
                      <span>{{p$.data[0].name}}</span>
                    </q-chip>
                  </div>
                  <div class="_fa bg-white">
                    <im-chat :pid="pid" :notabot="notabot" :model-value="selected"></im-chat>
                  </div>
                </div>
              </q-tab-panel>
              <q-tab-panel class="_panel" :name="false">
                <slot name="before-list"></slot>
                <slot name="list">
                  <div class="q-pa-sm font-3-4r tw-six text-ir-mid">Chats ({{total || chats?.length}})</div>

                  <div class="__list">
                    <q-virtual-scroll
                        class="__vs"
                        :items="chats"
                        v-slot="{ item, index }"
                        @virtual-scroll="emit('vs', $event)"
                    >
                      <div
                          class="__im _hov cursor-pointer"
                          @click="emit('update:selected', item)">
                        <im-item :model-value="item" :key="`im-${index}`"></im-item>

                        <div class="q-pt-sm">
                          <q-chip class="tw-six" dark label="New Messages" size="sm" color="orange" v-if="unopened(item)"></q-chip>
                          <q-chip class="tw-six" dark size="sm" color="accent" label="No Support" v-if="!Object.keys(item.support || {}).length"></q-chip>
                        </div>
                      </div>

                    </q-virtual-scroll>
                  </div>
                </slot>
              </q-tab-panel>
            </q-tab-panels>
          </div>
        </div>
        <div class="__extra"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ImChat from 'components/ims/in-app/cards/ImChat.vue';
  import ImItem from 'components/ims/in-app/cards/ImItem.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';

  import {usePlans} from 'stores/plans';
  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useAtcStore} from 'stores/atc-store';
  import {HFind} from 'src/utils/hFind';
  import {useOrgs} from 'stores/orgs';
  import {$limitStr} from 'src/utils/global-methods';

  const planStore = usePlans();
  const pplStore = usePpls();
  const orgStore = useOrgs();

  const subjectDrawer = ref(false);
  const subjectsOn = computed(() => {
    if(Screen.width >= 800) return true;
    return subjectDrawer.value
  })

  const emit = defineEmits(['update:selected', 'vs']);
  const props = defineProps({
    notabot: { required: true },
    selected: { required: false },
    chats: Array,
    pid: String,
    total: Number
  })

  const { item:plan } = idGet({
    store: planStore,
    value: computed(() => props.selected?.plan)
  })

  const supportIds = computed(() => Object.keys(props.selected?.support || {}))
  const {h$:p$} = HFind({
    store: pplStore,
    pause: computed(() => !props.selected),
    limit: computed(() => supportIds.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: supportIds.value }
        }
      }
    })
  })

  const unopened = (iM) => {
    const msgs = iM.messages || [];
    return msgs.filter(a => {
      if(!a.pid === props.pid) return false
      const opb = a.openedBy || []
      return !opb.includes(props.pid)
    }).length;
  }

  const participant = ref({})

  const loadParticipant = async () => {
    let ppl = pplStore.findInStore({ query: { login: participantLogin.value } })
    if(ppl.total) participant.value = ppl.data[0]
    else {
      ppl = await pplStore.find({ query: { login: participantLogin.value } })
      if(ppl.total) participant.value = ppl.data[0]
    }
  }

  const participantLogin = computed(() => props.selected?.participant?.login)
  watch(participantLogin, (nv, ov) => {
    if(nv && nv !== ov){
      loadParticipant()
    }
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .__chat_wrap {
    width: 100%;
    display: grid;
    grid-template-rows: auto 1fr;
    min-height: calc(95vh - 80px);
  }

  .__chat_main {
    width: 100%;
    display: grid;
    position: relative;
    grid-template-columns: auto 1fr auto;
  }
  .__subjects {
    height: 100%;
    overflow-y: scroll;
    width: 300px;
    max-width: 25vw;
  }

  .__body {
    height: 100%;
    width: 100%;
    display: grid;
    grid-template-rows: auto 1fr;
    border-left: solid 1px var(--ir-light);
    background: var(--ir-bg1);
    overflow-y: scroll;
    padding: 10px;
  }
  .__chat {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr;
  }
  .__list {
    width: 100%;
    max-width: 500px;
    border-radius: 10px;
    background: white;
    padding: 10px 5px;

    .__vs {
      max-height: 100%;
    }

    .__im {
      padding: 5px;
      border-radius: 8px;
      border-bottom: solid 1px var(--ir-light);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  @media screen and (max-width: 800px) {
    .__chat_main {
      grid-template-columns: 100%;
    }

    .__subjects {
      position: absolute;
      top: 0;
      left: 0;
      transition: all .3s ease;
      max-width: 100%;
      z-index: 10;
      background: white;
    }

    .__subjects_off {
      max-width: 0;
      padding: 0;
      overflow: hidden;
    }

  }
</style>
