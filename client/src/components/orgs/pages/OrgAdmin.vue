<template>
  <q-page class="_bg_ow">
    <template v-if="isAdmin">
      <div class="row justify-center q-py-lg">
        <div class="_cent br20 __box pa3 text-white">
          <div class="flex items-center">
            <default-avatar :imageStyle="{ objectFit: 'contain' }" size-in="70px" dark :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
            <div class="q-pl-md">
              <div class="text-sm tw-six">{{ org.name }}</div>
              <div class="text-white text-xs tw-five">Group Administration</div>
            </div>
          </div>
          <div style="height: 50px" class="_fw"></div>
          <div class="b-l-a q-px-md _fw">
            <q-tabs
                align="left"
                v-model="tab"
                no-caps
                @update:model-value="setTab"
                dark
            >
              <q-tab
                  v-for="(t, i) in Object.keys(tabs)"
                  :key="`t-${i}`"
                  :name="t"
                  :label="tabs[t].label"
              ></q-tab>
            </q-tabs>

          </div>

        </div>
      </div>
      <div class="row justify-center">
        <div class="_cent br20 bg-white">
          <q-tab-panels v-model="tab" class="__p">
            <q-tab-panel
                class="__p"
                v-for="(p, i) in Object.keys(tabs)"
                :key="`p-${i}`"
                :name="p"
                :label="tabs[p].label"
            >
              <component :is="tabs[p].component" v-bind="tabs[p].attrs"></component>
            </q-tab-panel>
          </q-tab-panels>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="pd8 row justify-center q-px-lg">
        <q-spinner></q-spinner>
        <div class="q-pl-sm">Checking your access</div>
      </div>

    </template>
  </q-page>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import OrgComps from 'components/comps/pages/OrgComps.vue';
  import PlansAdmin from 'components/plans/admin/pages/PlansAdmin.vue';
  import GroupMembers from 'components/groups/members/GroupMembers.vue';
  import OrgDetails from 'components/orgs/pages/OrgDetails.vue';
  import {useAtcStore} from 'stores/atc-store';

  import {computed, onMounted, ref, watch} from 'vue';
  import {canU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {useRoute, useRouter} from 'vue-router';
  const { login } = loginPerson()

  const route = useRoute();
  const router = useRouter();

  import {useOrgs} from 'stores/orgs';
  const orgStore = useOrgs();

  const props = defineProps({
    org: { required: false }
  })

  const tab = ref('plans');
  const setTab = (t) => {
    const {href} = router.resolve({...route, params: {tab: t }});
    window.history.pushState({}, '', href)
  };
  const orgId = computed(() => route.params.orgId || props.org?._id);
  const isAdmin = ref(false)

  const { item: org } = idGet({
    value: computed(() => orgId.value),
    store: orgStore
  })

  const tabs = computed(() => {
    return {
      'comp': {
        label: 'Compensation',
        component: OrgComps,
        attrs: {
          org: org.value
        }
      },
      'plans': {
        label: 'Health Plans',
        component: PlansAdmin,
        attrs: {
          org: org.value
        }
      },
      'people': {
        label: 'People',
        component: GroupMembers,
        attrs: {
          parent: org.value
        }
      },
      'details': {
        label: 'Details',
        component: OrgDetails,
        attrs: {
          modelValue: org.value
        }
      }
    }
  })

  watch(org, async (nv) => {
    const { ok } = await canU({
      requiredCapabilities: [[`orgs:${orgId.value}`, ['orgAdmin']], [`orgs:${orgId.value}`, ['WRITE']], ['orgs', 'WRITE']],
      or: true,
      subject: nv,
      login: login.value
    })
    isAdmin.value = ok;
  }, { immediate: true })

  onMounted(() => {
    if(route.params.tab && tabs[route.params.tab]) tab.value = route.params.tab;
  })
</script>

<style lang="scss" scoped>
  .__box {
    background: linear-gradient(122deg, var(--q-p9), var(--q-p7), var(--q-p9));
    position: relative;
  }
  .__p {
    padding: 0 !important;
    background: transparent;
  }
</style>
