<template>
  <q-page class="_fw _bg_ow">
    <div class="row justify-center">
      <div class="_cent mnh90 pw2">
        <div class="__title __c">
          <div class="__t relative-position">
            <div class="row items-center absolute-bottom q-pa-sm">
              <default-avatar :imageStyle="{ objectFit: 'contain' }" :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
              <div class="q-px-md tw-six font-1r">{{ org?.name }} Settings</div>
            </div>
          </div>
          <div class="row items-center __b">
            <q-tabs class="bg-transparent" indicator-color="primary" :model-value="$route.meta?.name" align="left" no-caps @update:model-value="$router.push({ name: $event })">
              <q-tab name="org-info" label="Info"></q-tab>
              <q-tab name="org-ownership" label="Owners"></q-tab>
              <q-tab name="org-banking" label="Banking"></q-tab>
              <q-tab name="org-control" label="Control"></q-tab>
              <q-tab v-for="(r, i) in addedPaths || []" :key="`p-${i}`" :name="r.name" :label="r.label"></q-tab>
            </q-tabs>
          </div>
        </div>

        <div class="__c">
        <router-view></router-view>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
import {useOrgs} from 'stores/orgs';
import {idGet} from 'src/utils/id-get';
import {computed} from 'vue';
import {useEnvStore} from 'stores/env';
import {useAtcStore} from 'stores/atc-store';

const envStore = useEnvStore()
const store = useOrgs();

const props = defineProps({
  modelValue: { required: true },
  addedPaths: Array
})

const { item: org } = idGet({
  store,
  value: computed(() => props.modelValue || envStore.getOrgId)
})
</script>

<style lang="scss" scoped>


  .__c {
    margin: 10px 0;
    background: white;
    //padding: 3vh 1vw;
    border-radius: 15px;
    box-shadow: 0 8px 12px -8px rgba(0,0,0,.2);
  }
  .__title {
    overflow:hidden;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 90px 50px;
    padding-bottom: 0;
    background: white;
  }

</style>
