<template>
  <default-chip
      v-bind="{
    modelValue: org,
    useAtcStore: useAtcStore,
    ...$attrs
      }"
  >
    <template v-slot:menu>
      <slot name="menu" :org="org"></slot>
    </template>
    <template v-slot:right>
      <slot name="right"></slot>
    </template>
  </default-chip>
</template>

<script setup>
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useOrgs} from 'src/stores/orgs';
  import {useAtcStore} from 'stores/atc-store';

  const store = useOrgs();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: org } = idGet({
    value: computed(() => props.modelValue),
    store
  })

</script>

<style lang="scss" scoped>

</style>
