<template>
  <q-page class="_bg_ow">
    <div class="row justify-center">
      <div class="_cent pd4 pw2">

        <div class="q-pa-md tw-six font-1-1-4r flex items-center">
          <default-avatar :imageStyle="{ objectFit: 'contain' }" :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
          <div class="q-mx-sm">{{org?.name}}</div>
        </div>

        <div class="flex items-center">
          <q-chip color="transparent">
            <span class="tw-six">{{ activePlan?.name || 'No plans found' }}</span>
            <q-icon v-if="p$.data?.length > 1"></q-icon>
          </q-chip>
          <plan-year-picker :model-value="activeYear" @update:model-value="yearSelect = $event"></plan-year-picker>
        </div>

        <div class="row">
          <div class="col-12 q-pa-sm" v-if="docReq._id && !groups.total && docReq.status !== 'complete'">
            <div class="__c">
              <div class="__t alt-font">Add Employees</div>
              <employees-from-census :req="docReq"></employees-from-census>
            </div>
          </div>
          <div class="col-12 q-pa-sm">
            <div class="__c">
              <div class="__t alt-font">Care Events</div>
              <care-dash :plan="activePlan" :org="org"></care-dash>
            </div>
          </div>
          <div class="col-12 q-pa-sm">
            <div class="__c">
              <div class="__t alt-font">Plan Claims</div>
              <plan-claims :plan="activePlan"></plan-claims>
            </div>
          </div>
          <div class="col-12 q-pa-sm">
            <div class="__c">
              <div class="__t alt-font">Plan Enrollment</div>
              <plan-dash :year="activeYear" :plan="activePlan" :org="org"></plan-dash>
            </div>

          </div>
          <div class="col-12 col-lg-6 q-pa-sm">
            <div class="__c">
              <div class="__t alt-font">Related Companies</div>
              <control-summary :org="org"></control-summary>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import PlanDash from '../PlanDash.vue';
  import ControlSummary from 'components/orgs/control/cards/ControlSummary.vue';
  import CareDash from 'components/orgs/dashboard/CareDash.vue';
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';
  import PlanClaims from 'components/claims/cards/PlanClaims.vue';
  import {useAtcStore} from 'stores/atc-store';
  import EmployeesFromCensus from 'components/plans/forms/EmployeesFromCensus.vue';

  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {computed, ref, watch} from 'vue';
  import {orgPlans} from 'components/orgs/dashboard/utils';
  import {useDocRequests} from 'stores/doc-requests';
  import {useGroups} from 'stores/groups';
  import {useEnvStore} from 'stores/env';

  const drStore = useDocRequests();
  const groupStore = useGroups();
  const orgStore = useOrgs();
  const envStore = useEnvStore();

  const groups = ref({});

  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => envStore.getOrgId),
    onLoad: async (val) => {
      if(val && !groups.value.total) groups.value = await groupStore.find({ query: { org: val._id }})
          .catch(err => {
            console.error(`Error finding groups:${err.message}`);
            return {}
          })
    }
  })

  const { activePlan, p$, activeYear } = orgPlans(org)

  const docReq = ref({})
  watch(activePlan, async (nv, ov) => {
    if(nv && nv._id !== ov?._id){
      const drs = await drStore.find({ query: { $sort: { createdAt: -1 }, plan: nv._id, status: { $ne: 'complete' }}})
      if(drs.total) docReq.value = drs.data[0];

    }
  }, { immediate: true });
</script>

<style lang="scss" scoped>
  .__c {
    margin: 5px 0;
    border-radius: 14px;
    background: white;
    box-shadow: 0 2px 8px -2px #999;
    padding: 60px 1.5vw 4vh 1.5vw;
    position: relative;
  }
  .__t {
    position: absolute;
    top: 0;
    left: 2.5%;
    transform: translate(0, -20%);
    padding: 10px max(1vw, 20px);
    border-radius: 8px;
    background: var(--q-p0);
    color: var(--q-p9);
    font-weight: 600;
    //width: 95%;
    font-size: 1rem;
    box-shadow: 0 2px 4px -2px #c6c6c6;
  }
</style>
