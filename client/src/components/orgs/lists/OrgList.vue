<template>
  <div class="_fw">
    <q-input v-model="search.text" v-bind="{ placeholder: 'Search People', ...inputAttrs}">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
    <q-list separator>
      <default-item v-for="(org, i) in selected" :key="`sel-${i}`" :model-value="org" :use-atc-store="useAtcStore">
        <template v-slot:side>
          <q-btn dense flat icon="mdi-close" color="red" @click="emitUp(org, true)"></q-btn>
        </template>
      </default-item>
      <default-item v-for="(org, i) in filtered" :key="`org-${i}`" :model-value="org" :use-atc-store="useAtcStore" @update:model-value="emitUp(org)"></default-item>
    </q-list>
  </div>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {useOrgs} from 'stores/orgs';
  import {useAtcStore} from 'stores/atc-store';

  const store = useOrgs();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    inputAttrs: Object,
    searchLimit: { default: 5 },
    params: Object,
    modelValue: { required: false },
    emitValue: Boolean,
    multiple: Boolean
  })

  const { search, searchQ } = HQuery({ keys: ['name', 'email']})

  const mvIds = computed(() => props.multiple ? props.emitValue ? [...props.modelValue || []] : [...props.modelValue || []].map(a => a._id) : props.modelValue ? [props.modelValue] : [])
  const { h$:o$ } = HFind({
    store,
    limit: computed(() => props.searchLimit + (props.modelValue || []).length),
    params: computed(() => {
      let q = {
        ...searchQ.value,
        ...props.params?.query
      }
      if(props.multiple ? props.modelValue?.length : !!props.modelValue){
        q = { $or: [q, { _id: { $in: mvIds.value}}]}
      }
      return {
        ...props.params,
        query: q
      }
    })
  })

  const selected = computed(() => (o$.data || []).filter(a => mvIds.value.includes(a._id)))

  const filtered = computed(() => {
    return (o$.data || []).filter(a => !mvIds.value.includes(a._id))
  })

  const emitUp = (val, rmv) => {
    const useV = props.emitValue ? val._id : val;
    if(props.multiple){
      const list = [...props.modelValue || []]
      const idx = list.indexOf(useV);
      if(idx > -1) list.splice(idx, 1);
      else list.push(useV);
      emit('update:model-value', list);
    } else rmv ? emit('update:model-value', undefined) : emit('update:model-value', useV);
  }
</script>

<style lang="scss" scoped>

</style>
