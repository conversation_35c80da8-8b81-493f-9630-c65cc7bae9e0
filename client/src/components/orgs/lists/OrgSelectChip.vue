<template>
  <div class="_fw row items-center">
    <default-chip v-for="(org, i) in o$.data" :key="`org-${i}`" :model-value="org"
                  :chip-attrs="{ removable: true, iconRemove: 'mdi-close', ...chipAttrs }"
                  :use-atc-store="useAtcStore"
                  @remove="remove(org._id)"></default-chip>
    <q-chip v-if="multiple || !modelValue" v-bind="{label: 'Select Org', iconRight: 'mdi-menu-down', clickable: true, ...selectChipAttrs}">
      <q-menu>
        <div class="w300 mw100 bg-white q-pa-md">
          <org-list v-bind="{ params, multiple, emitValue }" @update:model-value="emitUp"></org-list>
        </div>
      </q-menu>
    </q-chip>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import OrgList from 'components/orgs/lists/OrgList.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {useAtcStore} from 'stores/atc-store';

  const store = useOrgs();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    inputAttrs: Object,
    searchLimit: { default: 5 },
    params: Object,
    modelValue: { required: false },
    emitValue: Boolean,
    multiple: Boolean,
    chipAttrs: Object,
    selectChipAttrs: Object
  })

  const mvIds = computed(() => props.multiple ? props.emitValue ? [...props.modelValue || []] : [...props.modelValue || []].map(a => a._id) : props.modelValue ? [props.modelValue] : [])

  const { h$: o$ } = HFind({
    store,
    // limit: computed(() => mvIds.value?.length || 1),
    params: computed(() => {
      return {
        query: { _id: { $in: mvIds.value || [] } }
      }
    })
  })

  const remove = (id) => {
    if (props.multiple) {
      const idx = props.emitValue ? props.modelValue.indexOf(id) : props.modelValue.map(a => a._id).indexOf(id);
      const list = [...props.modelValue];
      list.splice(idx, 1);
      emit('update:model-value', list);
    } else emit('update:model-value', undefined)
  }
  const emitUp = (val) => {
    emit('update:model-value', val)
  }
</script>

<style lang="scss" scoped>

</style>
