<template>
  <div class="_fw">
    <div class="font-3-4r text-weight-bold cursor-pointer">{{ asgTitle }}</div>
    <div class="row items-center">
      <avatar-row :model-value="asg" path="orgRecords" :use-atc-store="useAtcStore">
      </avatar-row>
    </div>

    <div class="_fw" v-if="mgmt?.length">
      <div class="font-3-4r">Management Orgs</div>
      <avatar-row :model-value="{mgmt}" path="mgmt" :use-atc-store="useAtcStore"></avatar-row>
    </div>

  </div>
</template>

<script setup>
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';

  import {computed, ref, watch} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {_flatten} from 'symbol-syntax-utils';
  import {useAtcStore} from 'stores/atc-store';

  const orgStore = useOrgs();

  const props = defineProps({
    modelValue: Object
  })

  const asg = ref({});

  const asgTitle = computed(() => {
    return (asg.value?.orgRecords || [{ name: 'Affiliated Group' }]).map(a => a.name).join(' - ');
  })

  const mgmtIds = computed(() => {
    return Array.from(new Set(_flatten((asg.value?.orgRecords || []).map(a => a.managementOrgs))))
  })
  const mgmt = ref([]);

  const loading = ref(false);

  const getFullRecords = async (ids) => {
    const query = { _id: { $in: ids } };
    const inStore = orgStore.findInStore({ query });
    const inStoreList = (inStore?.data || [])
    const diff = (ids || []).filter(a => !inStoreList.map(b => String(b._id)).includes(String(a)) && !!a);
    if (!diff?.length) return inStore.data || [];
    else {
      const loaded = await orgStore.find({ query: { _id: { $in: diff } } });
      return [...(loaded?.data || []), ...inStoreList];
    }
  }

  watch(() => props.modelValue, async (nv) => {
    if (nv) {
      asg.value = Object.assign({}, nv);
      loading.value = true;
      asg.value.orgRecords = await getFullRecords(Object.keys(nv.orgs || {}))
      loading.value = false;
    }
  }, { immediate: true })

  watch(mgmtIds, async (nv, ov) => {
    if (nv?.length && nv.length !== ov?.length) {
      mgmt.value = await getFullRecords(nv);
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  table {
    border-collapse: collapse;
    width: 100%;

    tr {
      width: 100%;
    }

    td {
      font-size: .9rem;
      font-weight: 400;
      padding: 5px 20px;
      white-space: nowrap;
    }
  }
</style>
