<template>
  <div class="_fw">


    <div class="font-1r tw-six">Commonly Owned Entities</div>

    <div class="q-mt-sm q-pa-md __dis font-7-8r">
      <div>These organizations have common ownership with {{ primaryOrg?.name }}</div>
      <div class="q-py-sm">Organizations with sufficiently common ownership or control are treated as "control groups"
        meaning they are combined for purposes of FTE count, discrimination, and other plan features.
      </div>
      <div class="q-py-sm">
        It is important to get this right. If you have other commonly controlled entities to these ones <span
          class="text-weight-bold cursor-pointer text-blue-10" @click="addDialog = !addDialog">add them +</span>
      </div>
    </div>
    <div class="font-1r tw-six q-pt-md">Orgs with common ownership</div>
    <q-list separator>
      <default-item
          v-for="(o, i) in items.data"
          :key="`o-${i}`"
          v-bind="{
            modelValue: o,
            sizeIn: avatarSize,
            useAtcStore: useAtcStore
          }"
      ></default-item>
    </q-list>
    <div class="font-1r q-pt-md  text-weight-bold">Control Group Analysis</div>
    <div class="font-7-8r q-pt-xs q-pb-md">This automated analysis is good, but not perfect. Confirm the details and add any missing orgs.
    </div>
    <div v-for="(key, i) in Object.keys(cg || {})" :key="`key-${i}`" class="_fw row __c">
      <control-group-card
          :model-value="cg[key]"
      ></control-group-card>
    </div>
    <q-separator class="q-my-md"></q-separator>

    <asg-form :primary-org="org"></asg-form>

    <common-dialog v-model="addDialog">
      <div class="q-pa-md _fw">
        <org-form
            title="Add Org"
            add-fields="owners"
            owners
            limited
            @update:model-value="addDialog = false"
        ></org-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import DefaultItem from 'src/components/common/avatars/DefaultItem.vue';
  import OrgForm from 'src/components/orgs/forms/OrgForm.vue';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import ControlGroupCard from 'components/orgs/control/cards/ControlGroupCard.vue';
  import AsgForm from 'components/orgs/control/forms/AsgForm.vue';
  import {useAtcStore} from 'stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useOrgs} from 'src/stores/orgs';

  import {controlGroups} from '../utils/control-groups';

  const store = useOrgs();

  const addDialog = ref(false);

  const props = defineProps({
    org: { required: true },
    size: String
  });

  const items = ref({ total: 0, data: [] });

  const { item: primaryOrg } = idGet({
    value: computed(() => props.org),
    store
  });

  const { controls } = controlGroups({
    org: primaryOrg,
    watch: true,
    attribute: true
  })

  const avatarSize = computed(() => {
    const obj = {
      'xs': '20px',
      'sm': '24px',
      'md': '32px',
      'lg': '50px',
      'xl': '60px'
    };
    return obj[props.size || 'md'];
  });


  const cg = computed(() => {
    const obj = Object.assign({}, controls.value?.ctrls || {});
    for(const key in obj){
      const arr = Object.keys(obj[key]?.orgs || {});
      obj[key].orgRecords = store.findInStore({ query: { _id: { $in: arr } } }).data;
    }

    return obj;
  })

  watch(primaryOrg, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      items.value = await store.find({
        query: {
          $limit: 50,
          _id: { $ne: nv._id },
          '_owners': {
            ids: Array.from(new Set([...(nv.owners || []).map(a => a.id).filter(a => !!a), ...(nv.affiliatedOrgs || [])]))
          }
        }
      })
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__dis {
    border-radius: 8px;
    background: #efefef;
  }
  .__c {
    padding: 15px 10px;
    border-radius: 9px;
    box-shadow: 0 0 0 1px rgba(99,99,99,.4);
    margin: 5px 0;
  }
</style>
