<template>
  <div class="_fw">
    <div class="tw-six font-3-4r">Affiliated Service Group</div>

    <q-list separator>
      <q-item-label header>Member Orgs: {{ title }}</q-item-label>


      <default-item
          v-for="(item, i) in Object.keys(form?.orgs || {})"
          :key="`form-${i}`"
          :item-attrs="{ class: '_fw', clickable: false, dense: true }"
          :model-value="orgs[item]"
          :store="store"
          :use-atc-store="useAtcStore">
        <template v-slot:side>
          <remove-button @remove="remove(item)" label="" name="Org"></remove-button>
        </template>
      </default-item>


      <add-item v-if="adding < 1" clickable @click="adding = 1"></add-item>
      <add-item @click="adding = 0" v-else :icon="{ name: 'mdi-minus' }" label="Close"></add-item>
      <q-slide-transition>
        <div v-if="adding > 0" class="q-pa-md">
          <search-and-add-org
              @update:model-value="addOrg"
              @add="createOrg"
          ></search-and-add-org>
        </div>
      </q-slide-transition>
    </q-list>
    <div class="row justify-end q-py-md" v-if="edited">
      <q-btn class="_p_btn" size="sm" icon-right="mdi-content-save" label="Save" no-caps @click="saveAsg"></q-btn>
    </div>

  </div>
</template>

<script setup>
  import SearchAndAddOrg from 'components/orgs/forms/SearchAndAddOrg.vue';
  import AddItem from 'components/common/buttons/AddItem.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import RemoveButton from 'components/common/buttons/RemoveButton.vue';
  import {useAtcStore} from 'stores/atc-store';

  import {computed, ref, watch} from 'vue';

  import {$errNotify} from 'src/utils/global-methods';
  import {useOrgs} from 'stores/orgs';

  const store = useOrgs();

  const emit = defineEmits(['update:model-value', 'remove']);
  const props = defineProps({
    modelValue: Object,
    primaryOrg: Object,
    asgKey: String,
  })

  const adding = ref(0);
  const formFn = (defs) => {
    return {
      orgs: {},
      ...defs
    }
  }
  const form = ref(formFn());
  const orgs = ref({});

  const title = computed(() => {
    let str = '';
    Object.keys(orgs.value).forEach((k, i) => {
      if (i === 0) str += orgs.value[k]?.name || '';
      else str += ` - ${orgs.value[k]?.name || ''}`
    })
    return str || 'No Orgs Added'
  })

  const tab = ref('add');
  const edited = ref(false);

  const addOrg = (val) => {
    if (val) {
      form.value.orgs[val._id] = { type: 'A' };
      orgs.value[val._id] = val;
      edited.value = true;
    }
    adding.value = 0
  }

  const remove = (rmvKey) => {
    const key = Object.keys(form.value?.orgs || {}).join('.');
    emit('remove', key, rmvKey);
  };

  const createOrg = async (val) => {
    const newOrg = await store.create(val)
        .catch(err => $errNotify(`Error adding org: ${err.message}`));
    if (newOrg) addOrg(newOrg);
  }

  const saveAsg = () => {
    const key = Object.keys(form.value?.orgs || {}).join('.');
    emit('update:model-value', form.value, key, props.asgKey);
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) {
      form.value = formFn(nv);
      if (nv.orgRecords) nv.orgRecords.forEach(o => orgs.value[o._id] = o);
      else {
        const inStore = store.findInStore({ query: { _id: { $in: Object.keys(nv.orgs) } } }) || { data: [] }
        inStore.data.forEach(o => orgs.value[o._id] = o)
      }
    }
  }, { immediate: true })

  watch(() => props.primaryOrg, (nv) => {
    if (nv) {
      orgs.value[nv._id] = nv;
      if (!form.value.orgs[nv._id]) form.value.orgs[nv._id] = { type: 'A' }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
