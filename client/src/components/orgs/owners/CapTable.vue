<template>
  <div class="_fw">
    <div class="font-1r q-pb-md row items-center">
      <q-btn no-caps flat @click="addDialog = !addDialog">
        <span class="q-mr-sm" v-if="addDialog">Close</span>
        <span class="q-mr-sm" v-else>Add</span>
        <q-icon color="primary" :name="`mdi-${addDialog ? 'minus' : 'plus'}`"></q-icon>
        <q-tooltip>Add Owner</q-tooltip>
      </q-btn>
    </div>
    <q-slide-transition>
      <div class="q-pb-lg" v-if="addDialog">
        <owner-form save-msg="Save" :org="item" @update:model-value="addOwner"></owner-form>
      </div>
    </q-slide-transition>
    <q-table
        flat
        :columns="columns"
        :rows="ownerList"
        hide-bottom
        hide-pagination
        row-key="id"
    >
      <template v-slot:header="scope">
        <q-th auto-width></q-th>
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>

      <template v-slot:body="scope">
        <q-tr :props="scope">
          <q-td auto-width>
            <q-btn size="sm" dense flat
                   :icon="`${(selected || []).includes(scope.row._id) ? 'mdi-minus' :'mdi-dots-vertical'}`"
                   @click="select(scope.row)"></q-btn>
          </q-td>
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component v-if="col.component" :is="col.component" v-bind="col.attrs(scope.row)"></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
        </q-tr>
        <q-tr v-if="selected[0] === scope.row._id" :props="props">
          <q-td colspan="100%">
<!--            EXPAND OWNER FORM-->
            <owner-form
                save-msg="Save"
                :org="item"
                :model-value="scope.row"
                @update:model-value="addOwner"
            ></owner-form>
          </q-td>
        </q-tr>
      </template>
    </q-table>

  </div>
</template>

<script setup>
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import TdChip from 'src/components/common/tables/TdChip.vue';
  import OwnerForm from 'src/components/orgs/cards/OwnerForm.vue';

  import {computed, ref} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {idGet} from 'src/utils/id-get';

  import {useOrgs} from 'src/stores/orgs';
  const orgStore = useOrgs();

  import {usePpls} from 'stores/ppls';
  const pplsStore = usePpls();

  import {titles} from '../forms/owner-titles';
  import {HFind} from 'src/utils/hFind';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    org: { required: true },
    editing: Boolean,
    path: { type: String, default: 'owners' }
  });

  const selected = ref([]);
  const addDialog = ref(false);

  const select = (owner) => {
    const idx = (selected.value || []).indexOf(owner._id);
    if (idx > -1) selected.value.splice(idx, 1);
    else selected.value.push(owner._id);
  }

  const columns = computed(() => {
    return [
      {
        label: 'Owner',
        name: 'owner',
        component: DefaultChip,
        sortable: false,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white'
            },
            modelValue: row,
            backupNamePath: 'email',
            defaultName: row.email
          }
        }
      },
      {
        label: 'Type',
        name: 'type',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: row.idService === 'ppls' ? 'Individual' : 'Entity'
            }
          }
        }
      },
      {
        label: 'Title',
        name: 'title',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: _get(titles, [row.position, 'label']) || ''
            }
          }
        }
      },
      {
        label: 'Share',
        name: 'share',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              class: 'text-weight-bold alt-font',
              label: `${row.percent || 0}%`
            }
          }
        }
      }
    ].map(a => {
      return {
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name,
        ...a
      };
    })
  })

  const { item } = idGet({
    value: computed(() => props.org),
    store: orgStore,
  });

  const pplOwners = computed(() => (item.value?.owners || []).filter(a => a.idService === 'ppls'))
  const orgOwners = computed(() => (item.value?.owners || []).filter(a => a.idService === 'orgs'))

  const { h$:pplOwnerData } = HFind({
    store: pplsStore,
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          _id: { $in: pplOwners.value.map(a => a.id)}
        }
      }
    })
  })

  const { h$:orgOwnerData } = HFind({
    store: orgStore,
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          _id: { $in: orgOwners.value.map(a => a.id) }
        }
      }
    })
  })

  const ownerList = computed(() => {
    // return item.value?.owners;
    const ppls = (pplOwnerData.data || []).map(a => {
      return {
        ...a,
        percent: pplOwners.value.filter(o => o.id === a._id)[0]?.percent || 0
      }
    });
    const orgs = orgOwnerData.data.map(a => {
      return {
        ...a,
        percent: orgOwners.value.filter(o => o.id === a._id)[0]?.percent || 0
      }
    });
    const all = [...ppls, ...orgs];
    return _get(item.value, props.path, []).map(a => {
      const data = all.filter(b => b._id === a.id)[0] || undefined
      return { ...data, ...a };
    }).sort((a, b) => b.percent - a.percent);
  });

  const setForm = (owner, key, val) => {
    const idx = ownerList.value.map(a => a.id).indexOf(owner.id);
    const list = ownerList.value.slice();
    list[idx][key] = val;
    emit('update:model-value', list.map(a => {
      return {
        id: a.id,
        percent: a.percent,
        attribute: a.attribute,
        did: a.did,
        position: a.position
      }
    }));
  }

  const addOwner = (val) => {
    const list = [...item.value?.owners || []];
    const idx = list.map(a => a.id).indexOf(val.id);
    if (idx > -1) {
      list.splice(idx, 1, val);
      emit('update:model-value', list)
    } else emit('update:model-value', [...list, val])
    select(val);
  }
</script>

<style lang="scss" scoped>

</style>
