<template>
  <div class="_fw">
    <q-input
        v-model="search.text"
        v-bind="{
          placeholder: 'Search orgs...',
          ...$attrs
        }"
    >
      <template v-slot:prepend>
        <slot name="prepend">
          <q-icon name="mdi-magnify"></q-icon>
        </slot>
      </template>
    </q-input>
    <q-slide-transition>
      <div class="_fw q-py-md" v-if="search.text">
        <q-list separator>
          <add-item @click="addOrg" :label="`Add new org '${search.text}'`"></add-item>
          <q-item v-for="(item, i) in h$.data" :key="`item-${i}`" clickable @click="$emit('update:model-value', item)">
            <q-item-section avatar>
              <default-avatar :model-value="item" :use-atc-store="useAtcStore"></default-avatar>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{item.name}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>

  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {computed} from 'vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import {useOrgs} from 'stores/orgs';
  import AddItem from 'components/common/buttons/AddItem.vue';
  import {useAtcStore} from 'stores/atc-store';

  const store = useOrgs();

  const emit = defineEmits(['update:model-value', 'add'])
  const props = defineProps({
    query: Object
  })

  const { search, searchQ } = HQuery({});

  const addOrg = () => {
    if(search.value.text?.length > 1) emit('add', { name: search.value.text })
  }

  const { h$ } = HFind({
    store,
    params: computed(() => {
      return {
        query: {
          ...props.query,
          ...searchQ.value
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
