<template>
  <div class="_form_label">EIN</div>
  <div class="q-pa-sm">
    <!--            EIN-->
    <ssn-input
        @update:model-value="autoSave('ein')" :label="undefined" ein placeholder="Tax ID"
        v-model="form.ein"></ssn-input>
  </div>
  <!--      LEGAL STRUCTURE-->
  <div class="_form_label">Legal<br>Structure</div>
  <div class="q-pa-sm">
    <org-structure-picker
        label="Entity Structure"
        v-model="form.structure"
        @update:model-value="autoSave('structure')"
    >
    </org-structure-picker>
    <q-slide-transition>
      <div class="_fw" v-if="form.structure && allStructures[form.structure]?.checkPublic">
        <q-checkbox  :model-value="!!form.public" @update:model-value="setForm('public', $event)" label="Publicly Traded"></q-checkbox>
      </div>
    </q-slide-transition>
    <q-slide-transition>
      <div class="_fw" v-if="form.structure && allStructures[form.structure]?.memberCount">
        <q-checkbox :model-value="!!form.singleMember" @update:model-value="setForm('singleMember', $event)" label="Single Member"></q-checkbox>
      </div>
    </q-slide-transition>
  </div>

  <!--        TAXED AS-->
  <template v-if="form.structure">
    <div class="_form_label">Taxed As</div>
    <div class="q-pa-sm">
      <org-structure-picker
          filled
          label="Taxed As"
          v-model="form.taxStructure"
          tax
          @update:model-value="autoSave('taxStructure')"
      >
      </org-structure-picker>

    </div>
  </template>

  <div class="_form_label">Website or Description</div>
  <div class="q-pa-sm">
    <q-input autogrow v-model="form.website" placeholder="https://yourwebsite.com"
             @update:model-value="autoSave('website')"></q-input>
  </div>

  <div class="_form_label">Locations</div>
  <div class="q-pa-sm">
    <multi-address @update:model-value="autoSave('addresses')" @update:primary="autoSave('address')"
                   v-model:primary="form.address" v-model="form.addresses"></multi-address>
  </div>

</template>

<script setup>
  import OrgStructurePicker from 'components/orgs/forms/OrgStructurePicker.vue';
  import SsnInput from 'components/common/input/SsnInput.vue';
  import MultiAddress from 'components/common/address/tomtom/MultiAddress.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useOrgs} from 'src/stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref} from 'vue';
  import {allStructures} from 'components/orgs/forms/org-structures';


  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value', 'close']);
  const props = defineProps({
    modelValue: Object
  });

  const { item: org } = idGet({
    value: computed(() => props.modelValue),
    store: orgStore
  });

  const { form } = HForm({
    store: orgStore,
    value: org,
    addToStore: true,
    errWatch: true,
    validate: true,
    afterFn: (val) => {
      emit('update:model-value', val);
      emit('close');
    },
    vOpts: ref({
      'name': { name: 'name', v: ['notEmpty'] },
      // 'email': { name: 'email', v: ['notEmpty', 'email']}
    })
  });

  const { autoSave, setForm, setBeforeUnmount } = HSave({
    form,
    store: orgStore,
    pause: computed(() => !form.value?._id)
  });

  onMounted(() => {
    setBeforeUnmount();
  })
</script>

<style scoped>
</style>
