<template>
  <div class="_fw q-py-sm">
    <div class="row items-center">
      <q-chip class="tw-six" dense square clickable @click="tab = 'base'" label="Choose Method" color="transparent">
      </q-chip>
      <template v-if="tabs[tab]">
        <q-icon name="mdi-chevron-right" size="18px" color="primary"></q-icon>
        <q-chip color="transparent" square dense :label="tabs[tab]"></q-chip>
      </template>
    </div>
    <q-tab-panels class="_panel" v-model="tab" animated transition-next="jump-up" transition-prev="jump-down">
      <q-tab-panel class="_panel" name="base">

        <q-list separator>
          <q-item v-if="ca?._id" :clickable="!!canUseCareAccount" @click="tab = 'ca'">
            <q-item-section avatar>
              <q-img class="w30 h30" fit="contain" :src="logo"></q-img>
            </q-item-section>
            <q-item-section>
              <q-item-label :class="`tw-six text-${canUseCareAccount ? 'grey-10' : 'grey-6'}`">Plan Wallet
              </q-item-label>
              <q-item-label caption v-if="!canUseCareAccount">Plan Wallet transfers <b>NOT</b> enabled for
                {{ provider?.name || 'this provider' }}
              </q-item-label>
              <q-item-label caption v-else>Transfer via your CareAccount</q-item-label>

            </q-item-section>
          </q-item>
          <q-item :clickable="canUseCards" @click="tab = 'cc'">
            <q-item-section avatar>
              <q-icon name="mdi-credit-card" color="a3"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label :class="`tw-six text-${canUseCards ? 'grey-10' : 'grey-6'}`">Credit Card</q-item-label>
              <q-item-label caption v-if="!canUseCards">Card payments <b>NOT</b> enabled for
                {{ provider?.name || 'this provider' }}
              </q-item-label>
              <q-item-label caption v-else>Pay with a credit card</q-item-label>

            </q-item-section>
          </q-item>
          <q-item :clickable="!!canUseAch" @click="tab = 'ach'">
            <q-item-section avatar>
              <q-icon name="mdi-bank" color="s3"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label :class="`tw-six text-${canUseAch ? 'grey-10' : 'grey-6'}`">Bank Transfer</q-item-label>
              <q-item-label caption v-if="!canUseAch">ACH payments <b>NOT</b> enabled for
                {{ provider?.name || 'this provider' }}
              </q-item-label>
              <q-item-label caption v-else>Transfer from a bank account</q-item-label>

            </q-item-section>
          </q-item>
          <q-item clickable @click="tab = 'record'">
            <q-item-section avatar>
              <q-icon name="mdi-receipt" color="grey-5"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label class="tw-six">Record Payment</q-item-label>
              <q-item-label caption>Record payment made away from CommonCare</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <div v-if="!canUseCards && !canUseCareAccount && !canUseAch" class="q-pa-md text-italic font-7-8r">
          This provider isn't taking payments on CommonCare yet
        </div>

      </q-tab-panel>

      <q-tab-panel class="_panel" name="ca">

        <div class="row items-center q-py-xs">
          <q-img class="w20 h20 q-mr-xs" fit="contain" :src="logo"></q-img>
          <org-chip :model-value="org"></org-chip>
          <div class="font-1-2r text-grey-7 tw-six q-mx-sm">TO</div>
          <provider-chip :model-value="provider"></provider-chip>
        </div>
      </q-tab-panel>

      <q-tab-panel class="_panel" name="cc">

      </q-tab-panel>
      <q-tab-panel class="_panel" name="ach"></q-tab-panel>
    </q-tab-panels>

    <q-slide-transition>
      <div v-if="tab !== 'base'">
        <table>
          <tbody>
          <tr>
            <td>Balance (from provider)</td>
            <td>{{dollarString(balance/100, '$', 2)}}</td>
          </tr>
          <tr>
            <td>Approved (by plan)</td>
            <td>{{dollarString(payToday/100, '$', 2)}}</td>
          </tr>
          <tr>
            <td>Plan Share</td>
            <td>{{dollarString(planShare/100, '$', 2)}}</td>
          </tr>
          <tr>
            <td>Deductible Share</td>
            <td>{{dollarString(participantShare.ded/100, '$', 2)}}</td>
          </tr>
          <tr>
            <td>Coinsurance Share</td>
            <td>{{dollarString(participantShare.coins/100, '$', 2)}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import logo from 'src/assets/common_cent_p3.svg'
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useClaimPayments} from 'stores/claimPayments';
  import {useProviders} from 'stores/providers';
  import {useCareAccounts} from 'stores/care-accounts';
  import {usePlans} from 'stores/plans';
  import {SessionStorage} from 'symbol-auth-client';
  import {useClaims} from 'stores/claims';
  import {getPayToday} from 'components/claim-payments/utils/pay-claims';
  import {dollarString} from 'src/utils/global-methods';

  const orgStore = useOrgs();
  const cpStore = useClaimPayments();
  const pStore = useProviders();
  const caStore = useCareAccounts();
  const planStore = usePlans();
  const claimStore = useClaims();

  const emit = defineEmits(['update:ca', 'update:card', 'update:ach']);
  const props = defineProps({
    claim: { required: false },
    visit: { required: false },
    payer: { required: true },
    enrollmentId: { required: true },
    coverageId: { required: true },
    byId: Object,
    payToday:Number,
    balance: Number,
    splits: {
      default: () => {
        return {}
      }
    },
    discounts: {
      default: () => {
        return {}
      }
    },
  })

  const tab = ref('base');
  const tabs = {
    'ca': 'Plan Wallet',
    'cc': 'Credit Card',
    'ach': 'Bank Transfer'
  }

  const participantShare = computed(() => {
    const total = (props.splits?.person || 0) - (props.discounts?.total || 0);
    const ded = (props.splits?.ded || 0) - (props.discounts?.ded_discount || 0);
    const coins = (props.splits?.coins || 0) - (props.discounts?.coins_discount || 0);
    const oop = ded + coins;
    return {
      total,
      ded,
      coins,
      oop
    }
  })

  const planShare = computed(() => {
    return (props.splits?.plan || 0) + (props.discounts?.total || 0);
  })

  const fullVisit = computed(() => props.visit || {})

  const { item: fullClaim } = idGet({
    store: claimStore,
    value: computed(() => props.claimPayment)
  })


  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => fullClaim.value?.plan || fullVisit.value?.plan || {}),
    params: ref({ runJoin: { plan_org: true } })
  })

  const org = computed(() => plan.value?._fastjoin?.org)

  const { item: provider } = idGet({
    store: pStore,
    value: computed(() => fullClaim.value?.provider || fullVisit.value.provider),
    params: ref({ runJoin: { provider_avatar: true } }),
    refreshOn: (val) => !val || (val.avatar && !val._fastjoin?.files)
  })

  const { item: providerOrg } = idGet({
    store: orgStore,
    value: computed(() => provider.value?.org)
  })

  const { item: pca } = idGet({
    store: caStore,
    value: computed(() => (providerOrg.value?.careAccounts || [])[0])
  })
  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => (org.value?.careAccounts || [])[0])
  })

  const canUseCards = computed(() => provider.value?.payment_settings?.card_payments === 'active')
  const canUseAch = computed(() => provider.value?.payment_settings?.ach_payments?.length)
  const canUseCareAccount = computed(() => ca.value?.status === 'open' && pca.value?.status === 'open')

  const payClaim = async (data, config) => {
    const createCp = (clm) => {
      return {
        claim: clm,
        visit: clm.visit,
        person: clm.person,
        plan: clm.plan,
        org: clm.org,
        patient: clm.patient,
        provider: clm.provider,
        coverage: props.coverageId,
        enrollment: props.enrollmentId,
        preventive: clm.preventive
      }
    }
    const ids = Object.keys(props.byId);
    const query = { _id: { $in: ids }, $limit: ids.length }
    let claims = claimStore.findInStore({ query });
    if(claims.total !== ids.length) claims = await claimStore.find({ query })
    await cpStore.create(claims.data.map(a => createCp(a)), {
      banking: {
        pay_claims: {
          data,
          moov_id: org.value.treasury.id,
          payer: props.payer, ...config
        }
      }
    })
  }

  const payToday = computed(() => getPayToday(props.byId))

  const sendCa = async () => {
    const data = {
      amount: payToday.value,
      financial_account: ca.value.stripe_id,
      destination_payment_method_data: {
        type: 'financial_account',
        financial_account: pca.value.stripe_id
      },
      end_user_details: {
        present: true,
        ip_address: SessionStorage.getItem('sessionPrint').ipInfo.ip
      },
      statement_descriptor: `${plan.value.name} to ${provider.value.name} | ${fullVisit.value ? `Visit ID ${fullVisit.value._id}` : `Payment ID ${cp.value._id}`}`,
    }
    await payClaim(data, { method: 'ca' })
  }


</script>

<style lang="scss" scoped>
 table {
   width: 100%;
   border-collapse: collapse;
   tr {
     td {
       padding: 8px 8px;
       border-bottom: solid .3px #999;
       font-size: .9rem;
     }
   }
 }
</style>
