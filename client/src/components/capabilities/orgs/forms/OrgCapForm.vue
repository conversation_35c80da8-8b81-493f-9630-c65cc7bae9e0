<template>
  <div class="_fw">
    <template v-if="canEdit.ok">
      <div class="_f_l _f_chip">
        {{ path ? 'Edit' : 'Add' }}
      </div>
      <div class="_form_grid _f_g_r">
        <div class="_form_label">Set Name</div>
        <div class="q-pa-sm">
          <q-input dense filled v-model="newKey" @blur="changeKey"></q-input>
        </div>
        <div class="_form_label">Description</div>
        <div class="q-pa-sm">
          <q-input dense filled v-model="description" @blur="setDescription"></q-input>
        </div>
      </div>

      <div class="_f_l _f_chip">Permissions</div>
      <div class="_form_grid _f_g_r">
        <div class="_form_label">Select Roles</div>
        <q-list separator>
          <q-item v-for="(k, i) in Object.keys(capabilities)" :key="`permission-${i}`" clickable
                  @click="toggleCap(k)">
            <q-item-section>
              <q-item-label>
                {{ capabilities[k].label }}
              </q-item-label>
              <q-item-label caption>
                {{ capabilities[k].description }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-icon :color="activeCaps.includes(k) ? 'green' : 'grey-3'" name="mdi-checkbox-marked"></q-icon>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
      <q-slide-transition>
        <div v-if="attModified" class="row justify-end q-py-md">
          <q-btn color="primary" no-caps glossy label="Save Permissions" icon-right="mdi-content-save"
                 @click="saveChanges"></q-btn>
        </div>
      </q-slide-transition>
      <template v-if="path">
        <div class="_f_l _f_chip">People</div>
        <div class="_fw q-pa-md">

          <div class="row q-py-ams">
            <q-tabs dense v-model="pplTab" align="left" no-caps indicator-color="primary">
              <q-tab name="list">
                <span class="font-3-4r">Current</span>
              </q-tab>
              <q-tab name="add">
                <span class="font-3-4r">Add</span>
              </q-tab>
            </q-tabs>
          </div>

          <q-tab-panels @update:model-value="pplTab = $event;search.text = ''" :model-value="loginIds.length ? pplTab : 'add'" animated
                        class="_panel">
            <q-tab-panel name="list" class="_panel">
              <q-input dense v-model="search.text" filled>
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
              <q-list separator>
                <q-item v-for="(prsn, i) in a$.data" :key="`prsn-${i}`">
                  <q-item-section>
                    <q-item-label>{{ prsn.name }}</q-item-label>
                    <q-item-label caption>{{ prsn.email }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <remove-proxy-btn
                        icon="mdi-close"
                        size="sm"
                        :name="prsn.firstName"
                        @remove="remove(prsn)"
                    ></remove-proxy-btn>
                  </q-item-section>
                </q-item>
              </q-list>
              <pagination-row
                  v-bind="{
                    pAttrs: { size: 'sm' },
                    h$:a$,
                    pagination,
                    pageRecordCount,
                    limit
                  }"
              ></pagination-row>
            </q-tab-panel>
            <q-tab-panel name="add" class="_panel">
              <q-input dense v-model="search.text" filled>
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>

              <div class="q-py-sm row justify-end">
                <q-chip color="transparent">
                  <span class="q-mr-sm">Has logged in</span>
                  <q-icon name="mdi-checkbox-marked" color="primary"></q-icon>
                </q-chip>
              </div>
              <q-list separator>
                <q-item v-for="(psn, i) in p$.data.map(a => hasInvite(a))" :key="`psn-${i}`" :clickable="!psn.hasInvite" @click="add(psn)">
                  <q-tooltip v-if="!psn.email" class="font-7-8r tw-five">No email added</q-tooltip>
                  <q-item-section>
                    <q-item-label>{{ psn.name }}</q-item-label>
                    <q-item-label caption>{{ psn.email }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn dense flat v-if="psn.hasInvite" icon="mdi-timer-sand" color="accent">
                      <q-popup-proxy>
                        <div class="w400 mw100 q-pa-md">
                          <q-list separator>
                            <q-item-label header>Invited {{$ago(psn.hasInvite)}}</q-item-label>
                            <q-item :clickable="!reminderSent" @click="remind(psn)">
                              <q-item-section>
                                <q-item-label>Send Reminder</q-item-label>
                              </q-item-section>
                              <q-item-section side>
                                <q-icon v-if="!reminderSent" color="primary" name="mdi-send"></q-icon>
                                <q-icon v-else color="primary" name="mdi-check-circle"></q-icon>
                              </q-item-section>
                            </q-item>
                            <q-item clickable @click="copyInviteLink(psn)">
                              <q-item-section>
                                <q-item-label>Copy Link</q-item-label>
                              </q-item-section>
                              <q-item-section side>
                                <q-icon color="accent" name="mdi-content-copy"></q-icon>
                              </q-item-section>
                            </q-item>
                            <q-item clickable>
                              <q-item-section>
                                <q-item-label>Remove Invite</q-item-label>
                              </q-item-section>
                              <q-item-section side>
                                <q-icon name="mdi-delete" color="red"></q-icon>
                              </q-item-section>
                              <remove-proxy remove-label="Remove Invite?" @remove="removeInvite(psn)"></remove-proxy>
                            </q-item>
                          </q-list>
                        </div>
                      </q-popup-proxy>
                    </q-btn>
                    <q-icon v-else-if="!psn.login" name="mdi-checkbox-blank-outline" color="ir-light">
                      <q-tooltip class="mw500 font-7-8r tw-five">Once {{ psn.name }} logs in, they will be added
                        automatically assuming you still have permission to add them at that time.
                      </q-tooltip>
                    </q-icon>
                    <q-icon v-else name="mdi-check" color="primary"></q-icon>
                  </q-item-section>
                </q-item>
              </q-list>
              <pagination-row
                  v-bind="{
                pAttrs: { size: 'sm' },
                limit,
                h$:p$,
                pageRecordCount:sRecordCount,
                pagination:sPage
                  }"
              ></pagination-row>
            </q-tab-panel>
          </q-tab-panels>


        </div>
      </template>
      <q-slide-transition>
        <div v-if="loginModified" class="row justify-end q-py-md">
          <q-btn color="primary" no-caps glossy label="Save People" icon-right="mdi-content-save"
                 @click="saveChanges"></q-btn>
        </div>
      </q-slide-transition>

      <template v-if="cap._id">
        <div class="_f_l _f_chip">Remove</div>
        <div class="_fw q-pa-md">
          <div class="_form_grid">
            <div class="_form_label">Remove Permission Set</div>
            <div class="q-pa-sm">
              <remove-proxy-btn name="Permission Set" @remove="removeCap"></remove-proxy-btn>
            </div>
          </div>
        </div>
      </template>
    </template>
    <template v-else>
      <div class="q-pa-md text-italic">You don't have permission to edit this set</div>
    </template>
  </div>
</template>

<script setup>
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  import {computed, ref, watch} from 'vue';
  import {$ago, $capitalizeFirstLetter, $copyTextToClipboard, $errNotify} from 'src/utils/global-methods';
  import {useCaps} from 'stores/caps';
  import {parseUcan} from 'src/utils/ucans';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {usePpls} from 'stores/ppls';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  import {usePings} from 'stores/pings';
  import {useRouter} from 'vue-router';

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const { login, person } = loginPerson()

  const capStore = useCaps();
  const orgStore = useOrgs();
  const pplStore = usePpls();
  const pingStore = usePings();
  const router = useRouter();

  const emit = defineEmits(['update:path'])
  const props = defineProps({
    modelValue: { required: true },
    path: { type: String, required: false },
    caps: { type: Object, required: true },
    need: { required: true }
  })

  const pplTab = ref('list')

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const cap = computed(() => props.modelValue)

  const removeCap = () => {
    capStore.remove(cap.value._id, { disableSoftDelete: true })
        .catch(err => $errNotify(`Error removing capability: ${err.message}`))
  }
  const description = ref('');
  const newKey = ref('');
  const changeKey = async () => {
    if (newKey.value?.length) {
      const nk = newKey.value.split(' ').join('_').toLowerCase()
      if (!props.path && !(cap.value.caps || {})[nk]) {
        await capStore.patch(cap.value._id, { $set: { [`caps.${nk}`]: { description: description.value } } })
        emit('update:path', newKey.value)
      } else if (nk !== props.path) {
        if (!cap.value.caps[nk]) {
          const patchObj = {
            $unset: { [`caps.${props.path}`]: '' },
            $set: { [`caps.${newKey.value}`]: cap.value.caps[props.path] }
          }
          await capStore.patch(cap.value._id, patchObj)
        } else $errNotify('Permissions group name already exists')
      }
    }
  }

  const setDescription = async () => {
    if (description.value !== cap.value.caps[props.path].description) await capStore.patch(cap.value._id, { $set: { [`caps.${props.path}.description`]: description.value } })
  }

  watch(() => props.path, (nv) => {
    if (nv) {
      newKey.value = nv.split('_').map(a => $capitalizeFirstLetter(a)).join(' ');
      description.value = cap.value?.caps[nv].description || '';
    }
  }, { immediate: true });

  const capabilities = computed(() => {
    return props.caps
  })

  const activeUcan = computed(() => (cap.value?.caps || {})[props.path]?.ucan);
  const parsedUcan = ref({})

  const newAtt = ref([]);
  const attModified = ref(false);
  const activeCaps = ref([]);

  //TODO: this is kind of a hacky custom validator. It would be better to build and verify the ucan officially. Pass it to the server, generate it properly, pass it back
  const includesCap = (k) => {
    const cp = capabilities.value[k].cap;
    const cps = Array.isArray(cp) ? cp : [cp]
    let inc = false;
    for (const cap of cps) {
      const att = [...newAtt.value || []]

      for (let i = att.length - 1; i >= 0; i--) {
        const { with: w, can } = att[i];
        if (JSON.stringify(w) === JSON.stringify(cap.with)) {
          if (can.namespace === cap.can.namespace) {
            let inc2 = true;
            //if any of the segments not present, no match
            for (let j = 0; j < cap.can.segments?.length; j++) {
              if (!can.segments.includes(cap.can.segments[j])) {
                inc2 = false;
                break;
              }
            }
            inc = inc2;
          }
        }
        // newAtt.value = att;
      }
    }
    return inc
  }

  const toggleCap = (k) => {
    const cp = capabilities.value[k].cap;
    const cap = Array.isArray(cp) ? cp : [cp]
    const adding = !activeCaps.value.includes(k);
    const att = [...newAtt.value || []]
    if (!att.length) {
      newAtt.value = [...cap];
      attModified.value = true;
      activeCaps.value.push(k)
    } else {
      for (let i = att.length - 1; i >= 0; i--) {
        const { with: w, can } = att[i];
        let capIdxAdded = [];
        for (let idx = 0; idx < cap.length; idx++) {
          if (!capIdxAdded.includes(idx)) {
            const c = cap[idx];
            if (JSON.stringify(w) === JSON.stringify(c.with)) {
              let inc2 = true;
              if (can.namespace === c.can.namespace) {
                for (let j = 0; j < c.can.segments?.length; j++) {
                  if (!can.segments.includes(c.can.segments[j])) {
                    inc2 = false;
                    break;
                  }
                }
              }
              if (inc2 && !adding) {
                att.splice(i, 1);
                activeCaps.value.splice(activeCaps.value.indexOf(k), 1);
                attModified.value = true;
              } else if (adding) {
                capIdxAdded.push(idx);
                att.push(c);
                if (activeCaps.value.indexOf(k) === -1) activeCaps.value.push(k);
                attModified.value = true;
              }
            }
          }
        }
      }
      newAtt.value = att;
    }
  }

  watch(activeUcan, (nv, ov) => {
    if (nv) {
      if (nv !== ov) {
        parsedUcan.value = parseUcan(nv);
        newAtt.value = parsedUcan.value?.payload?.att || []
        for (const k in capabilities.value) {
          if (includesCap(k)) {
            activeCaps.value.push(k)
          }
        }
      }
    } else {
      activeCaps.value = [];
      parsedUcan.value = {}
    }
  }, { immediate: true });

  const { canEdit, reset } = clientCanU({
    subject: cap,
    or: true,
    log: false,
    caps: computed(() => [...props.need]),
    cap_subjects: computed(() => [org.value._id]),
    login
  })

  watch(org, (nv) => {
    if (nv && !canEdit.value.ok) reset()
  }, { immediate: true })

  const limit = ref(10);
  const { search, searchQ } = HQuery({})
  const loginIds = computed(() => (cap.value?.caps || {})[props.path]?.logins || [])
  const { h$: a$, pagination, pageRecordCount } = HFind({
    store: pplStore,
    limit,
    params: computed(() => {
      return {
        _search: { people_search: true },
        query: {
          ...searchQ.value,
          login: { $in: loginIds.value }
        }
      }
    })
  })

  const { h$: p$, pagination: sPage, pageRecordCount: sRecordCount } = HFind({
    store: pplStore,
    limit,
    params: computed(() => {
      return {
        query: {
          login: { $nin: loginIds.value },
          inOrgs: { $in: [getOrgId.value] },
          ...searchQ.value
        }
      }
    })
  })

  const remind = async (p, skip) => {
    reminderSent.value = true;
    if(!skip && (p.invites || {})[org.value._id]?.reminded?.length){
      if(new Date().getTime() - new Date(p.invites[org.value._id].reminded[0]).getTime() < 1000 * 60 * 10){
        return $errNotify('You can only send a reminder every 10 minutes')
      }
    }

    const { href } = router.resolve({ name: 'org-invite', params: { personId: p._id, orgId: org.value._id } });

    await pingStore.create({
      recipient: p._id,
      recipientService: 'ppls',
      subject: org.value._id,
      subjectService: 'orgs',
      priority: 5,
      nouns: [person.value.name],
      verbs: ['invited you to join'],
      category: 'Invites',
      message: `You have been invited to join ${org.value.dba || org.value.name} on CommonCare`,
      link: `${window.location.hostname}${href}`,
      methods: {
        email: { send: true },
        internal: { send: true }
      }
    }, { runJoin: { log: { service: 'ppls', subject: p._id, data: { $addToSet: { [`invites.${org.value._id}.reminded`]: { value: new Date(), $position: 0, $slice: 10 } }} }}})
        .catch(err => {
          $errNotify(`Error sending reminder: ${err.message}`)
          reminderSent.value = false;
        })

  }

  const loginModified = ref(false);
  const loginChanges = ref({})
  const remove = (p) => {
    const k = `caps.${props.path}.logins`
    loginChanges.value.$pull = { [k]: [...(loginChanges.value.$pull || {})[k] || [], p.login] }
    loginModified.value = true;
  }
  const add = (p) => {
    if (p.login) {
      const k = `caps.${props.path}.logins`
      loginChanges.value.$addToSet = { [k]: { $each: [...(loginChanges.value.$addToSet || {})[k]?.$each || [], p.login] } }
      loginModified.value = true;
    } else {
      const invites = p.invites || {}
      pplStore.patch(p._id, {
        $set: {
          [`invites.${org.value._id}`]: {
            ...invites[cap.value._id],
            by: login.value?._id,
            at: new Date(),
            caps: { ...invites[cap.value._id]?.caps, [props.path]: { id: cap.value._id, path: props.path } }
          }
        }
      }, { special_change: '*' })
      remind(p, true)
    }
  }
  const hasInvite = (p) => {
    if(!p.invites) return p;
    if(!p.invites[org.value._id]) return p;
    if(!p.invites[org.value._id].caps) return p;
    if(p.invites[org.value._id].caps[props.path]) return { ...p, hasInvite: p.invites[org.value._id].at };
    return p;
  }

  const saveChanges = async () => {
    let patchObj = {};
    if (attModified.value) patchObj.$set = { [`caps.${props.path}.ucan`]: JSON.stringify(newAtt.value) }
    if (loginModified.value) patchObj = { ...patchObj, ...loginChanges.value }
    await capStore.patch(cap.value._id, patchObj)
    attModified.value = false;
    loginModified.value = false;
  }

  const reminderSent = ref(false);

  const copyInviteLink = (p) => {
    const { href } = router.resolve({ name: 'org-invite', params: { personId: p._id, orgId: org.value._id } });
    $copyTextToClipboard(`${window.location.hostname}${href}`, 'Link Copied')
  }

  const removeInvite = (p) => {
    pplStore.patch(p._id, { $unset: { [`invites.${org.value._id}`]: '' } }, { special_change: '*' })
        .catch(err => $errNotify(`Error removing invite: ${err.message}`))
  }

</script>

<style lang="scss" scoped>

</style>
