<template>
  <div class="_fw">
    <div class="_form_grid _f_g_r" v-if="Object.keys(form || {}).includes('treasuryData')">
      <div class="_form_label">Classification</div>
      <div class="q-pa-sm">
        <q-radio :model-value="form.treasuryData.complaint_classification" val="operational" label="Operations" @update:modelValue="handleSave('complaint_classification', $event)"></q-radio>
        <q-radio :model-value="form.treasuryData.complaint_classification" val="executive"
                 label="Executive (Legal)" @update:modelValue="handleSave('complaint_classification', $event)"></q-radio>
      </div>
      <div class="_form_label">Category</div>
      <div class="q-pa-sm">
        <q-radio v-for="item in ['Privacy or Security', 'Legal or Regulatory', 'Product or Service']" :key="item"
                 :model-value="form.treasuryData.complaint_category" :val="item" :label="item" @update:modelValue="handleSave('complaint_category', $event)"></q-radio>
      </div>
      <div class="_form_label">Sub Category</div>
      <div class="q-pa-sm">
        <q-select :model-value="form.treasuryData.complaint_sub_category" @update:model-value="handleSave('complaint_sub_category', $event)" :options="['Issuing', 'Transfers', 'Payments', 'Accounts', 'Yield']" placeholder="Enter Sub Category"></q-select>
      </div>

      <div class="_form_label">Description</div>
      <div class="q-pa-sm">
        <q-input filled autogrow :model-value="form.treasuryData.description" @update:model-value="handleSave('description', $event)" placeholder="Describe the issue..."></q-input>
      </div>

      <div class="_form_label">Unfair or Deceptive Practices Alleged</div>
      <div class="q-pa-sm">
        <q-checkbox @update:model-value="handleSave('alleges_UDAP_or_discrimination', '$event')" :model-value="form.treasuryData.alleges_UDAP_or_discrimination"></q-checkbox>
      </div>

      <div class="_form_label">User Stage</div>
      <div class="q-pa-sm">
        <q-radio v-for="item in  ['prospect', 'onboarding', 'active']" :key="`stage-${item}`"
                 :model-value="form.treasuryData.user_stage" @update:model-value="handleSave('user_stage', $event)" :val="item" :label="$capitalizeFirstLetter(item)"></q-radio>

      </div>
      <div class="_form_label">Issue is systemic</div>
      <div class="q-pa-sm">
        <q-checkbox @update:model-value="handleSave('systemic_issue_identified', $event)" :model-value="form.treasuryData.systemic_issue_identified"></q-checkbox>
      </div>
      <div class="_form_label">Redress Required</div>
      <div class="q-pa-sm">
        <q-checkbox :model-value="form.treasuryData.redress_reqd" @update:model-value="handleSave('redress_reqd', $event)"></q-checkbox>
      </div>
      <div class="_form_label">Description of Correction</div>
      <div class="q-pa-sm">
        <q-input autogrow filled placeholder="Describe Corrective Action..." :model-value="form.treasuryData.description_of_corrective_action" @update:model-value="handleSave('description_of_corrective_action', $event)"></q-input>
      </div>
      <template v-if="exceededFifteen">
      <div class="_form_label">Reason Exceeded 15 Days</div>
      <div class="q-pa-sm">
        <q-input autogrow filled placeholder="Type Reason..." :model-value="form.treasuryData.reason_exceeded_15_days" @update:model-value="handleSave('reason_exceeded_15_days', $event)"></q-input>
      </div>
      </template>
    </div>
  </div>
</template>

<script setup>

  import {HForm} from 'src/utils/hForm';
  import {useIssues} from 'stores/issues';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import { dateDiff } from 'src/utils/date-utils';

  const store = useIssues();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: issue } = idGet({
    store,
    value: computed(() => props.modelValue)
  })

  const { form, save } = HForm({
    value: issue,
    formFn: (defs) => {
      return {
        ...defs,
        treasuryData: {
          ...defs?.treasuryData,
        }
      }
    },
    store
  })

  const exceededFifteen = computed(() => {
    if(!issue.value?.resolvedAt) return false;
    return Math.abs(dateDiff(issue.value.createdAt, issue.value.resolvedAt, 'days')) > 15
  })

  const to = ref()
  const $set = ref({})
  const handleSave = (path, val) => {
    const treasuryData = form.value.treasuryData || {};
    treasuryData[path] = val;
    form.value.treasuryData = treasuryData;
    console.log('set it', treasuryData, form.value.treasuryData)
    store.patchInStore(issue.value._id, treasuryData )
    $set.value[`treasuryData.${path}`] = val
    clearTimeout(to.value);
    setTimeout(() => {
      store.patch(issue.value._id, {$set: $set.value})
    }, 1500)
  }
</script>

<style lang="scss" scoped>

</style>
