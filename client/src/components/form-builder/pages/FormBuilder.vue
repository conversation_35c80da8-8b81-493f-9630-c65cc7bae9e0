<template>
  <q-page style="position: relative" class="q-pt-lg bg-ir-bg1">

    <div class="row justify-center">
      <div class="_cent pw2 pd5 bg-white">

        <div class="row items-center q-py-md q-px-sm" style="width: 100%">
          <q-avatar size="60px" color="ir-mid">
            <img v-if="form.avatar?.url" :src="getFile({ obj: form, path: 'avatar' })">
            <q-icon v-else name="mdi-image" color="ir-bg"></q-icon>
            <q-popup-proxy>
              <image-form v-model="form.avatar" :use-atc-store="useAtcStore"></image-form>
            </q-popup-proxy>
          </q-avatar>

          <q-input :style="`width: ${$max(5, form.name?.length + 3)}ch; max-width: 30vw`" class="q-ml-xs" outlined dense placeholder="Name Form"
                   input-class="text-sm text-mb-sm text-weight-light" v-model="form.name"/>
          <q-space/>
          <q-btn-group
              push
              size="sm"
              v-model="sliderOn"
          >
            <template v-if="$q.screen.lt.md">
              <q-btn @click="sliderOn = true" :class="sliderOn ? 'bg-primary text-light' : 'bg-light text-primary'">
                <q-icon name="mdi-form-select"/>
                <q-tooltip content-class="bg-dark text-light">Build Form</q-tooltip>
              </q-btn>
            </template>
            <template v-if="$q.screen.lt.md">
              <q-btn @click="sliderOn = false" :class="!sliderOn ? 'bg-primary text-light' : 'bg-light text-primary'">
                <q-icon name="mdi-eye"/>
                <q-tooltip content-class="bg-dark text-light">Preview Form</q-tooltip>
              </q-btn>
            </template>
          </q-btn-group>
          <div>
            <div v-if="form.updatedAt" class="font-3-4r q-pb-sm text-center">
             Saved {{ $ago(form.updatedAt, 'h:mm a') }}
            </div>
            <q-btn v-if="!form._id" push class="q-mx-xs" @click="save" color="ir-text">
              <q-icon name="mdi-content-save"/>
              <q-tooltip content-class="bg-dark text-light">Save Form</q-tooltip>
            </q-btn>

          </div>
        </div>

        <q-separator/>

        <field-strip
            :form="form"
            v-model="tab"
            @update:model-value="welcomeDialog = false; finishDialog = false;"
            @finish="finishOn"
            @welcome="welcomeOn"
        ></field-strip>

        <q-separator/>

        <div class="q-pa-md q-mt-lg text-md text-mb-md row justify-end text-weight-md">
          <q-btn-dropdown
              no-caps
              class="q-mr-md tw-six"
              label="Form Color"
              :color="form.primaryColor || 'primary'"
              :size="$q.screen.lt.md ? 'xs' : 'sm'"
          >
            <color-picker @update:model-value="updateFormColor" v-model="form.primaryColor"></color-picker>
          </q-btn-dropdown>
          <q-btn-dropdown
              no-caps
              class="tw-six"
              label="Button Color"
              :color="form.secondaryColor || 'secondary'"
              :size="$q.screen.lt.md ? 'xs' : 'sm'"
          >
            <color-picker @update:model-value="updateBtnColor" v-model="form.secondaryColor"></color-picker>
          </q-btn-dropdown>
        </div>

        <div class="row" style="height: 100%; min-height: 75vh; position: relative">
          <div :class="`col-12 col-md-6 q-pa-sm ${$q.screen.lt.md ? '__slider' : ''}`"
               :style="sliderOn || $q.screen.gt.sm ? {} : {width: 0, overflow: 'hidden', opacity: 0}">
            <div class="q-pa-sm">
              <q-btn flat color="dark" icon="mdi-plus-circle" icon-right="mdi-menu-down" label="New Field">
                <q-menu>
                  <choose-form
                      @welcome="welcomeOn"
                      @finish="finishOn"
                      :button-color="form.secondaryColor || 'secondary'"
                      :form-color="form.primaryColor || 'primary'"
                      @update:model-value="newField"
                  />
                </q-menu>
              </q-btn>
            </div>

            <q-slide-transition>
              <template v-if="!welcomeDialog && !finishDialog">
                <field-editor
                    :tab-in="tab"
                    @tab="changeTab"
                    @duplicate="duplicateField(tab)"
                    @rmv="rmvField(tab)"
                    :length-in="(form.fields || []).length"
                    :field-index="tab"
                    @reorder="reorder($event - 1, tab, (form.fields || [])[tab])"
                    @update:model-value="setField"
                    :model-value="(form.fields || [])[tab]"
                    :form="form"
                ></field-editor>
              </template>
            </q-slide-transition>

            <q-slide-transition>
              <template v-if="welcomeDialog || finishDialog">
                <div v-if="welcomeDialog">
                  <enter-exit
                      :videos-in="form.welcomeVideos"
                      :files-in="form.welcomeFiles"
                      :image-in="form.welcomeImage"
                      :message-in="form.welcomeMessage"
                      :title-in="form.welcomeTitle"
                      @update:model-value="setEnterExit('welcome', $event)"></enter-exit>
                </div>
                <div v-if="finishDialog">
                  <enter-exit
                      exit
                      :videos-in="form.finishVideos"
                      :files-in="form.finishFiles"
                      :image-in="form.finishImage"
                      :message-in="form.finishMessage"
                      :title-in="form.finishTitle"
                      @update:model-value="setEnterExit('finish', $event)"></enter-exit>
                </div>
              </template>
            </q-slide-transition>

          </div>
          <div class="col-12 col-md-6 q-pa-sm"
               :style="$q.screen.gt.sm ? {borderLeft: 'solid .1px rgba(0,0,0,.2)'} : {}">
<!--            <div class="row">-->
<!--              <div class="text-weight-bold text-xs text-mb-sm">-->
<!--                <div class="cursor-pointer text-ir-mid" style="display: flex; align-items: center"-->
<!--                     @click="formShow === 'slider' ? formShow = 'stack' : formShow = 'slider'">-->
<!--                  <q-icon class="q-mr-xs"-->
<!--                          :name="formShow === 'slider' ? 'mdi-stack-exchange' : 'mdi-page-next-outline'"></q-icon>-->
<!--                  <div>{{ formShow === 'slider' ? 'stack view' : 'slider view' }}</div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
            <q-card flat style="width: 100%; height: 100%;">

              <form-slider
                  :change-counter="changeCounter"
                  :display-in="formShow"
                  :welcome-in="welcomeDialog"
                  :finish-in="finishDialog"
                  @tab="tab = $event"
                  :tabIn="tab"
                  :form-in="form"
              />

            </q-card>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import ChooseForm from 'components/form-builder/forms/ChooseForm.vue';
  import FormSlider from 'components/form-builder/cards/FormSlider.vue';
  import FieldEditor from 'components/form-builder/forms/FieldEditor.vue';
  import ColorPicker from 'components/common/colors/ColorNamePicker.vue';
  import EnterExit from 'components/form-builder/forms/EnterExit.vue';
  import FieldStrip from 'components/form-builder/cards/FieldStrip.vue';
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';

  import {$ago, $max} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useFbs} from 'stores/fbs';
  import {useAtcStore} from 'stores/atc-store';
  import {HForm, HSave} from 'src/utils/hForm';
  import {trackContext} from 'layouts/utils/track-context';
  import {loginPerson} from 'stores/utils/login';
  import {useRouter} from 'vue-router';
  import {getFile} from 'src/utils/fs-utils';

  const router = useRouter();

  const { route, hostId } = trackContext()
  const { person } = loginPerson();

  const fbStore = useFbs();

  const emit = defineEmits(['saved']);
  const props = defineProps({
    formIn: { required: false },
    orgId: String
  })

  const { item: fb } = idGet({
    store: fbStore,
    value: computed(() => props.formIn || route.params.formId),
  })

  const { form, save } = HForm({
    value: fb,
    store: fbStore,
    undoRedo: true,
    beforeFn: (val) => {
      if (!val.owner) val.owner = person.value._id
      if (!val.host) val.host = hostId.value;
      if (props.orgId) val.org = props.orgId
      if (val._id && val.live) {
        if (!val.parent) val.parent = val._id;
        delete val._id;
        delete val.createdAt;
        delete val.updatedAt;
        delete val.createdBy;
        delete val.updatedBy;
        val.live = false;
        if (val.children) delete val.children;
      } else if (!val.parent) val.live = true;
    },
    afterFn: (val) => {
      router.push({ ...route, params: { formId: val._id } });
      emit('saved', val);
    }
  })

  const { autoSave } = HSave({
    form,
    save,
    store: fbStore
  })



  const changeCounter = ref(0);
  const formShow = ref('slider')
  const welcomeDialog = ref(false);
  const finishDialog = ref(false);
  const sliderOn = ref(true);
  const tab = ref(0);

  watch(tab, (nv, ov) => {
    if ((nv || nv === 0) && nv !== ov) {
      welcomeDialog.value = false;
      finishDialog.value = false;
    }
  }, { immediate: true });


  const setEnterExit = (path, values) => {
    const paths = ['Videos', 'Files', 'Image', 'Message', 'Title'].map(a => `${path}${a}`)
    for(const p of paths){
      form.value[p] = values[p.split(path)[1].toLowerCase()]
    }
    autoSave(paths)
  }

  const finishOn = () => {
    welcomeDialog.value = false;
    finishDialog.value = true;
  }
  const welcomeOn = () => {
    finishDialog.value = false;
    welcomeDialog.value = true;
  }
  const changeTab = (i) => {
    tab.value = i;
    welcomeDialog.value = false;
    finishDialog.value = false;
  }
  const updateFormColor = (color) => {
    for (let i = 0; i < (form.value.fields || []).length; i++) {
      form.value.fields[i].color = color
    }
    autoSave(['fields', 'primaryColor'])
  }
  const updateBtnColor = (color) => {
    for (let i = 0; i < (form.value.fields || []).length; i++) {
      form.value.fields[i].attrs = { ...form.value.fields[i].attrs, color }
    }
    autoSave(['fields', 'secondaryColor']);
  }
  const duplicateField = (idx) => {
    const field = Object.assign({}, form.value.fields[idx]);
    form.value.fields.splice(idx, 0, { ...field, id: new Date().getTime().toString() });
    autoSave('fields')
  }
  const rmvField = (idx) => {
    form.value.fields.splice(idx, 1);
    tab.value = Math.max(idx - 1, 0);
    autoSave('fields')
  }

  const arrMove = (arr, fromIndex, toIndex, obj) => {
    const cloneArr = JSON.parse(JSON.stringify(arr));
    cloneArr.splice(fromIndex, 1);
    cloneArr.splice(toIndex, 0, obj);
    return cloneArr;
  }

  const moveField = (newIdx, oldIdx, item) => {
    const list = form.value.fields || [];
    if (newIdx > -1 && newIdx < list.length && newIdx !== oldIdx) {
      form.value.fields = arrMove(list, oldIdx, newIdx, item);
      autoSave('fields')
    }
  }
  const reorder = (newIdx, oldIdx, item) => {
    moveField(newIdx, oldIds, item)
  }

  const newField = (field, i) => {
    const len = (form.value.fields || []).length;
    const f = { id: new Date().getTime().toString(), ...field }
    if (len > 0) {
      const idx = i ? i : len - 1;
      form.value.fields.splice(idx + 1, 0, f);
    } else form.value.fields = [f];
    tab.value = len;
    autoSave('fields')
  }

  const setField = (val) => {
    changeCounter.value++
    const fields = [...form.value.fields || []];
    fields[tab.value] = val;
    form.value.fields = [...fields];
    autoSave('fields')
  }

</script>

<style scoped lang="scss">
  .__slider {
    z-index: 20;
    position: absolute;
    height: 100%;
    background: #fafafa;
    top: 5px;
    left: 0;
    transition: width .3s ease-out;
  }

</style>
