<template>
  <div class="fill_size q-pa-sm">
    <template v-if="responseIn">
    <div class="q-my-sm row justify-end text-xs text-mb-xs text-weight-medium">
      Submitted: {{$ago(responseIn.createdAt, 'ddd MMM-DD, YYYY - h:mm a')}}
    </div>
      <default-item :store="pplStore" :model-value="responseIn.createdBy" description-path="email" :use-atc-store="useAtcStore">
        <template v-slot:bottom="scope">
          <div>{{_get(scope.item, 'phone.number.national', 'No Phone')}}</div>
        </template>
      </default-item>
  <div v-show="_get(responseIn, 'formData')" v-for="(key, i) in Object.keys(responseIn.formData || {})" :key="`key-${i}`">
    <q-item>
      <q-item-section>
<!--        <div class="text-xs text-mb-sm text-weight-light">Question: {{key}}</div>-->
<!--        <answer-iterator :key-in="key" :value="_get(responseIn, ['formData', key])"></answer-iterator>-->
<!--        <div class="text-xs text-mb-sm text-weight-medium">Answer: {{responseIn['formData'][key]}}</div>-->
      </q-item-section>
    </q-item>
  </div>
    </template>
    <template v-else-if="loading">
      <q-spinner></q-spinner>
    </template>
    <template v-else>
      <div class="q-pa-lg text-xxs text-mb-xxs text-italic">
        No Response...
      </div>
    </template>
  </div>
</template>

<script setup>
  // import AnswerIterator from 'components/form-builder/cards/AnswerIterator.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';

  import {onMounted, ref} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {$ago} from 'src/utils/global-methods';
  import {_get} from 'symbol-syntax-utils';
  import {useAtcStore} from 'stores/atc-store';

  const pplStore = usePpls();
  const props = defineProps({
    responseIn: Object
  })

  const loading = ref(true);

  onMounted(() => {
    setTimeout(() => {
      loading.value = false
    }, 2000)
  })
</script>
