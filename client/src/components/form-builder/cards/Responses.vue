<template>
  <div class="fill_size q-pa-md">

    <div class="row items-center text-sm text-mb-md text-weight-medium">
      <div>{{ form.name || 'Untitled' }} Results</div>
      <q-space/>
      <div class="text-xs text-mb-sm text-weight-light">{{ total }} Responses</div>
    </div>

    <div v-for="(response, i) in stateResponses" :key="`response-${i}`">
      <q-separator class="q-my-sm"/>
      <response-card :response-in="response"></response-card>
    </div>

    <pagination-row v-bind="{ limit, pageRecordCount, pagination, h$:r$ }"></pagination-row>

  </div>
</template>

<script setup>
  import ResponseCard from './ResponseCard.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';

  import {ref, computed} from 'vue';
  import {useRoute} from 'vue-router';
  import {idGet} from 'src/utils/id-get';
  import { useFbs } from 'src/stores/fbs';
  import {HFind} from 'src/utils/hFind';
  import {useFbRes} from 'stores/fb-res';

  const route = useRoute()
  const fbStore = useFbs()
  const fbrStore = useFbRes()

  const props = defineProps({
    formIn: Object
  })

  const total = ref(0);
  const skip = ref(0);
  const limit = ref(25);

  const formId = computed(() => props.formIn?._id || route.params.formId)

  const { item:form } = idGet({
    store: fbStore,
    value: formId
  })


  const { h$:r$, pageRecordCount, pagination } = HFind({
    store: fbrStore,
    limit,
    params: computed(() => {
      return {
        query: {
          $skip: skip.value,
          $sort: { createdAt: -1 },
          _id: { $in: form.value.responses || [] }
        },
        runJoin: { with_person: true }
      }
    })
  })

</script>
