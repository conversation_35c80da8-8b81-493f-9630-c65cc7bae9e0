<template>
  <div class="_fw">
    <div class="__top">
      <div class="font-1r tw-six">Elect Plan Coverage</div>
      <div class="font-1r alt-font">Coverage options are how {{ fullPlan?.name }} helps you pay for healthcare expenses
        and contain your risk of
        medical expenses.
      </div>
    </div>

    <q-tab-panels class="_panel" :model-value="!!viewing" animated>
      <q-tab-panel class="_panel" :name="false">

        <div class="__tp">
          <div class="font-1r tw-six q-py-md">Your Coverages
            ({{ enrollmentCoverageIds.length }} Selected)
          </div>

          <!--          SELECTED COVERAGES-->
          <div class="__ct">
            <table>
              <tbody>
              <tr class="font-3-4r tw-six text-grey-7">
                <td></td>
                <td></td>
                <td>Name</td>
                <td>Type</td>
                <td>Premium (Mo)</td>
                <td>Allowance</td>
                <td>Enrolled</td>
              </tr>
              <tr v-for="(item, i) in enrolledCoverages.data"
                  :key="`enr-${i}`" @click="setViewing(item)">
                <td></td>
                <td>
                  <q-avatar size="10px" :color="typeColors[item.type]"></q-avatar>
                </td>
                <td>{{ item.name }}</td>
                <td>{{ types[item.type] }}</td>
                <td class="alt-font">{{ displayPremium(item) }}</td>
                <td class="alt=font">
                  {{ dollarString(getErContribution({ covId: item._id, enrollment, plan: fullPlan }), '$', 2) }}
                </td>
                <td>{{ ((enrollment?.coverages || {})[item._id]?.participants || []).length }}</td>
              </tr>
              </tbody>
            </table>
          </div>

        </div>

        <div class="__tp">
          <div class="tw-six q-pb-sm font-1r">Select {{
              enrolledCoverages.total ? 'Additional ' : ''
            }}Coverage
          </div>
          <q-input bg-color="white" rounded outlined v-model="search.text" placeholder="Search options...">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <div class="q-py-sm flex items-center">
            <q-btn dense flat size="sm" color="primary" icon="mdi-filter"></q-btn>
            <type-picker :use-types="relevantTypes" multiple v-model="activeTypes"></type-picker>

          </div>


          <div v-if="!filteredCoverages.length" class="q-pa-md text-italic font-1r">
            No more options
          </div>

          <q-scroll-area id="I_S" @scroll="senseScrollLoad">
            <div class="row q-pa-sm">
              <div class="col-12 col-md-6 q-pa-sm"
                   v-for="(cov, i) in filteredCoverages"
                   :key="`cov-${i}`">
              <div
                  :id="`i_s-${i}`"
                  class="__c"
                  @click="setViewing(cov)">
                <coverage-card :person="person" :plan="fullPlan" :model-value="cov"></coverage-card>
              </div>
              </div>

            </div>
          </q-scroll-area>
          <div class="row q-py-sm">
            1 - {{ filteredCoverages.length }} of {{ filteredCoverages.length }} Unselected Coverage
            Options
          </div>

        </div>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">
        <div class="row items-center">
          <q-btn flat dense icon="mdi-chevron-left" color="primary" @click="viewingOff"></q-btn>
          <q-space></q-space>
          <remove-proxy-btn color="black" icon="" :dense="false" v-if="(enrollment?.coverages || {})[viewing._id]" flat
                        no-caps
                        remove-label="Un-enroll from coverage?" @remove="unenroll(viewing)">
            <template v-slot:default>
              <span class="q-mr-sm">Un-enroll</span>
              <q-icon color="red" name="mdi-cancel"></q-icon>
            </template>
          </remove-proxy-btn>
        </div>
        <coverage-page v-bind="{ subsidy:getSubsidy(viewing), modelValue: viewing, plan: fullPlan, person, enrollment }">
          <template v-slot:bottom>
            <q-separator class="q-my-sm"></q-separator>
            <div class="q-pt-md q-pb-sm tw-six">
              Enroll Household Members
            </div>
            <household-select
                @update:model-value="changes = true"
                v-model="participants"
                v-bind="{
                  person,
                  plan:fullPlan,
                  coverage:viewing,
                  enrollment
                }"
            ></household-select>
            <div class="row q-pa-md justify-end">
              <q-btn v-if="changes" push class="_s_btn tw-six" no-caps icon-right="mdi-content-save"
                     label="Confirm Changes"
                     @click="selectCoverage()"></q-btn>
            </div>
          </template>
        </coverage-page>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import CoveragePage from 'components/coverages/pages/CoveragePage.vue';
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';
  import HouseholdSelect from 'components/households/forms/HouseholdSelect.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import TypePicker from 'components/coverages/cards/TypePicker.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {HFind, hInfiniteScroll} from 'src/utils/hFind';
  import {types, typeColors} from 'src/components/coverages/utils/types';
  import {useCoverages} from 'stores/coverages';
  import {$errNotify, dollarString} from 'src/utils/global-methods';
  import {useEnrollments} from 'stores/enrollments';
  import {_get} from 'symbol-syntax-utils';
  import {usePpls} from 'stores/ppls';
  import {containsPoint} from 'src/utils/geo-utils';
  import {getCoverageRate} from 'components/coverages/utils/display';
  import {getErContribution} from 'components/enrollments/utils/contributions';
  import {useCmsStore} from 'stores/cms-store';
  import {useMarketplace} from 'stores/marketplace';
  import {convertCmsPerson} from 'components/market/household/utils';
  import {useShops} from 'stores/shops';

  const planStore = usePlans();
  const cStore = useCoverages();
  const eStore = useEnrollments();
  const pplStore = usePpls();
  const cmsStore = useCmsStore();
  const marketStore = useMarketplace();
  const shopStore = useShops();

  const props = defineProps({
    plan: { required: true },
    modelValue: { required: true }
  })

  const enrollment = computed(() => props.modelValue || {});

  const { item: shop } = idGet({
    store: shopStore,
    value: computed(() => enrollment.value.shop)
  })
  const { item: fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore
  })
  const { item: person } = idGet({
    value: computed(() => enrollment.value?._fastjoin?.person || enrollment.value?.person),
    store: pplStore
  })

  const getSubsidy = (covId) =>  {
    return getErContribution({
      plan: fullPlan.value,
      covId,
      enrollment
    })
  }

  const viewing = ref(undefined);
  const changes = ref(false);
  const participants = ref([])

  const viewingOff = () => {
    viewing.value = undefined;
    changes.value = false;
    participants.value = [];
  }

  const unenroll = (cov) => {
    const patchObj = { $unset: { [`coverages.${cov._id}`]: '' } }
    const id = enrollment.value._id
    eStore.patchInStore(id, patchObj);
    eStore.patch(id, patchObj)
        .catch(err => $errNotify(err.message));
    viewingOff()
  }

  //Filter out coverages that don't apply to the group or geo of this person
  const planCoverageIds = computed(() => {
    const arr = [];
    const cov = fullPlan.value?.coverages || {};
    const ids = Object.keys(cov);
    for (let i = 0; i < ids.length; i++) {
      let pass = false;
      const obj = cov[ids[i]];
      if (obj) {
        const { geo, groups = [] } = obj;
        if (!groups.length && !geo?.features?.length) pass = true;
        else {
          const p = person.value || {};
          for (let idx = 0; idx < groups.length; idx++) {
            if ((p.inGroups || []).includes(groups[idx])) pass = true
          }
          if (geo && !pass && p._id) {
            const { enrolled } = enrollment.value || {}
            const basePoint = [enrollment.value?.address?.longitude, enrollment.value?.address?.latitude]
            pass = containsPoint(basePoint, geo)
            //If person is not in geo - check each member
            if (!pass) {
              for (const e in enrolled) {
                if (enrolled[e].point) pass = containsPoint(enrolled[e].point, geo);
                if (pass) break;
              }
            }
          }
        }
      } else pass = true;
      if (pass) arr.push(ids[i])
    }
    return arr;
  })
  const enrollmentCoverageIds = computed(() => Object.keys(enrollment.value?.coverages || {}))

  const enrolled = computed(() => {
    return cStore.findInStore({ query: { _id: { $in: Object.keys(enrollment.value?.coverages || {}) } } })
  })

  const enrolledTypes = computed(() => {
    const obj = {};
    for (const k in types.value) {
      obj[k] = (enrolled.value.data || []).filter(a => a.type === k);
    }
    return obj;
  })

  const limitMsg = computed(() => {
    const obj = {};
    const h = enrolledTypes.value;
    if (h.mm?.length) {
      obj.mm = 'You can only choose one Major Medical coverage';
      obj.hs = 'You cannot choose both Sharing and Major Medical coverage';
      obj.hra = 'You cannot choose both HRA and Major Medical coverage';
    }
    if (h.hs?.length) {
      obj.mm = 'You cannot choose both Sharing and Major Medical coverage';
      obj.hs = 'You can only choose one Sharing option';
    }
    if (h.hra?.length) {
      obj.mm = 'You cannot choose both HRA and Major Medical coverage'
      obj.hra = 'You cannot choose multiple HRA options'
    }
    return obj;
  })

  const selectCoverage = async () => {
    const id = enrollment.value._id;
    const vId = viewing.value._id;
    if (!_get(enrollment.value, `coverages.${vId}`) && !!limitMsg.value[viewing.value.type]) $errNotify(limitMsg.value[viewing.value.type]);
    else if (!participants.value?.length) {
      if (!_get(enrollment.value, ['coverages', vId, 'participants'])?.length) $errNotify('No participants selected')
      else {
        const patchObj = { $unset: { [`coverages.${vId}`]: '' } };
        eStore.patchInStore(id, patchObj)
        await eStore.patch(id, patchObj)
        viewing.value = undefined;
      }
    } else {
      /** Set initial enrollment coverage settings. If the plan is an ichra nad there is also a shop option available for the plan, enroll in the shop coverage simultaneously so the enrollment experience includes all options. */
      const patchObj = { $set: { [`coverages.${vId}.participants`]: participants.value, [`coverages.${vId}.ichra`]: !!viewing.value.ichra, [`coverages.${vId}.shop`]: !!viewing.value.shop }}
      if(viewing.value.ichra){
        const shop = c$.data.filter(a => a.shop && a.type.includes('hs', 'mm', 'hra'))[0]
        if(shop && !(enrollment.value.coverages || {})[shop._id]) patchObj.$set[`coverages.${shop._id}`] = { participants: participants.value, shop: true }
      }
      eStore.patchInStore(id, patchObj)
      await eStore.patch(id, patchObj)
          .catch(err => $errNotify(`Error adding coverage. Try again. Error: ${err.message}`))
      viewing.value = undefined;
    }
  }

  const setViewing = (val) => {
    viewing.value = val;
    participants.value = _get(enrollment.value, `coverages.${val._id}.participants`);
  }

  const activeTypes = ref([])

  const search = ref({ text: '' });
  const { h$: c$ } = HFind({
    store: cStore,
    limit: computed(() => planCoverageIds.value?.length || 0),
    params: computed(() => {
      const q = { covered: 'group', _id: { $in: planCoverageIds.value || [] } };
      if (activeTypes.value?.length) q.type = { $in: activeTypes.value };
      return {
        query: q
      }
    })
  })


  const relevantTypes = computed(() => Array.from(new Set(c$.data.map(a => a.type))));

  // const planCoverages = computed(() => {
  //   return cStore.findInStore({ query: { _id: { $in: planCoverageIds.value } } })
  // })

  const enrolledCoverages = computed(() => cStore.findInStore({ query: { _id: { $in: enrollmentCoverageIds.value } } }))

  const getRate = (cov) => {
    return (enrollment.value?._fastjoin?.rates || []).filter(a => a.coverage === cov._id)[0];
  }
  const enrolledPremiums = ref({})
  const setEnrolledPremiums = async () => {
    const obj = {};
    const list = enrolledCoverages.value.data || [];
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.shop) {
        /** Handle shop scenarios */
        const config = enrollment.value.coverages[item._id] || {};
        if(config.premium) obj[item._id] = config.premium;
        else if (config.policy) {
          let policy = cmsStore.get(item._id);
          if (!policy) policy = await marketStore.get(config.policy, {
            runJoin: {
              get_plan: {
                id: config.policy,
                place: shop.value.stats?.place,
                household: {
                  income: enrollment.value.householdIncome || 10000000,
                  people: [convertCmsPerson(person.value), ...(shop.value.stats?.people || []).map(a => convertCmsPerson(a))]
                }
              }
            }
          })
              .catch(err => {
                console.log(`Could not get cms policy in choose coverages: ${err.message}`)
                return undefined
              })
          obj[item._id] = policy?.premium || 0
        } else if (config.individual_coverage) {

            let cvg = cStore.getFromStore(config.individual_coverage).value;
            if (!cvg) cvg = cStore.get(config.individual_coverage)
                .catch(err => console.log(`Error loading individual coverage in choose coverage: ${err.message}`))
            obj[item._id] = getCoverageRate({ coverage: cvg, enrollment: enrollment.value }) || 0
          }

      } else obj[item._id] = getCoverageRate({ coverage: item, enrollment: enrollment.value, rate: getRate(item) })
    }
    enrolledPremiums.value = obj;
  }

  watch(() => enrolledCoverages.value.data, (nv, ov) => {
    if (nv?.length && nv.length !== ov?.length) {
      setEnrolledPremiums();
    }
  }, { immediate: true })

  const displayPremium = (cov) => {
    if (enrolledPremiums.value[cov._id]) return dollarString(enrolledPremiums.value[cov._id], '$', 2)
    else if (enrollment.value?.ichra) {
      //TODO: this may not be valid anymore
      const policy = enrollment.value?._fastjoin?.ichra?.policy?.policy
      if (policy?.premium) return dollarString(policy.premium, '$', 2)
      else return 'Policy Not Selected'
    } else return 'N/A'
  }

  const filteredCoverages = computed(() => c$.data.filter(a => !enrollmentCoverageIds.value.includes(a._id) && a.name.toLowerCase().includes(search.value.text.toLowerCase())));

  const { senseScrollLoad } = hInfiniteScroll({ h$: c$, loadNum: 100 })


</script>

<style lang="scss" scoped>
  .__top {
    padding: 20px;
    border-radius: 8px;
    background: var(--ir-bg1);
    margin: 10px 0;
  }

  .__c {
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px var(--ir-light);
    background: white;
    position: relative;
    height: 100%;
  }

  .__av {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    border: solid 2px;
  }


  #I_S {
    height: 800px;
  }

  .__ct {
    width: 100%;
    overflow-x: scroll;
    border-radius: 10px;

    table {
      width: 100%;
      border-collapse: collapse;
      box-sizing: border-box;

      tr {
        cursor: pointer;
        border-radius: 6px;
        transition: all .3s;
        box-shadow: 0 2px 2px var(--ir-light);

        &:hover {
          background: var(--q-p0);
          box-shadow: 0 2px 4px var(--ir-light);

        }

        &:first-child {
          td {
            border-bottom: none;
          }
          &:hover {
            background: white;
          }
        }
        &:last-child {
          td {
            border-bottom: none;
          }
        }

        td {
          background: white;
          border-bottom: 1px solid var(--ir-light);
          cursor: pointer;
          padding: 10px 12px;
          white-space: nowrap;
          font-family: var(--alt-font);
        }

        td:first-child {
          padding: 10px 4px;
        }

        td:last-child {
          width: 40%;
        }
      }

    }
  }

  .__tp {
    padding: 30px 2vw;
    background: var(--ir-bg1);
    border-radius: 8px;
    margin: 20px 0;
  }

</style>
