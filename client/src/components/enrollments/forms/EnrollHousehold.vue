<template>
  <div class="_fw">
    <div class="tw-six font-1r q-px-sm q-pt-md">Add Your Household Members</div>
    <div class="row items-center q-py-sm q-px-sm">
      <div class="tw-six text-grey-8 font-3-4r">Enrollment Type:</div>
      <q-radio label="Single" :model-value="enrollment?.type" val="single" @update:model-value="setType($event)"></q-radio>
      <q-radio label="Family" :model-value="enrollment?.type" val="family" @update:model-value="setType($event)"></q-radio>
    </div>

    <q-slide-transition>
      <household-members
          v-if="enrollment?.type === 'family'"
          :person="person"
          @loaded="hhLoaded()"
          @update:model-value="updateHH"
          :enrollment="enrollment"
      ></household-members>
    </q-slide-transition>
    <q-expansion-item v-if="enrollment?.type === 'single'" expand-icon="mdi-menu-down">
      <template v-slot:header>
        <member-item :model-value="person" is-person></member-item>
      </template>
      <member-form :model-value="person" @update:model-value="setEnrolled(true)"></member-form>
    </q-expansion-item>

    <common-dialog v-model="confirmResetType" setting="small">
      <div class="_fw q-pa-lg">
        <div class="tw-six font-1r">Change enrollment type to single?</div>
        <q-separator class="q-my-sm"></q-separator>
        <div class="font-7-8r">This will remove enrollment data for all other household members</div>
        <div class="q-pt-md row justify-end">
          <q-btn flat no-caps @click="confirmResetType = false">
            <span class="q-mr-sm">Cancel</span>
            <q-icon color="red" name="mdi-close"></q-icon>
          </q-btn>
          <q-btn class="q-ml-sm" flat no-caps @click="setType(pendingType, true)">
            <span class="q-mr-sm">Yes</span>
            <q-icon color="green" name="mdi-check"></q-icon>
          </q-btn>
        </div>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import HouseholdMembers from 'components/households/cards/HouseholdMembers.vue';
  import MemberItem from 'components/households/cards/MemberItem.vue';
  import MemberForm from 'components/households/forms/MemberForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import {enrollHousehold} from 'components/coverages/utils/enrolled';

  const eStore = useEnrollments();

  const props = defineProps({
    plan: { required: true },
    modelValue: { required: true }
  })

  const { item:enrollment } = idGet({
    value: computed(() => props.modelValue),
    store: eStore
  })

  const {
    hhLoaded,
    updateHH,
    setType,
    confirmResetType,
    setEnrolled,
    person
  } = enrollHousehold({ enrollment })

</script>

<style lang="scss" scoped>

</style>
