<template>
  <div class="_fw q-pa-md">
    <!--    <div class="q-pa-sm">-->
    <!--      <div class="tw-six font-1r">Welcome to the Cafeteria</div>-->
    <!--      <div class="font-1r">-->
    <!--        The <b>"cafeteria"</b> part of the plan (aka-->
    <!--        <b>section 125</b>) is the <i>only</i> way for employees to elect to use payroll dollars to purchase healthcare-->
    <!--        tax free. If you don't select healthcare, any plan dollars will be taxed to you as normal income.-->
    <!--      </div>-->
    <!--    </div>-->


    <div class="tw-six font-1r">Make
      elections for each category below.
    </div>

    <div class="row __pad">
      <div class="flex items-center relative-position no-wrap">
        <q-btn :color="!tab ? 'p3' : 'primary'" dense flat round icon="mdi-home-circle" class="z5 bg-white"
               @click="setTab('')"></q-btn>
        <div class="__line"></div>
        <div v-for="(k, i) in activeCafe" :key="`k-${i}`"
             :class="`__dot ${tab === k ? '__active' : isDone(k) ? '__done' : ''}`" @click="setTab(k)">
          <div class="font-3-4r text-grey-8 tw-six text-uppercase __txt">{{ cafeKeys[k].shortName }}</div>
        </div>
      </div>
    </div>

    <q-tab-panels :model-value="!!tab" animated transition-prev="slide-up" transition-next="slide-down">
      <q-tab-panel class="_panel" :name="false">

        <div class="_fw q-pa-sm">
          <table>
            <thead>
            <tr>
              <td>Account</td>
              <td>Election (Monthly)</td>
              <td>Opt Out</td>
              <td>Compliance</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(k, i) in activeCafe" :key="`kl-${i}`" @click="setTab(k)">
              <td>{{ cafeKeys[k]?.smallName }} <span v-if="cafeKeys[k].smallName !== cafeKeys[k].shortName" class="font-3-4r tw-six text-ir-mid">({{ cafeKeys[k]?.shortName }})</span></td>
              <td>
                <span :class="`tw-six text-${_get(enrollment, `cafe.${k}.amount`) ? 'p7' : 's7'}`">{{
                    dollarString(_get(enrollment, `cafe.${k}.amount`) || 0, '$', 0)
                  }}</span>
              </td>
              <td>
                <q-icon name="mdi-checkbox-marked" color="secondary" size="22px"
                        v-if="!!_get(enrollment, `cafe.${k}.optOut`)"></q-icon>
              </td>
              <td>
                <q-icon size="25px" v-if="complianceDone[k] || _get(enrollment, `cafe.${k}.optOut`)" color="green"
                        name="mdi-check-circle"></q-icon>
                <q-icon size="25px" v-else color="yellow-7" name="mdi-alert"></q-icon>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </q-tab-panel>

      <q-tab-panel class="_panel" :name="true">
        <q-tab-panels :model-value="tab" animated class="_panel">

          <q-tab-panel class="_panel" v-for="(k, i) in activeCafe" :key="`panel${i}`" :name="k">
            <div class="row __on">
              <div class="col-12 col-md-6 q-pa-sm" @click="setTab(k)">
                <div class="tw-six font-1-1-8r">{{ cafeKeys[k]?.name }} ({{ cafeKeys[k]?.shortName }})</div>
                <div class="font-1r">{{ cafeKeys[k]?.ad }}</div>
                <q-separator class="q-my-sm"></q-separator>


                <div class="_fw">
                  <q-checkbox
                      class="q-my-sm"
                      :model-value="form.optOut"
                      :label="`Opt out of the ${cafeKeys[k]?.name} for ${fullPlan?.name}`"
                      @update:model-value="setOptOut"
                  ></q-checkbox>
                  <q-slide-transition>
                    <div class="_fw" v-if="(!form.optOut && complianceDone[k]) || form.amount">
                      <div class="w400 mw100">
                        <div class="tw-six text-grey-8 q-pt-sm">Elective Monthly {{ cafeKeys[k]?.name }} Contributions
                        </div>
                        <money-input
                            @update:model-value="autoSave('amount')"
                            v-model="form.amount"
                            :decimal="2"
                            placeholder="Contribution Amount (Monthly)"
                            filled
                            :error-message="errors[k]"
                            :error="!!errors[k]"
                        ></money-input>
                      </div>
                    </div>
                  </q-slide-transition>
                </div>
              </div>
              <div class="col-12 col-md-6 q-pa-sm">
                <q-slide-transition>
                  <div class="_fw">
                    <enroll-by-key
                        v-if="!form.optOut"
                        :plan="fullPlan"
                        :enrollment="enrollment"
                        :coverages="coverages"
                        :contributions="contributions"
                        :benefit-key="k"
                        @done="complianceDone[k] = true"
                    ></enroll-by-key>

                    <template v-if="k === 'pop'">
                      <div class="q-py-sm _fw font-1r tw-six">
                        You have <span class="__need bg-p1 text-p10">{{
                          dollarString(contributions?.needed?.preTax || 0, '$', 2)
                        }}</span>
                        in medical premiums elected.
                        <div class="tw-four">You use the Premium Only Plan to pay them tax free.</div>
                      </div>
                    </template>
                    <template v-if="k === 'cash'">
                      <div class="q-py-sm _fw font-1r tw-six">
                        You have <span :class="`__need ${contributions?.needed?.postTax > form.amount ? 'bg-s1 text-s10' : 'bg-p1 text-p10'}`">{{
                          dollarString(contributions?.needed?.postTax || 0, '$', 2)
                        }}</span>
                        in post-tax premiums elected.
                        <div class="tw-four">Your taxable elections should be used to cover these.</div>
                      </div>
                    </template>

                  </div>
                </q-slide-transition>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>

      </q-tab-panel>
    </q-tab-panels>


  </div>
</template>

<script setup>
  import EnrollByKey from 'components/enrollments/forms/EnrollByKey.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {usePlans} from 'stores/plans';
  import {idGet} from 'src/utils/id-get';
  import {computed, nextTick, onMounted, ref} from 'vue';
  import {cafeKeys} from 'components/plans/utils';
  import {useEnrollments} from 'stores/enrollments';
  import {dollarString} from 'src/utils/global-methods';
  import {_get} from 'symbol-syntax-utils';
  import {useRoute, useRouter} from 'vue-router';

  const route = useRoute();
  const router = useRouter();
  const planStore = usePlans();
  const eStore = useEnrollments();

  const emit = defineEmits(['update:changes']);
  const props = defineProps({
    plan: { required: false },
    emitUp: Boolean,
    modelValue: { required: true },
    coverages: { default: () => [] },
    contributions: {
      default: () => {
        return { needed: { total: 0, preTax: 0 } }
      }
    }
  })

  const tab = ref('');
  const complianceDone = ref({});

  const { item: fullPlan } = idGet({
    value: computed(() => props.plan || props.modelValue?.plan),
    store: planStore
  })

  const enrollment = computed(() => props.modelValue);

  const activeCafe = computed(() => {
    const list = [];
    const cafe = fullPlan.value?.cafe || {}
    for (const k in cafeKeys) {
      if (cafe[k]?.active) list.push(k);
    }
    return list
  })

  const isDone = (k) => {
    const cafe = props.modelValue?.cafe || {};
    return !!cafe[k]?.amount || !!cafe[k]?.optOut
  }

  const formFn = (defs) => {
    return {
      optOut: false,
      amount: 0,
      ...defs
    }
  }
  const form = ref(formFn())

  const patchObj = ref({});
  const to = ref(undefined);
  const runSave = () => {
    if (to.value) {
      window.clearTimeout(to.value);
      to.value = undefined;
    }
    if (Object.keys(patchObj.value).length) {
      const $set = {};
      for (const k in patchObj.value) {
        $set[`cafe.${tab.value}.${k}`] = patchObj.value[k]
      }
      if (!props.emitUp) {
        // eStore.patchInStore(enrollment.value._id, { $set });
        eStore.patch(enrollment.value._id, { $set })
      } else {
        for (const k in patchObj.value) {
          if (k === 'optOut') $set[`cafe.${tab.value}.amount`] = 0;
        }
        emit('update:changes', $set);
      }
    }
  }
  const autoSave = (path) => {
    nextTick(() => {
      patchObj.value[path] = form.value[path];
    })
    if (to.value) window.clearTimeout(to.value);
    to.value = window.setTimeout(() => {
      runSave()
    }, 1500)
  }

  const setOptOut = (val) => {
    form.value.optOut = val;
    autoSave('optOut');
    if (!val) {
      form.value.amount = 0;
      autoSave('amount');
    }
  }

  const setTab = (val) => {
    tab.value = val;
    if (to.value) runSave();
    form.value = formFn((enrollment.value?.cafe || { [val]: {} })[val])
    if (val === 'pop') {
      const preTax = props.contributions?.preTax || 0;
      if (preTax && form.value.amount !== preTax) {
        form.value.amount = preTax;
        autoSave('amount');
      }
    }
    const { href } = router.resolve({ ...route, params: { ...route.params, tab: route.params.tab, subTab: val } });
    window.history.pushState({}, '', href)
  }

  const errors = computed(() => {
    return {
      'pop': Math.abs((props.contributions?.needed?.preTax || 0) - form.value.amount) > .01 ? `You must match your ${dollarString(props.contributions?.needed?.preTax || 0, '$', 2)} in elected coverage premiums` : '',
      'cash': Math.abs((props.contributions?.needed?.postTax || 0) - form.value.amount) > .01 ? `You must match your ${dollarString(props.contributions?.needed?.postTax || 0, '$', 2)} in elected post tax premiums` : ''
    }
  })

  onMounted(() => {
    if (route.params.subTab) setTab(route.params.subTab);
  })
</script>

<style lang="scss" scoped>
  .__pad {
    padding: 15px 0 30px 0;
    width: 100%;
    overflow-x: scroll;
  }

  .__dot {
    width: 15px;
    height: 15px;
    margin: 0 20px;
    border: solid 2px white;
    box-shadow: 0 0 0 2px var(--q-secondary);
    border-radius: 50%;
    background: var(--q-secondary);
    z-index: 1;
    transition: all .2s;
    cursor: pointer;
    position: relative;
  }

  .__txt {
    position: absolute;
    top: 22px;
    left: 50%;
    transform: translate(-50%, 0);
  }

  .__line {
    position: absolute;
    top: 50%;
    left: 20px;
    right: 20px;
    transform: translate(0, -50%);
    height: 2px;
    //background: var(--q-primary);
    background: #999;
  }

  .__active {
    background: var(--q-p3);
    box-shadow: 0 0 0 2px var(--q-p3);
    transform: scale(1.05);
  }

  .__done {
    background: var(--q-primary);
    box-shadow: 0 0 0 2px var(--q-primary);
  }

  .__off {
    box-shadow: 2px 4px 12px -4px #999;
    border-radius: 15px;
    background: white;
    margin: 9px 3px;
    transition: all .2s;
    padding: 20px 6px;
    cursor: pointer;
  }

  .__on {
  }

  table {
    text-align: center;
    width: 100%;
    border-collapse: collapse;
    //border-spacing: 0 10px;
    font-size: 1rem;

    thead {
      font-weight: 600;
      font-size: .8rem;

      tr {
        box-shadow: none;

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }

    tr {
      border-radius: 8px;
      //box-shadow: 2px 3px 6px #dedede;
      transition: all .4s;
      cursor: pointer;

      &:hover {
        box-shadow: 2px 4px 12px -4px #999;
        transform: translate(0, -2px);
      }

      td {
        border-bottom: solid .3px #999;
        padding: 12px 6px;
      }
    }

  }

  .__need {
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: var(--alt-font);
    font-size: 1.2rem;
  }
</style>
