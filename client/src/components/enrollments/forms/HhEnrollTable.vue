<template>
  <div class="__mbrs">
    <div class="row items-center q-py-md">
      <div>Enrollment Type:</div>
      <q-chip color="p6" outline class="tw-six">{{ $capitalizeFirstLetter(enrollment?.type || 'Not Selected') }}</q-chip>
      <q-space/>
      <q-btn @click="adding = true" flat no-caps v-if="canEdit">
        <span>Add Member</span>
        <q-icon class="q-ml-sm" color="primary" name="mdi-plus"></q-icon>
      </q-btn>
    </div>
    <table v-if="Object.keys(byId)">
      <thead>
      <tr class="font-3-4r tw-six">
        <td v-if="canEdit">Enroll</td>
        <td>Member</td>
        <td>Relation</td>
        <td>Zip</td>
        <td>Smoker</td>
        <td>Disabled</td>
        <td>Incarcerated</td>
      </tr>
      </thead>
      <tr v-for="(mbr, i) in Object.keys(byId || {})" :key="`mbr-${i}`" class="cursor-pointer"
          @click="editing = mbr">
        <td v-if="canEdit">
          <q-checkbox :model-value="isCovered(mbr)"
                      @update:model-value="setParticipant(mbr)"></q-checkbox>
        </td>
        <td>
          <member-chip color="white" first-name show-age :model-value="byId[mbr]"></member-chip>
        </td>
        <td>
          <!--          Relation-->
          <relation-chip color="transparent" :model-value="byId[mbr].relation"></relation-chip>
        </td>
        <td>
          <!--          Zip-->
          <span>{{ (byId[mbr].address?.postal || person?.address?.postal || '').slice(0, 5) }}</span>
        </td>
        <td>
          <!--          Smoker-->
          <q-icon color="red-2" name="mdi-checkbox-marked-outline"
                  v-if="(byId[mbr].monthsSinceSmoked || 100) < 12"></q-icon>
        </td>
        <td>
          <!--          Disabled-->
          <q-icon color="purple-2" name="mdi-checkbox-marked-outline" v-if="!!byId[mbr].disabled"></q-icon>
        </td>
        <td>
          <!--          Incarcerated-->
          <q-icon color="orange-2" name="mdi-checkbox-marked-outline" v-if="!!byId[mbr].incarcerated"></q-icon>
        </td>
      </tr>
    </table>

    <div class="q-py-lg row justify-center" v-else>
      <q-spinner color="secondary" size="40px"></q-spinner>
    </div>

    <common-dialog setting="smmd" :model-value="!!editing || adding" @update:model-value="toggleEdit"
                   v-if="canEdit">
      <div class="q-px-md q-pb-md bg-white">
        <div class="_f_l _f_chip">Enroll Member</div>

        <member-form
            :household="hh"
            :model-value="byId[editing]"
            @update:model-value="checkAdd">
          <template v-slot:bottom v-if="!adding">
            <enrollment-extras :enrollment="fulle" :household="hh" :member="byId[editing]"></enrollment-extras>
          </template>
        </member-form>


      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import MemberForm from 'components/households/forms/MemberForm.vue';
  import EnrollmentExtras from 'components/households/forms/EnrollmentExtras.vue';
  import RelationChip from 'components/households/cards/RelationChip.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useEnrollments} from 'stores/enrollments';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {useHouseholds} from 'stores/households';
  import {useCoverages} from 'stores/coverages';
  import {_get} from 'symbol-syntax-utils';


  const pplsStore = usePpls();
  const enrollmentStore = useEnrollments();
  const store = useHouseholds();
  const coverageStore = useCoverages();

  const props = defineProps({
    enrollment: { required: true },
    coverage: { required: true },
    canEdit: Boolean
  })

  const { item: fulle } = idGet({
    store: enrollmentStore,
    value: computed(() => props.enrollment),
    params: ref({ runJoin: { enrollment_person: true } }),
    refreshWhen: computed(() => !props.enrollment?._fastjoin?.person)
  })
  const { item: fullc } = idGet({
    store: coverageStore,
    value: computed(() => props.coverage),
  })

  const { item: hh } = idGet({
    store,
    value: computed(() => fulle.value?._fastjoin?.person?.household)
  })
  const { item: person } = idGet({
    store: pplsStore,
    value: computed(() => fulle.value?.person)
  })

  const participants = ref([]);
  const setParticipants = (tries = 0) => {
    if (fulle.value?._id) {
      participants.value = [...(fulle.value.coverages || {})[fullc.value._id]?.participants || []]
    } else if (tries < 5) {
      setTimeout(() => {
        setParticipants(tries + 1)
      }, 1000)
    }
  }
  watch(fullc, (nv, ov) => {
    if (nv?._id && !ov?._id) setParticipants(0)
  }, { immediate: true })

  const memberIds = computed(() => Object.keys(hh.value?.members || {}));

  const editing = ref(undefined);
  const adding = ref(false);
  const closed = computed(() => {
    return new Date(fulle.value.close).getTime() < new Date().getTime();
  })
  const setParticipant = async (val) => {
    if (props.canEdit) {
      // console.log('set p', val._id || val);
      const id = val._id || val;
      const idx = participants.value.indexOf(id);
      if(idx > -1){
        participants.value.splice(idx, 1);
      } else participants.value.push(id);
      await enrollmentStore.patch(fulle.value._id, { $set: { [`coverages.${fullc.value._id}.participants`]: participants.value } })
    }
  }
  const toggleEdit = (val) => {
    if (!val) {
      editing.value = undefined;
      adding.value = false;
    }
  }
  const byId = ref({})

  const setById = (p) => {
    if(!p) return;
    const eeKeys = Object.keys((fulle.value.enrolled || {})[p._id] || {})
    const keys = Array.from(new Set(['dependent', 'disabled', 'incarcerated', 'annualIncome', 'relation', ...eeKeys]))
    const patchObj = {};
    const path = p._id === fulle.value.person ? '' : `members.${p._id}.`
    for(const k of keys){
      if(!eeKeys.includes(k)){
        const hhv = _get(hh.value, `${path}${k}`)
        if(hhv) patchObj[k] = hhv;
      }
    }
    if(Object.keys(patchObj).length) {
      enrollmentStore.patch(fulle.value._id, { $set: { [`enrolled.${p._id}`]: {...fulle.value.enrolled[p._id], ...patchObj} } })
    }
    byId.value[p._id] = { ...p, _id: p._id, ...fulle.value.enrolled[p._id], ...patchObj }
    if (p._id === person.value?._id) byId.value[p._id].relation = 'self'
  }

  const checkAdd = (val) => {
    if (!byId.value[val._id]) {
      setById(val);
      editing.value = val._id;
      adding.value = false;
      setParticipant(val)
    }
  }

  const isCovered = (id) => {
    return participants.value?.includes(id);
  }

  watch(memberIds, async (nv, ov) => {
    if (nv?.length !== ov?.length) {
      setTimeout(async () => {
        const res = await pplsStore.find({ query: { $limit: nv.length + 1, _id: { $in: nv } } })
        setById(person.value)
        for (const p of res.data) {
          setById(p)
        }
      }, 500)

    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__mbrs {
    width: 100%;
    overflow-x: scroll;

    table {
      border-collapse: collapse;
      width: 100%;

      td {
        padding: 6px 10px;
        border-bottom: solid .3px #999;
      }
    }
  }
</style>
