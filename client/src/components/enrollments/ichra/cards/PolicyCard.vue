<template>
  <div class="_fw">

    <policy-face
        v-bind="{ place, enrollment, mult, aptc:shop?.useAptc ? shop.aptc : 0, dark, policy, coins, combined, openLink, enrType, subsidy }"></policy-face>

    <template v-if="expansion">
      <div class="_fw q-py-md">

        <q-expansion-item v-bind="{ expandIcon: 'mdi-menu-down', ...expandAttrs}">
          <template v-slot:header>
            <slot name="header">
              <q-item class="_fw">
                <q-item-section>
                  <q-item-label>Benefit Details</q-item-label>
                </q-item-section>
              </q-item>
            </slot>
          </template>
          <policy-detail v-bind="{ policy, coins, combined, openLink, dark, ichra }">
            <template v-slot:expansion="scope" v-if="$slots.expansion">
              <slot name="expansion" v-bind="scope"></slot>
            </template>
            <template v-slot:bottom v-if="$slots.bottom">
              <slot name="bottom"></slot>
            </template>
            <template v-slot:benefits>
              <slot name="benefits"></slot>
            </template>
          </policy-detail>
        </q-expansion-item>

      </div>
    </template>

    <template v-if="popup">

      <div class="row q-py-sm">
        <q-chip
            :dark="dark"
            clickable
            @click="open = !open"
            color="ir-bg2"
        >
          <span class="tw-five">Benefit Details</span>
          <q-icon class="q-ml-sm" color="accent" name="mdi-information"></q-icon>
        </q-chip>
      </div>

      <common-dialog v-model="open" setting="medium">
        <div class="_fw q-pa-lg">
          <policy-face v-bind="{ place, policy, coins, combined, openLink, dark }"></policy-face>
          <q-separator class="q-my-md"></q-separator>
          <policy-detail v-bind="{ policy, coins, combined, openLink, ichra, dark }">
            <template v-slot:expansion="scope" v-if="$slots.expansion">
              <slot name="expansion" v-bind="scope"></slot>
            </template>
            <template v-slot:bottom v-if="$slots.bottom">
              <slot name="bottom"></slot>
            </template>
            <template v-slot:benefits>
              <slot name="benefits"></slot>
            </template>
          </policy-detail>

        </div>
      </common-dialog>
    </template>
  </div>
</template>

<script setup>
  import PolicyFace from 'components/enrollments/ichra/cards/PolicyFace.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PolicyDetail from 'components/enrollments/ichra/cards/PolicyDetail.vue';

  import {computed, ref, watch} from 'vue';
  import {getOop} from '../utils';
  import {_get} from 'symbol-syntax-utils';
  import {useAtcStore} from 'stores/atc-store';
  import {useMarketplace} from 'stores/marketplace';
  import {shopToHousehold} from 'components/market/shop/utils';

  const atcStore = useAtcStore()
  const marketStore = useMarketplace();

  const props = defineProps({
    place: Object,
    modelValue: { required: true },
    ichra: { required: false },
    dark: Boolean,
    enrollmentType: String,
    popup: { default: true },
    expansion: Boolean,
    expandAttrs: Object,
    aptc: Number,
    mult: Number,
    subsidy: Number,
    enrollment: Object,
    shop: Object
  })

  const open = ref(false);

  const setPolicy = ref({})
  const policy = computed(() => {
    return {
      ...props.modelValue,
      ...setPolicy.value,
      premium: props.modelValue?.premium || setPolicy.value?.premium
    }
  })
  const coins = computed(() => {
    return getOop({
      policy: policy.value,
      spend: props.ichra?.annualSpend,
      type: props.ichra?.type || props.enrollmentType
    });
  })

  const enrType = computed(() => props.ichra?.type || props.enrollmentType || 'family')

  const combined = computed(() => {
    return (policy.value?.premium || 0) * 12 + (coins.value || 0)
  })

  const openLink = (path, backup) => {
    const url = _get(policy.value, path) || _get(policy.value, backup);
    if (url) window.open(url, '_blank')
  }

  const getPolicy = async () => {
    const inStore = props.modelValue?._id ? props.modelValue : atcStore.get_state('marketplace', props.modelValue);
    if (inStore) setPolicy.value = inStore;
    else {
      const { place } = props.shop?.stats || {};
      const plans = await marketStore.find({
        query: {}, runJoin: {
          find_by_id: {
            id: props.modelValue._id,
            household: shopToHousehold(props.shop),
            place
          }
        }
      })
          .catch(err => {
            console.error(`Error getting policy: ${err.message}`)
            return { data: [] }
          })
      console.log('got plans', plans);
      setPolicy.value = plans.data[0];
      if(setPolicy.value) atcStore.set_get('marketplace', setPolicy.value._id, setPolicy.value);
    }
  }

  watch(() => props.modelValue, (nv) => {
    if (nv?._id && nv._id !== setPolicy.value._id) getPolicy();
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
