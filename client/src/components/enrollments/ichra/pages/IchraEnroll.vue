<template>
  <q-page class="_bg_g">
    <div class="__t row justify-center">
      <div class="_cent q-py-lg q-px-sm">
        <div class="font-1-1-4r tw-six">
          {{ plan?.name }}
        </div>
      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent pd3">

        <!--        <div class="tw-six font-1r">Your Insurance Policy</div>-->
        <div class="_fw">

          <policy-builder v-if="enrollment?._id" :enrollment="enrollment"></policy-builder>
          <div class="q-py-xl row justify-center" v-else>
            <q-spinner color="primary" size="40px"></q-spinner>
          </div>

        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import PolicyBuilder from 'components/enrollments/ichra/cards/PolicyBuilder.vue';

  import {idGet} from 'src/utils/id-get';
  import {useEnrollments} from 'stores/enrollments';
  import {computed, ref} from 'vue';
  import {useRoute} from 'vue-router';
  import {usePlans} from 'stores/plans';


  const enrollmentStore = useEnrollments();
  const planStore = usePlans();
  const route = useRoute();

  const { item: enrollment } = idGet({
    store: enrollmentStore,
    value: computed(() => route.params.enrollmentId),
    params: ref({ runJoin: { enrollment_person: true } })
  })

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => enrollment.value?.plan)
  })

</script>

<style lang="scss" scoped>
  .__title {
    font-size: 1rem;
  }

  .__t {
    background: linear-gradient(175deg, var(--q-p9), var(--q-p7), var(--q-p9));
    color: white;
  }
</style>
