<template>
  <q-page>
    <div class="row justify-center __bg_t">
      <div class="_cent __u">

        <!--    TOP SECTION - PLAN NAME & STATUS-->
        <div>
          <div class="tw-five text-a2 text-sm">{{ enrollment?.planYear }}
            {{ enrollment?.version?.split('_')[1] === '0' ? 'Open' : 'Special' }} Enrollment
          </div>
          <div class="text-md tw-five alt-font">{{ fullPlan?.name }}</div>
          <div class="flex items-center q-pt-sm" v-if="ec?.amount">
            <div class="text-xs tw-five">Employer Allowance:</div>
            <q-chip v-if="contributions?.employer?.cafe" color="accent" class="tw-five text-white">
              <span>{{ ec.type === 'flat' ? '' : 'Est. ' }}</span>
              {{ dollarString(contributions?.employer?.cafe, '$', 0) }} monthly
            </q-chip>
            <q-chip v-if="ec.type === 'percent'" color="p0" text-color="p9" class="tw-six">
              {{ $max(ec.amount, ec.family || 0) }}% {{ `of ${ ec.percentType === 'cost' ? 'plan cost' : `your ${ec.includeExtras ? 'total' : 'base'} pay`}` }}
            </q-chip>
          </div>
          <div class="flex items-center">
            <div class="text-xs tw-five">Status:&nbsp;</div>
            <status-chip :outline="false" class="tw-six" :model-value="enrollment?.status"></status-chip>
          </div>

        </div>


        <!--        TABS &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->
        <div class="_fw _oxs">
          <q-tabs no-caps align="left" v-model="tab" @update:model-value="setTab" class="__tabs"
                  inline-label>
            <q-tab icon="mdi-home" name=""></q-tab>
            <q-tab v-for="(t, i) in ['household', 'coverage', 'benefits'].filter(a => !sections[a].hide)"
                   :key="`t-${i}`"
                   :name="t">
              <span class="text-xxs tw-six">{{ sections[t].label }}</span>

            </q-tab>
          </q-tabs>
        </div>


        <!--        TABS-->


      </div>
    </div>

    <!--    ENROLLMENT NAVIGATION TABS-->
    <div style="z-index: 0" class="__eh _oh relative-position">
      <div class="row justify-center z5">
        <div class="_cent z5 pw2">


          <div class="__c" v-if="!login?._id || login.owner !== person?._id">
            <login-check v-if="!loading" :person-check="person"></login-check>
            <div v-else class="q-pa-lg">
              <q-spinner size="50px" color="primary"></q-spinner>
            </div>
          </div>
          <template v-else>
            <q-tab-panels
                class="_panel"
                :model-value="!tab"
                animated
                transition-next="slide-down"
                transition-prev="slide-up">
              <q-tab-panel :name="true" class="_panel">

                <div class="row">
                  <div class="col-12 col-md-8 q-pa-sm">
                    <div class="__c">
                      <review-card
                          v-bind="{
                        compliance,
                        coverages:
                        c$.data,
                        enrollment,
                        plan:fullPlan,
                        person
                      }"></review-card>
                    </div>
                    <div class="__c" v-for="(sec, i) in ['household', 'coverage', 'benefits']" :key="`sec-${i}`">
                      <div class="row items-center">
                        <div class="__t">{{ sections[sec].label }}</div>
                        <q-space></q-space>
                        <q-btn color="p9" flat dense icon="mdi-pencil-box" @click="setTab(sec)"></q-btn>
                      </div>

                      <div class="q-pa-md">
                        <component :is="sections[sec].mini" v-bind="sections[sec].miniAttrs"></component>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-4 q-pa-sm">
                    <div class="__cd q-my-xs">
                      <div class="row items-center">
                        <div class="__t">Your Income</div>
                        <q-space></q-space>
                        <q-btn color="a7" flat dense icon="mdi-eye" @click="setTab('income')"></q-btn>
                      </div>
                      <household-tax v-bind="{ person, plan:fullPlan, toggle: true, enrollment }"></household-tax>
                    </div>
                    <div class="__cd q-my-xs">
                      <div class="row items-center">
                        <div class="__t">Plan Docs</div>
                        <q-space></q-space>
                        <q-btn color="a7" flat dense icon="mdi-eye" @click="setTab('docs')"></q-btn>
                      </div>
                      <docs-list :plan="fullPlan"></docs-list>
                    </div>
                  </div>

                </div>

              </q-tab-panel>
              <q-tab-panel :name="false" class="bg-white pw2 q-py-lg">
                <q-tab-panels v-model="tab" class="_panel" animated transition-prev="jump-right"
                              transition-next="jump-left">
                  <q-tab-panel class="_panel" v-for="(section, i) in Object.keys(sections)" :key="`section-${i}`"
                               animated
                               :name="section">
                    <component v-if="!sections[section].hide" :is="sections[section].component"
                               v-bind="sections[section].attrs"></component>
                    <div v-else class="q-pa-lg text-italic">Enrollment Complete - this section is not accessible</div>
                  </q-tab-panel>
                </q-tab-panels>
              </q-tab-panel>
            </q-tab-panels>

          </template>

        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import LoginCheck from 'components/enrollments/cards/LoginCheck.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';
  import ReviewCard from 'components/enrollments/cards/ReviewCard.vue';
  import DocsList from 'components/plans/docs/cards/DocsList.vue';
  import HouseholdTax from 'components/households/cards/HouseholdTax.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import {usePlans} from 'stores/plans';
  import {loginPerson} from 'stores/utils/login';
  import {useRoute, useRouter} from 'vue-router';
  import {usePpls} from 'stores/ppls';
  import {$max, dollarString} from 'src/utils/global-methods';
  import {enrollmentSections} from 'components/enrollments/utils';
  import {useShops} from 'stores/shops';
  import {useHouseholds} from 'stores/households';

  const { login } = loginPerson()

  const route = useRoute();
  const router = useRouter();

  const eStore = useEnrollments();
  const planStore = usePlans();
  const pplsStore = usePpls();
  const shopStore = useShops();
  const hhStore = useHouseholds();

  const { item: enrollment } = idGet({
    value: computed(() => route.params.enrollId),
    store: eStore,
    refresh: true,
    params: ref({ runJoin: { enrollment_rates: true, enrollee_points: true } })
  })
  const { item: fullPlan } = idGet({
    value: computed(() => enrollment.value?.plan),
    store: planStore
  })
  const { item: person } = idGet({
    value: computed(() => enrollment.value?.person),
    store: pplsStore
  })
  const { item:shop } = idGet({
    store: shopStore,
    value: computed(() => enrollment.value.shop)
  })

  const { compliance, sections, contributions, camsData, c$ } = enrollmentSections({
    enrollment,
    plan: fullPlan,
    person,
    shop
  })

  const ec = computed(() => {
    const cont = fullPlan.value?.employerContribution || {};
    return cont['*'] || cont[enrollment.value?.group] || { amount: 0 }
  })

  const loading = ref(true);
  const tab = ref('');

  const setTab = (val) => {
    tab.value = val;
    const { href } = router.resolve({ ...route, params: { tab: val } });
    window.history.pushState({}, '', href);
  }

  watch(camsData, (nv) => {
    if (nv?.total) loading.value = false
  }, { immediate: true })
  watch(enrollment, (nv, ov) => {
    if(nv && nv._id !== ov?._id){
      if(nv.status === 'review') eStore.patch(nv._id, { status: 'complete'})
    }
  }, { immediate: true })

  watch(person, async (nv) => {
    if(nv?._id && !nv.household){
      const hh = await hhStore.create({ person: nv._id })
      pplsStore.patchInStore(nv._id, { household: hh._id })
    }
  }, { immediate: true })

  onMounted(() => {
    if (route.params.tab) tab.value = route.params.tab
  })
</script>

<style lang="scss" scoped>
  .__bg_t {
    color: white;
    background: linear-gradient(200deg, var(--q-a9), var(--q-accent));

    .__u {
      padding: max(6vh, 70px) max(2vw, 20px);
      position: relative;
    }
  }

  .__bg {
    background: radial-gradient(var(--q-p3) -10%, transparent 50%);
  }

  .__eh {
    position: relative;
    min-height: 75vh;
    background: linear-gradient(165deg, var(--q-a1), var(--q-a0));
  }

  .__tit {
    padding: 5vh 4vw 2vh 4vw;
  }

  .__b, .__tabs {
    position: absolute;
    bottom: 0;
    left: 2vw;
  }

  .__tabs {
    right: 2vw;
    background: linear-gradient(90deg, var(--q-accent), transparent);
    border-radius: 12px 12px 0 0;
    //color: white;
    //background: var(--q-p9);
  }

  .__tab {
    background: white;
  }


  .__c, .__cd {
    border-radius: 12px;
    box-shadow: 0 2px 10px -4px #d6d6d6;
    background: white;
    padding: 20px 15px;
    width: 100%;
    overflow: scroll;
    margin: 15px 0;
  }

  .__cd {
    //background: linear-gradient(200deg, var(--q-a9), var(--q-a6)) !important;
    background: rgba(255, 255, 255, .5) !important;
    //background: linear-gradient(45deg, rgba(0,0,0,.5), var(--q-a9)) !important;;
  }

  .__block {
    border-radius: 12px;
    background: white;
    padding: 20px 15px;
  }

  .__t {
    font-size: 1rem;
    font-weight: 600;
  }

  .__bmp {
    padding-top: 2px;
  }
</style>
