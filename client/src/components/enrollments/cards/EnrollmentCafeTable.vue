<template>
  <div class="_fw">
    <table>
      <tbody>
      <tr>
        <th>Plan</th>
        <th>Monthly Election</th>
        <th v-if="showCompliance">Compliance</th>
      </tr>
      <tr v-for="(k, i) in Object.keys(er?.cafe || {})" :key="`cafe-${i}`">
        <td>{{cafeKeys[k].name}}</td>
        <td v-if="compliance">{{compliance[k]?.display || 'N/A'}}</td>
        <td v-else-if="!_get(er, `cafe.${k}.optOut`)">{{dollarString(_get(er, `cafe.${k}.amount`, 0), '$', 0)}}</td>
        <td v-else>N/A</td>
        <td v-if="showCompliance">
          <span v-if="compliance" v-html="(compliance[k]?.compliance || '').split(' ')[0]">
          </span>
          <q-tooltip v-if="compliance" class="tw-six">{{compliance[k]?.compliance}}</q-tooltip>

        </td>
      </tr>
      <tr>
        <td class="tw-six">Total Election</td>
        <td class="tw-six text-primary">{{dollarString(total, '$', 0)}}/Mo</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>

import {cafeKeys} from 'components/plans/utils';
import {_get} from 'symbol-syntax-utils';
import {dollarString} from 'src/utils/global-methods';
import {idGet} from 'src/utils/id-get';
import {computed} from 'vue';
import {useEnrollments} from 'stores/enrollments';

const store = useEnrollments();

const props = defineProps({
  enrollment: { type: Object },
  coverages: Array,
  compliance: Object,
  showCompliance: Boolean
})

const { item:er } = idGet({
  store,
  value: computed(() => props.enrollment)
})

const total = computed(() => {
  let t = 0;
  for(const k in er.value?.cafe){
    const o = er.value.cafe[k];
    if(!o.optOut) t += o.amount || 0;
  }
  return t;
})

</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    font-size: 1rem;
    border-collapse: collapse;
    th, td {
      padding: 5px 10px;
      text-align: left;
    }
    td {
      border-top: solid .2px #999;
    }
  }
</style>
