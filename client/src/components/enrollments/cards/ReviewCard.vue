<template>
  <div class="_fw q-py-md">
    <div class="_form_grid">
      <div class="_form_label">Location</div>
      <div class="q-pa-sm">
        <div class="flex items-center">
          <q-icon class="q-mr-sm" size="20px" name="mdi-map-marker" color="red"></q-icon>
          <div class="font-1r tw-six text-grey-8 flex items-center cursor-pointer" @click="settingAddress = true">
            <div>Your zip code:&nbsp;</div>
            <div class="text-grey-10">
              <b>{{ fulle?.address?.postal }}</b>
            </div>
          </div>
        </div>
        <div class="q-pa-sm" v-if="multiFips?.length">
          <div class="font-3-4r">Your County</div>
          <q-chip color="transparent" clickable>
            <span class="q-mr-sm">{{ fulle?.county?.name }}</span>
            <q-icon name="mdi-menu-down" color="blue"></q-icon>
            <q-menu>
              <div class="_fw mw300">
                <q-list separator>
                  <q-item v-for="(item, i) in multiFips" :key="`co-${i}`" clickable @click="chooseCounty(item)">
                    <q-item-section>
                      <q-item-label>{{ item.name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </q-chip>
        </div>
        <q-slide-transition>
          <div v-if="settingAddress" class="_fw">
            <tomtom-autocomplete :model-value="fulle.address" @update:model-value="setAddress" :use-tomtom-geocode="useTomtomGeocode"></tomtom-autocomplete>
          </div>
        </q-slide-transition>
      </div>

      <div class="_form_label">Cost Summary</div>
      <div class="q-pa-sm">
        <income-summary v-bind="{ enrollment, plan, person:prsn }"></income-summary>
      </div>

      <div class="_form_label">
        Compliance
      </div>

      <div v-if="checksNotPassed?.filter(a => !a.warn).length" class="q-py-md q-px-sm">
        <div class="tw-six text-grey-8 q-pt-md q-px-sm">⛔ You're not quite finished</div>
        <ul>
          <li class="font-1r cursor-pointer" v-for="(check, i) in checksNotPassed" :key="`chk-${i}`" v-html="check.text"
              @click="check.click ? check.click() : openReview">
          </li>
        </ul>
      </div>
      <div v-else-if="!['complete', 'review'].includes(fulle?.status)">
        <template v-if="checksNotPassed?.length">
          <div class="tw-six text-grey-8 q-pt-md q-px-sm">⚠️ Things to consider</div>
          <ul>
            <li class="font-1r cursor-pointer" v-for="(check, i) in checksNotPassed" :key="`chk-warn-${i}`" v-html="check.text"
                @click="check.click ? check.click() : openReview">
            </li>
          </ul>
        </template>

        <div class="q-pa-sm text-italic">No Compliance Stops</div>
        <div v-if="!confirm" class="row items-center">
          <q-btn no-caps class="tw-six" flat @click="confirm = true">
            <q-icon name="mdi-check" color="green" class="q-mr-sm"></q-icon>
            <span>I'm Finished - Close Enrollment</span>
          </q-btn>
        </div>

        <q-slide-transition>
          <div class="_fw q-pa-md" v-if="confirm">

            <div class="tw-six font-1r text-grey-8">Are you sure you want to finish enrolling?</div>
            <div class="row items-center q-py-sm">
              <q-btn flat class="q-mr-md" no-caps @click="confirm = false">
                <span class="q-mr-sm">Cancel</span>
                <q-icon color="red" name="mdi-close"></q-icon>
              </q-btn>
              <q-btn @click="completeEnrollment" push class="_p_btn tw-six" dark icon-right="mdi-check" no-caps
                     label="Confirm Finish"></q-btn>
            </div>
          </div>
        </q-slide-transition>
      </div>
      <div v-else>
        <div class="font-1r text-grey-8 tw-six flex items-center q-pa-md">
          <q-icon name="mdi-checkbox-marked" class="q-mr-sm" color="green" size="20px"></q-icon>
          <div>Enrollment Complete</div>
        </div>
      </div>


    </div>


  </div>
</template>

<script setup>
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import IncomeSummary from 'components/households/cards/IncomeSummary.vue';

  import {computed, ref, watch} from 'vue';
  import {SessionStorage} from 'symbol-auth-client';
  import {$errNotify, dollarString} from 'src/utils/global-methods';
  import {useTomtomGeocode} from 'stores/tomtom-geocode';
  import {useEnrollments} from 'stores/enrollments';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {_get} from 'symbol-syntax-utils';
  import {useRoute, useRouter} from 'vue-router';
  import {useCoverages} from 'stores/coverages';

  const route = useRoute();
  const router = useRouter();

  const pplsStore = usePpls();
  const eStore = useEnrollments();
  const junkStore = useJunkDrawers();
  const coverageStore = useCoverages();

  const props = defineProps({
    person: { required: true },
    plan: { required: true },
    enrollment: { required: true },
    coverages: { required: true },
    compliance: { required: true },
  })

  const fullPlan = computed(() => props.plan || {})

  const { item: fulle } = idGet({
    store: eStore,
    value: computed(() => props.enrollment)
  })

  const { item: prsn } = idGet({
    store: pplsStore,
    value: computed(() => fulle.value?.person)
  })


  const shopCoverages = ref([])
  watch(fulle, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      const data = await coverageStore.find({
        query: {
          $limit: 1,
          _id: { $in: Object.keys(nv.coverages || {}) },
          shop: true
        }
      })
      if (data.total) shopCoverages.value = data.data;
    }
  }, { immediate: true });

  const openReview = () => {
    const { href } = router.resolve({ ...route, params: { ...route.params, tab: 'benefits' } })
    window.open(href, '_self')
  }

  const confirm = ref(false);

  const checks = computed(() => {
    const er = SessionStorage.getItem(`er_benefit:${props.plan._id}`);
    const ec = SessionStorage.getItem(`elected:${fulle.value?._id}`);
    const complianceOnList = Object.keys(props.compliance || {}).filter(a => (fullPlan.value.cafe || {})[a]?.active && !_get(fulle.value, `cafe[${a}].acknowledgements`)?.length && (props.compliance)[a].priority < 3)
    const employerContribution = props.plan?.employerContribution || {}
    return {
      compliance: {
        text: `The following plan benefits have compliance messages that need to be reviewed: <b>${complianceOnList.map(a => props.compliance[a].label).join(', ')}</b>`,
        on: complianceOnList?.length
      },
      spend: {
        text: `You have not allocated your employer provided benefit. Your employer offers ${dollarString(er, '$', 0)} ${employerContribution.type === 'percent' ? `(estimated ${employerContribution.amount}% of ${employerContribution.percentType === 'cost' ? 'plan cost' : 'your pay'}` : ''} - you only allocated ${dollarString(ec, '$', 0)}`,
        on: (er || 0) > (ec || 1),
        warn: true
      },
      ichra: {
        text: `You have not selected individual coverage options for the following coverages: ${shopCoverages.value.map(a => a.name).join(', ')}`,
        on: shopCoverages.value.filter(a => {
          const config = (props.enrollment || {}).coverages[a._id] || {};
          return !config.optOut && !(config.individual_coverage || config.policy);
        }).length > 0
      }
    }
  })

  const checksNotPassed = computed(() => Object.keys(checks.value).map(a => checks.value[a]).filter(a => !!a.on));

  const completeEnrollment = () => {
    const patchObj = { $set: { status: 'complete' } }
    eStore.patchInStore(fulle.value._id, patchObj)
    eStore.patch(fulle.value._id, patchObj)
        .catch(err => $errNotify(`Error completing - try again: ${err.message}`));
  }

  const settingAddress = ref(false);
  const multiFips = ref([]);

  const setAddress = async (address) => {
    const query = { itemId: `zips|${address.postal.slice(0, 3)}` }
    let inStore = junkStore.findInStore({ query });
    if (!inStore.total) inStore = await junkStore.find({ query });
    const { data } = inStore.data[0];
    const zipData = data[address.postal];
    if (zipData.counties.length > 1) multiFips.value = zipData.counties.map((a, i) => {
      return {
        name: a,
        fips: zipData.all_fips[i],
        state: zipData.state
      }
    })
    const county = {
      fips: zipData.fips,
      name: zipData.county,
      stateCode: zipData.state
    }
    eStore.patchInStore(fulle.value._id, { address, county })
    await eStore.patch(fulle.value._id, { address, county });
    const pAddress = prsn.value?.address;
    if (!pAddress) {
      await pplsStore.patch(prsn.value._id, { address });
    } else {
      let run = true;
      for (const addr of prsn.value?.addresss) {
        if (addr.latitude && addr.latitude === address.latitude) run = false;
      }
      if (run) await pplsStore.patch(prsn.value._id, { $addToSet: { addresses: address } });
      settingAddress.value = false
    }
  }

  const chooseCounty = async (county) => {
    eStore.patchInStore(fulle.value._id, { county })
    await eStore.patch(fulle.value._id, { county });
  }

  watch(prsn, (nv) => {
    if (nv?.address && !fulle.value?.address) {
      setAddress(nv?.address);
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
</style>
