<template>
  <div class="_fw">
    <div class="tw-six text-grey-8 font-1r">{{coverage?.name || 'Coverage'}} Coverage Change</div>
    <div class="text-grey-8 font-1r">{{$possiblyPlural('Person', diff.amt)}} {{diff.word}}</div>
    <div class="q-py-md row items-center">
      <member-chip v-for="(p, i) in diff.list" :key="`p-${i}`" :model-value="p" show-age></member-chip>
    </div>
  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useCoverages} from 'stores/coverages';
  import {computed} from 'vue';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import MemberChip from 'components/households/cards/MemberChip.vue';

  const cStore = useCoverages();

  const props = defineProps({
    modelValue: Object,
    path: String
  })

  const { item:coverage } = idGet({
    store: cStore,
    value: computed(() => props.path?.split('.').slice(1)[0])
  })

  const diff = computed(() => {
    const { newVal, oldVal } = props.modelValue || { newVal: [] }
    const l = newVal?.length || 0;
    const ol = oldVal?.length || 0;
    let word = 'Removed';
    const list = [];
    if(l > ol){
      word = 'Added'
      for(const p of newVal){
        if(!oldVal?.includes(p)) list.push(p);
      }
    } else {
      for(const p of oldVal){
        if(!newVal?.includes(p)) list.push(p);
      }
    }
    return {
      list,
      word,
      amt: Math.abs(l - ol)
    }
  })
</script>

<style lang="scss" scoped>

</style>
