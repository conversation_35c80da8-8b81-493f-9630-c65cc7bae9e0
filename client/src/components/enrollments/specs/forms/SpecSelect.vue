<template>
  <div class="_fw">
    <div class="flex items-center">
      <q-chip color="white" class="tw-six text-p6" :label="enrollment?.version.split('_').join(' - ')" clickable @click="active = 'base'"></q-chip>
      <template v-if="active !== 'base'">
        <q-icon name="mdi-chevron-right"></q-icon>
        <q-chip color="white" class="tw-six text-p6" :label="qEvents[active]?.name || active"></q-chip>
      </template>

    </div>
    <div class="font-1r">
      <q-tab-panels animated transition-next="jump-down" transition-prev="jump-up" v-model="active" class="_panel">
        <q-tab-panel name="base" class="_panel">
          <div>What kind of event is prompting this change?</div>

          <div class="__cg">
            <div class="__c" v-for="(cat, i) in Object.keys(qEvents)" :key="`cat-${i}`" @click="active = cat">
              <div class="font-1-1-4r tw-six text-p6 alt-font">{{qEvents[cat].name}}</div>
              <div class="font-1r">{{qEvents[cat].description}}</div>
            </div>
          </div>
        </q-tab-panel>
        <q-tab-panel v-for="(cat, i) in Object.keys(qEvents)" :key="`pan-${i}`" :name="cat" class="_panel">
          <component
              @update:model-value="submit"
              :is="qEvents[cat].component"
              v-bind="{...qEvents[cat].attrs || {}, enrollment, household: hh, evt: cat }"
          ></component>
        </q-tab-panel>
      </q-tab-panels>

    </div>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import { qEvents } from 'components/enrollments/specs/utils';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {useEnrollments} from 'stores/enrollments';

  const hhStore = useHouseholds();
  const eStore = useEnrollments();

  const emit = defineEmits(['close']);
  const props = defineProps({
    enrollment: Object
  })

  const { item:er } = idGet({
    value: computed(() => props.enrollment),
    refreshWhen: computed(() => !props.enrollment?._fastjoin?.person),
    store: eStore,
    params: ref({ runJoin: { spec_person: true }})
  })

  const person = computed(() => er.value?._fastjoin?.person)

  const { item:hh } = idGet({
    value: computed(() => person.value?.household),
    store: hhStore
  })


  const active = ref('base');
  const submit = () => {
    active.value = 'base';
    emit('close');
  }
</script>

<style lang="scss" scoped>
  .__cg {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: 50% 50%;
    grid-gap: 10px;
    padding: 20px 0;
  }
  .__c {
    padding: 20px;
    border-radius: 8px;
    background:  white;
    box-shadow: 0 3px 9px -3px #999;
    cursor:pointer;
  }

  @media screen and (max-width: 799px) {
    .__cg {
      grid-template-columns: 100%;
    }
  }
</style>
