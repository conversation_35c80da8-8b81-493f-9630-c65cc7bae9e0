<template>
  <div class="_fw">
    <div class="q-pa-sm font-1r">Find eligible group members who have not been invited to enroll in {{fullPlan.name}}</div>
    <q-input dense filled v-model="search.text" placeholder="Search..." class="q-my-sm">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
    <div class="q-py-sm">
      <version-picker @update:model-value="toggleVersion" :model-value="versions" :plan="fullPlan" multiple></version-picker>
    </div>

    <q-slide-transition>
      <div class="_fw" v-if="selected?.length">
        <q-btn v-if="!confirming" no-caps push class="_p_btn tw-six" @click="confirming = true" :label="`Invite ${$possiblyPlural('person', selected.length)} to enroll`"></q-btn>
        <div class="flex items-center" v-else>
          Confirm send invites to enroll to {{$possiblyPlural('person', selected.length)}}
          <q-btn flat label="Cancel" icon="mdi-cancel" @click="confirming = false"></q-btn>
          <q-btn flat label="Send" icon="mdi-check" @click="confirm"></q-btn>
        </div>
      </div>
    </q-slide-transition>

    <q-table
        :rows="rows"
        :columns="columns"
    >
      <template v-slot:top v-if="loading">
        <div class="flex items-center">
          <q-spinner color="primary" size="30px"></q-spinner>
          <q-chip color="transparent" label="processing..."></q-chip>
        </div>
      </template>
      <template v-slot:header="scope">
        <q-th auto-width>
          <q-checkbox
              :model-value="selected?.length === pageRecordCount"
              @update:model-value="toggleAll"
          ></q-checkbox>
        </q-th>
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope">
          <q-td>
            <q-checkbox
                @update:model-value="toggleSelected(scope.row)"
                :model-value="isSelected(scope.row)"
            ></q-checkbox>
          </q-td>
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component :is="col.component" v-bind="col.attrs(scope.row)"></component>
          </q-td>
        </q-tr>
      </template>
      <template v-slot:bottom>
        <div class="row items-center _fw">
              <span>{{
                  ((pagination.currentPage * limit) - limit) + 1
                }} - {{ pageRecordCount }} of {{ h$.total }}</span>
          <q-space></q-space>
          <q-pagination
              @update:model-value="h$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup>
  import VersionPicker from 'components/enrollments/forms/VersionPicker.vue';
  import TdChip from 'components/common/tables/TdChip.vue';
  import PayChip from 'components/comps/cards/PayChip.vue';
  import HireDate from 'components/comps/cams/cards/HireDate.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {enrollmentsByYear} from 'src/components/enrollments/admin/utils';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {usePpls} from 'stores/ppls';
  import {$errNotify, $possiblyPlural} from 'src/utils/global-methods';
  import {useEnrollments} from 'stores/enrollments';
  import { getCurrentPlanYear } from 'components/plans/utils';

  const pplsStore = usePpls();
  const planStore = usePlans();
  const enrollmentsStore = useEnrollments();

  const props = defineProps({
    plan: { required: true }
  })
  const yr = new Date().getFullYear();

  const versions = ref([]);
  const loading = ref(false);

  const { item: fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore,
  })
  const loadedPlan = ref({});
  const params = ref({ runJoin: { no_enrollments: versions.value } });

  const no_enrollment = computed(() => loadedPlan.value?.no_enrollment || []);

  const { search, searchQ } = HQuery({});
  const limit = ref(25);
  const { h$, pagination } = HFind({
    store: pplsStore,
    limit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: no_enrollment.value.map(a => a.person)}
        }
      }
    })
  })

  const pageRecordCount = computed(() => {
    return Math.min(h$.total, pagination.value.currentPage * limit.value)
  });
  const selected = ref([]);
  const selectedIds = computed(() => selected.value?.map(a => a._id));

  const confirming = ref(false);
  const confirm = async () => {
    loading.value = true;
    const { org, _id} = fullPlan.value;
    const getGroup = (v) => {
      return (v.inGroups || []).filter(a => (fullPlan.value.groups || []).includes(a))[0];
    }
    const participants = selected.value.map(a => {
      return {
        group: getGroup(a),
        cams: a._fastjoin.cams._id
      }
    })
    const version = `${getCurrentPlanYear(fullPlan.value)}_0`;
    await enrollmentsStore.create({ plan: _id, version, org, participants, _special: true })
        .catch(err => $errNotify(`Error enrolling, try again: ${err.message}`))
    loading.value = false;
  }

  const rows = computed(() => {
    return h$.data.map(a => {
      return {
        ...a,
        '_fastjoin': { ...a._fastjoin, cams: no_enrollment.value.filter(b => b.person === a._id)[0]}
      }
    })
  })
  const isSelected = (val) => {
    return selectedIds.value?.includes(val._id);
  }
  const toggleSelected = (val) => {
    const idx = selectedIds.value.indexOf(val._id);
    if (idx > -1) selected.value.splice(idx, 1);
    else selected.value.push(val);
  }

  const toggleAll = (val) => {
    if (!val) selected.value = [];
    else selected.value = [...rows.value];
  }

  const enrollments = computed(() => enrollmentsByYear(fullPlan.value));

  const go = async () => {
    if (versions.value.length){
      loading.value = true;
      loadedPlan.value = await planStore.get(fullPlan.value._id, { ...params.value })
      loading.value = false;
    }
  }

  const timeout = ref(undefined);
  const toggleVersion = (val) => {
    versions.value = val;
    loading.value = true;
    if(timeout.value) window.clearTimeout(timeout.value);
    timeout.value = window.setTimeout(() => {
      go()
    }, 2000)
  }

  watch(fullPlan, (nv, ov) => {
    if (nv?._id && nv._id !== ov?._id) {
      for (const subK in enrollments.value[yr] || {}) {
          versions.value.push(`${yr}_${subK}`);
      }
      go()
    }
  }, { immediate: true })

  const columns = computed(() => {
    return [
      {
        name: 'Person',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: row.name
            }
          }
        }
      },
      {
        name: 'Role',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: row._fastjoin?.cams?.name
            }
          }
        }
      },
      {
        name: 'Pay',
        component: PayChip,
        attrs: (row) => {
          return {
            service: 'cams',
            modelValue: row._fastjoin?.cams
          }
        }
      },
      {
        name: 'Hire Date',
        component: HireDate,
        attrs: (row) => {
          return {
            edit: true,
            modelValue: row._fastjoin?.cams
          }
        }
      },
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })
</script>

<style lang="scss" scoped>

</style>
