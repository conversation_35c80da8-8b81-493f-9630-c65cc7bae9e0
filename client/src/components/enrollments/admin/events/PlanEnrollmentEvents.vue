<template>
  <div class="_fw">
    <q-table
        class="bg-transparent"
        flat
        hide-pagination
        :columns="cols"
        :rows="Object.keys(enrollment || {}).map(a => getRow(a))"
    >

      <template v-slot:no-data>
        <div class="q-pa-sm text-italic font-7-8r">
          No enrollments created yet
        </div>
      </template>
      <template v-slot:header="scope">
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope" @click="select(scope.row.subK)">
          <!--                  <q-td>-->
          <!--                    <q-btn dense flat size="sm"-->
          <!--                           icon="mdi-dots-horizontal">-->
          <!--                    </q-btn>-->
          <!--                  </q-td>-->
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component :is="col.component" v-bind="col.attrs(scope.row)"></component>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>

<script setup>
  import TdChip from 'components/common/tables/TdChip.vue';

  import {computed, ref} from 'vue';
  import {getCurrentPlanYear} from 'components/plans/utils';

  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {enrollmentsByYear} from 'components/enrollments/admin/utils';
  import {formatDate, isBetweenDates} from 'src/utils/date-utils';
  import {$limitStr, dollarString} from 'src/utils/global-methods';
  const planStore = usePlans();

  const emit = defineEmits(['update:selected']);
  const props = defineProps({
    plan: { required: true },
    year: { required: false }
  })

  const selected = ref('');
  const select = (val) => {
    selected.value = `${String(yr.value)}_${String(val)}`
    emit('update:selected', selected.value);
  }

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  })

  const enrollment = computed(() => {
    if (!fullPlan.value?.enrollments) return {}
    const e = enrollmentsByYear(fullPlan.value);
    if (!e) return {};
    return e[yr.value];
  })

  const isOpen = computed(() => {
    const obj = {};
    const time = new Date().getTime();
    for (const k in fullPlan.value?.enrollments || {}) {
      const { open, close } = fullPlan.value.enrollments[k] || { close: undefined }
      obj[k] = { open: false, close: false }
      obj[k].open = isBetweenDates(new Date(), open, close)
      obj[k].close = open && (!close || new Date(close).getTime() <= time)
    }
    return obj;
  })

  const getRow = (subK) => {
    return {
      ...enrollment.value[subK],
      subK
    }
  }

  const yr = computed(() => String(props.year || getCurrentPlanYear(fullPlan.value)) || Object.keys(fullPlan.value.enrollments || {})[0]);

  const cols = computed(() => {
    return [
      {
        name: 'Event',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              class: 'text-p9 tw-six',
              label: `${yr.value} - ${row.subK}`
            }
          }
        }
      },
      {
        name: 'Open',
        component: TdChip,
        attrs: (row) => {
          const { open, close } = isOpen.value[`${selected.value}`] || {};
          return {
            chipAttrs: {
              class: 'tw-six',
              label: row.open ? formatDate(row.open, 'MM/DD/YYYY') : 'Not Set',
              color: open ? 'green-7' : close ? 'grey-6' : 'blue',
              outline: true,
            }
          }
        }
      },
      {
        name: 'Close',
        component: TdChip,
        attrs: (row) => {
          const { open, close } = isOpen.value[`${selected.value}`] || {};
          return {
            chipAttrs: {
              class: 'tw-six',
              label: row.close ? formatDate(row.close, 'MM/DD/YYYY') : 'Not Set',
              color: open ? 'green-10' : close ? 'grey-6' : 'blue-10',
              outline: true,
            }
          }
        }
      },
      {
        name: 'Description',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              label: $limitStr(row.description, 35, '...'),
              color: 'white'
            }
          }
        }
      },
      {
        name: 'Enrolled',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              label: row.active ? dollarString(row.enrolled, '', 0) : 'Not Active',
              color: row.active ? 'white' : 'red',
              outline: !row.active
            }
          }
        }
      }
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })

</script>

<style lang="scss" scoped>

</style>
