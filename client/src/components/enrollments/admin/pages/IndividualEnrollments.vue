<template>
  <div class="_fw q-py-md">


        <plan-enrollment-table :query="query" :plan="fullPlan">
          <template v-slot:filter>
            <!--        VERSION FILTER-->
            <div class="row items-center q-py-sm">
              <version-picker v-model="filters.version" :plan="fullPlan" multiple></version-picker>
            </div>
            <!--         STATUS FILTER-->
            <div class="row items-center q-py-sm">
              <q-chip clickable class="tw-six" color="white">
                <q-icon color="primary" name="mdi-filter"></q-icon>
                <span>Status</span>
                <q-icon color="primary" name="mdi-menu-down"></q-icon>

                <q-menu>
                  <q-list separator>
                    <q-item v-for="(st, i) in statuses" :key="`status-${i}`" clickable @click="toggleFilter(st, 'status')">
                      <q-item-section>
                        <q-item-label>{{ st.split('_').join(' ') }}</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-icon v-if="filters.status.includes(st)" name="mdi-checkbox-marked-outline"
                                color="green"></q-icon>
                        <q-icon v-else name="mdi-checkbox-blank-outline" color="grey-4"></q-icon>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>

              </q-chip>
              <q-chip
                  v-for="(s, i) in filters.status"
                  :key="`s-${i}`"
                  color="white"
                  :label="s.split('_').join(' ')"
                  removable @remove="filters.status.splice(i, 1)" icon-remove="mdi-close">
              </q-chip>
            </div>
          </template>
        </plan-enrollment-table>




  </div>
</template>

<script setup>
  import PlanEnrollmentTable from 'components/enrollments/admin/cards/PlanEnrollmentTable.vue';
  import VersionPicker from 'components/enrollments/forms/VersionPicker.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useGroups} from 'stores/groups';
  import {statuses} from 'src/components/enrollments/admin/utils';

  const planStore = usePlans();

  const groupStore = useGroups();

  const props = defineProps({
    plan: { required: true },
    event: { required: false }
  })
  const { item: fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore
  })

  const groupIds = computed(() => fullPlan.value?.groups || []);

  const filters = ref({
    version: [],
    status: [],
    group: []
  })

  const toggleFilter = (k, path) => {
    const idx = filters.value[path].indexOf(k);
    if (idx > -1) filters.value[path].splice(idx, 1);
    else filters.value[path].push(k);
  }

  const query = computed(() => {
    const q = { $sort: { version: -1 } };
    for (const k in filters.value) {
      if (filters.value[k]?.length) q[k] = { $in: filters.value[k] }
    }
    return q;
  })

  const groups = ref({ total: 0, data: [] });

  watch(groupIds, async (nv, ov) => {
    if (nv && nv.length !== ov?.length) {
      groups.value = await groupStore.find({ query: { _id: { $in: nv } } })
    }
  }, { immediate: true })

  watch(() => props.event, (nv, ov) => {
    if (nv && nv !== ov) filters.value.version = [nv];
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
