<template>
  <div class="_fw">

    <q-tab-panels class="_panel" animated :model-value="!!selected">
      <q-tab-panel class="_panel" :name="false">

        <div class="row items-center q-pa-sm">
          <div class="tw-six font-1r">Manage Enrollments</div>
          <q-btn dense flat icon="mdi-plus" color="primary" no-caps @click="adding = year || yr">
          </q-btn>
        </div>

        <q-chip class="tw-six" color="transparent" clickable icon-right="mdi-menu-down"
                :label="year ? `Plan Year:  ${year}` : 'Select Year'">
          <q-menu class="w200 bg-white">
            <q-list>
              <q-item-label header v-if="!Object.keys(enrollments || {}).length">No Enrollments Added</q-item-label>
              <q-item v-for="(k, i) in Object.keys(enrollments || {})" :key="`k-${i}`" clickable @click="setEvent(k)">
                <q-item-section>
                  <q-item-label>{{ k }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-chip>

        <q-tab-panels class="_panel" animated transition-next="slide-up" transition-prev="slide-down"
                      :model-value="!!adding">
          <q-tab-panel class="_panel" :name="true">


            <div class="w400 mw100 q-py-md">
              <div class="q-pa-sm tw-six text-primary font-1r">Add Enrollment</div>
              <q-checkbox :model-value="year === yr" :label="String(yr)" @update:model-value="year = yr"></q-checkbox>
              <q-checkbox :model-value="year === (yr + 1)" :label="String(Number(yr+1))"
                          @update:model-value="year=yr+1"></q-checkbox>
              <div class="row items-center justify-end">
                <q-btn no-caps @click="adding = 0" flat>
                  <span>Cancel</span>
                  <q-icon class="q-ml-sm" name="mdi-close" color="red"></q-icon>
                </q-btn>
                <q-btn
                    flat
                    no-caps
                    @click="addNextKey(year)">
                  <span>Add Enrollment {{ nextKey(year)?.split('_').join(' - ') }}</span>
                  <q-icon class="q-ml-sm" name="mdi-check" color="green"></q-icon>
                </q-btn>
              </div>
            </div>


          </q-tab-panel>
          <q-tab-panel class="_panel" :name="false">

            <plan-enrollment-events
                :year="year"
                :plan="fullPlan"
                :selected="selected"
                @update:selected="setEvent"
            ></plan-enrollment-events>

          </q-tab-panel>
        </q-tab-panels>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">


        <q-tab-panels class="_panel" :model-value="editing" animated transition-next="slide-right"
                      transition-prev="slide-left">
          <q-tab-panel class="_panel" :name="true">
            <div class="q-py-sm">
              <q-chip color="transparent" clickable @click="editing = false">
                <q-icon name="mdi-close" color="red"></q-icon>
                <span class="q-ml-sm">Done Editing</span>
              </q-chip>
            </div>
            <q-separator class="q-my-sm"></q-separator>
            <enrollment-form :plan-key="selected" :plan="plan"></enrollment-form>
          </q-tab-panel>
          <q-tab-panel class="_panel" :name="false">
            <div class="q-py-sm">
              <q-chip color="transparent" clickable @click="setEvent()">
                <q-icon name="mdi-chevron-left" color="primary"></q-icon>
                <span class="q-ml-sm">All Enrollments</span>
              </q-chip>
            </div>

            <div class="row q-pb-md">
              <div class="__top_box relative-position">
                <q-btn class="t-r" dense flat size="sm" icon="mdi-pencil-box" @click="editing = true"></q-btn>
                <div class="font-7-8r alt-font">Event <b>{{ selected.split('_').join(' - ') }}</b> &nbsp; Type:
                  <b>{{ enrollment.open_enroll ? 'Open' : 'Special' }} Enrollment</b>
                </div>
                <div class="font-7-8r alt-font">
                  Enrollment Period: <b>{{ formatDate(enrollment.open, 'MM/DD/YYYY') }} - {{ formatDate(enrollment.close, 'MM/DD/YYYY') }}</b>
                </div>
                <div>
                  <div v-if="enrollment?.active" class="font-7-8r num-font q-pt-sm">
                    <div class="tw-six">
                      {{ enrollment.enrolled }} enrolled of {{ invitedTotal }} invited
                    </div>
                    <div class="q-pt-md">
                      <q-chip dense color="transparent" clickable @click="activateDialog = true">
                        <span class="q-mr-sm tw-six">Re-sync invites</span>
                        <q-icon name="mdi-refresh" color="primary"></q-icon>
                      </q-chip>
                    </div>
                  </div>
                  <div v-else class="font-1r flex items-center q-pt-md">
                    <q-chip dense clickable color="transparent" class="tw-six text-primary"
                            @click="activateDialog = true">
                      <span>Activate Enrollment</span>
                      <q-icon class="q-ml-sm" name="mdi-check-circle"></q-icon>
                    </q-chip>
                  </div>
                </div>
              </div>
            </div>

            <plan-year-enrollments
                :event="selected"
                :year="year"
                :plan="fullPlan"
            ></plan-year-enrollments>

          </q-tab-panel>
        </q-tab-panels>

      </q-tab-panel>
    </q-tab-panels>

    <common-dialog setting="smmd" v-model="activateDialog">
      <div class="_fw q-pa-lg br10 bg-white bs2-8">
        <activate-card
            :plan="fullPlan"
            :plan-key="selected"
            @cancel="activateDialog = false"
            @done="activateDialog = false"
        ></activate-card>

      </div>
    </common-dialog>


  </div>
</template>

<script setup>
  import PlanYearEnrollments from 'components/enrollments/admin/cards/PlanYearEnrollments.vue';
  import PlanEnrollmentEvents from 'components/enrollments/admin/events/PlanEnrollmentEvents.vue';
  import ActivateCard from 'components/enrollments/admin/cards/ActivateCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {computed, onMounted, ref} from 'vue';
  import {enrollmentsByYear} from 'src/components/enrollments/admin/utils';
  import {idGet} from 'src/utils/id-get';
  import {formatDate} from 'src/utils/date-utils';

  import {usePlans} from 'stores/plans';
  import {useRoute, useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  import {useEnrollments} from 'stores/enrollments';
  import EnrollmentForm from 'components/enrollments/forms/EnrollmentForm.vue';


  const envStore = useEnvStore()
  const planStore = usePlans();
  const enrollmentStore = useEnrollments();

  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    plan: { required: false }
  })

  const yr = new Date().getFullYear();
  const year = ref(yr);
  const selected = ref('')
  const activateDialog = ref(false);

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || envStore.getPlanId),
    routeParamsPath: 'planId'
  })

  const enrollments = computed(() => enrollmentsByYear(fullPlan.value));

  const enrollment = computed(() => {
    return (fullPlan.value.enrollments || {})[selected.value] || {}
  })

  const adding = ref(0);

  const closeAdd = (val) => {
    if (!val) {
      adding.value = 0;
    }
  }

  const nextKey = (y) => {
    const keys = Object.keys(enrollments.value[y] || {});
    const last = keys ? Number(keys[keys.length - 1] || -1) : -1;
    return `${y}_${last + 1}`
  }
  const addNextKey = (y) => {
    // console.log('add next key', y)
    const nk = nextKey(y);
    planStore.patchInStore(fullPlan.value._id, { enrollments: { ...fullPlan.value.enrollments, [nk]: {} } })
    planStore.patch(fullPlan.value._id, { $set: { [`enrollments.${nk}`]: {} } })
    closeAdd()
  }

  const editing = ref(false);
  const invitedTotal = ref();
  const setEvent = async (evt, skip) => {
    selected.value = evt || '';
    if (!skip) router.push({ ...route, params: { event: selected.value } })
    if (evt) {
      const invited = await enrollmentStore.find({
        query: {
          plan: fullPlan.value._id,
          version: evt,
          $limit: 1
        }
      })
      if (invited) invitedTotal.value = invited.total;
    }
  }

  onMounted(async () => {
    if (route.params.event) {
      await setEvent(route.params.event, true);
      year.value = Number(route.params.event.split('_')[0])
      setTimeout(() => {
        if (!Object.keys((fullPlan.value.enrollments || {})[route.params.event] || {}).length) editing.value = true;
      }, 500)
    }
  })

</script>

<style lang="scss" scoped>
  .__top_box {
    padding: 20px 15px;
    border-radius: 8px;
    border: solid 1px var(--ir-mid);
    background: var(--ir-bg1);
  }
</style>
