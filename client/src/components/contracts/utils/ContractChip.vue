<template>
  <q-chip v-bind="{clickable: true, ...$attrs}">
    <slot name="default">
      <q-icon color="a3" name="mdi-file-document" class="q-mr-xs"></q-icon>
      <span v-if="contract._id" class="tw-six">{{contract.name}}</span>
      <span v-else>{{picker ? 'Select Contract' : emptyLabel}}</span>
      <q-icon v-if="picker" class="q-ml-sm" name="mdi-menu-down"></q-icon>
    </slot>
    <q-popup-proxy v-if="picker">
      <div class="bg-ir-bg1 text-ir-text w400 mw100 q-pa-sm">
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>

          <q-list separator>
            <q-item v-for="(c, i) in c$.data" :key="`t-${i}`" clickable @click="handleSelect(c)">
              <q-item-section avatar>
                <q-icon color="a3" name="mdi-file-document"></q-icon>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{c.name}}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {useContracts} from 'stores/contracts';

  const contractStore = useContracts()

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    emptyLabel: { default: 'No Contract' },
    modelValue: { required: true },
    emitValue: Boolean,
    picker: Boolean,
    ownerId: { required: false },
    subjectId: { required: false }
  })

  const { item:contract } = idGet({
    store: contractStore,
    value: computed(() => props.modelValue)
  })

  const handleSelect = (v) => {
    if(props.emitValue) emit('update:model-value', v._id)
    else emit('update:model-value', v)
  }

  const { search, searchQ } = HQuery({})
  const limit = ref(10)
  const { h$:c$ } = HFind({
    store: contractStore,
    limit,
    pause: computed(() => !props.picker || !props.ownerId),
    params: computed(() => {
      const query = { status: { $nin: ['sent', 'rejected', 'executed']}, ...searchQ.value };
      if(props.ownerId) query.owner = props.ownerId;
      if(props.subjectId) query.subject = props.subjectId;
      return {
        query
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
