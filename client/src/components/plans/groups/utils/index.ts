import {computed, ComputedRef, ref, Ref, watch} from 'vue';
import {Plan} from 'components/plans/utils/index.js';
import {idGet} from 'src/utils/id-get.js';
import {useOrgs} from 'stores/orgs.js';
import {$errNotify} from 'src/utils/global-methods.js';
import {usePlans} from 'stores/plans.js';
import {HFind} from 'src/utils/hFind.js';
import {useGroups} from 'stores/groups.js';
import {useAtcStore} from 'stores/atc-store';

export const planGroups = (plan:Ref<Plan>|ComputedRef<Plan>) => {
    const orgStore:any = useOrgs();
    const planStore:any = usePlans();
    const groupStore:any = useGroups();

    const adding = ref(undefined);

    const { item: org } = idGet({
        value: computed(() => plan.value?.org),
        store: orgStore,
        useAtcStore
    })

    const grpIDs = computed(() => {
        const obj:any = {};
        for (const id of plan.value?.groups || []) {
            obj[id] = true;
        }
        return obj;
    })

    const affiliatedIds = computed(() => {
        const ids = org.value?.managementOrgs || [];
        Object.keys(org.value?.controls || {}).forEach(a => a.split('.').forEach(b => ids.push(b)));
        Object.keys(org.value?.asg || {}).forEach(a => a.split('.').forEach(b => ids.push(b)));
        if(org.value) ids.push(org.value._id);
        return ids;
    })

    const affiliated = HFind({
        store: orgStore,
        limit: ref(50),
        paginateApi: 'server',
        params: computed(() => {
            return {
                query: {
                    _id: { $in: affiliatedIds.value }
                }
            }
        })
    })

    const groups:Ref<{ [key:string]: { total: number, data: Array<any> } & any}> = ref({});
    watch(() => affiliated.h$.data, async (nv, ov) => {
        if(nv && nv.length !== ov?.length){
            const obj:any = {};
            for(let i = 0; i < nv.length; i++){
                const prms = { query: { org: nv[i]._id, planClass: true } };
                let res = groupStore.findInStore(prms)
                if(!res.total)  res = await groupStore.find(prms);
                obj[nv[i]._id] = res;
            }
            groups.value = obj;
        }
    }, { immediate: true })

    const { h$:allGroups } = HFind({
        store: groupStore,
        limit: ref(250),
        paginateApi: 'server',
        params: computed(() => {
            return {
                query: {
                    org: { $in: affiliatedIds.value },
                    planClass: true
                }
            }
        })
    })

    const patchObj:Ref<any> = ref({})
    const saveTo = ref()
    const maybeSave = async () => {
        if(saveTo.value) clearTimeout(saveTo.value)
        saveTo.value = setTimeout(async () => {
            const saved = await planStore.patch(plan.value._id, patchObj.value)
                .catch((err:any) => $errNotify(`Error adding group to plan: ${err.message}`))
            if(saved?._id) patchObj.value = {}
        }, 1000)
    }

    const checkOrgs = () => {
        patchObj.value.orgs = Array.from(new Set(allGroups.data.map(a => a.org)));
    }

    const addGroup = async (grp:any) => {
        adding.value = grp._id;
        const arr = [...plan.value.groups || []];
        const idx = arr.indexOf(grp._id);
        if(idx > -1){
            // console.log('idx', idx);
            arr.splice(idx, 1);
            patchObj.value.$pull = { groups: grp._id }
        } else {
            arr.push(grp._id);
            patchObj.value.$addToSet = { groups: grp._id }
        }
        planStore.patchInStore(plan.value._id, { groups: arr })
        maybeSave()
        adding.value = undefined;

        checkOrgs()
    }

    return {
        allGroups,
        org,
        adding,
        addGroup,
        grpIDs,
        groups,
        affiliated,
        groupStore
    }
}
