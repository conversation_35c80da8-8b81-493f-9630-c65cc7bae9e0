<template>
  <div class="_fw q-pa-sm">

    <div class="__top">
      <div class="font-1r tw-six">Select eligible groups from {{ org?.name }} and affiliated companies</div>
      <div class="font-7-8r">Remember, affiliated companies are treated as a single company for discrimination, employee
        count, and other plan purposes.
      </div>
      <q-separator class="q-my-sm"></q-separator>
      <div class="font-7-8r">You can separate certain plan benefits by valid <span class="tw-six text-blue-10 cursor-pointer">employee classes
        <q-popup-proxy breakpoint="50000">
          <div class="w400 mw100 br10 q-pa-lg bg-white">
            <employee-classes></employee-classes>
          </div>
        </q-popup-proxy>
      </span>
      </div>
    </div>
    <q-list
        v-for="(o, i) in affiliated.h$.data || []"
        :key="`org-${i}`"
        separator
        class="q-py-md">
      <default-item
          :item-attrs="{ class: '_fw', clickable: false }"
          :model-value="o"
          :use-atc-store="useAtcStore"
      ></default-item>
      <div v-if="!groups[o._id]?.total" class="q-pa-md text-italic">No Eligible Employee Classes Found</div>
      <q-item v-for="(grp, i) in groups[o._id]?.data || []" :key="`grp-${i}`" clickable @click="addGroup(grp)">
        <q-item-section avatar>
          <q-spinner v-if="adding === grp._id"></q-spinner>
          <q-icon v-else-if="grpIDs[grp._id]" color="green" name="mdi-checkbox-marked"></q-icon>
          <q-icon v-else color="grey-7" name="mdi-checkbox-blank-outline"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label><span class="tw-six">{{ grp.name }}</span> - {{ $possiblyPlural('Member', grp.memberCount) }}
          </q-item-label>
          <q-item-label :class="`text-${grpIDs[grp._id] ? 'primary' : 'grey-7'}`">
            {{ grpIDs[grp._id] ? 'Participating' : 'Not Participating' }}
          </q-item-label>
        </q-item-section>
      </q-item>


    </q-list>

  </div>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import EmployeeClasses from 'components/plans/groups/utils/EmployeeClasses.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useAtcStore} from 'stores/atc-store';
  import {planGroups} from '../utils';
  import { $possiblyPlural} from 'src/utils/global-methods';

  const planStore = usePlans();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: plan } = idGet({
    value: computed(() => props.modelValue),
    store: planStore
  })

  const { org, adding, addGroup, grpIDs, groups, affiliated  } = planGroups(plan);


</script>

<style lang="scss" scoped>
  .__title {
    border-radius: 8px;
    color: #666;
    //background: linear-gradient(12deg, var(--q-p9), var(--q-p6), var(--q-p9));
    //color: white;
  }

  .__gg {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 300px));
    grid-gap: 20px;
  }

  .__c {
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .2);
    justify-content: center;
  }

  .__top {
    padding: 4vh 2vw;
    border-radius: 6px;
    background: #efefef;
    //color: var(--q-p9);
    //color: white;
  }
</style>
