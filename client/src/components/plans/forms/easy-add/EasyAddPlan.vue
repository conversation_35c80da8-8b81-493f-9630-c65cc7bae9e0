<template>
    <div class="row justify-center">
      <div class="_sent pw2 pd4">
        <template v-if="!plan.doc">
          <div class="_fw">
            <div class="q-pa-md tw-five font-1-1-8r text-ir-deep">Welcome to the future of health plans: <span
                class="font-1-1-4r tw-six text-accent">Let's set yours up</span></div>
          </div>

          <div class="__c">
            <q-tabs no-caps align="justify" v-model="tab" indicator-color="p3" :inline-label="$q.screen.gt.sm">
              <q-tab v-for="(t, i) in Object.keys(tabs)" :key="`tab-${i}`" :name="t">
                <q-icon class="font-1-1-4r q-mx-sm" :name="tabs[t].icon" :color="tab === t ? 'primary' : ''"></q-icon>
                <span class="text-xs tw-six">{{ tabs[t].label }}</span>
              </q-tab>
            </q-tabs>
          </div>

          <q-tab-panels keep-alive class="_panel" animated v-model="tab">
            <q-tab-panel class="_panel" name="profile">
              <template v-if="!isAuthenticated">
                <div class="__c">
                  <div class="w500 mw100">
                    <trad-login/>
                  </div>
                </div>
              </template>
              <template v-else>

                <div class="__gs">

                  <div class="__left">
                    You
                  </div>
                  <div class="__right">

                    <div class="__in">
                      <div>Name</div>
                      <div>
                        <q-input v-bind="inputs" v-model="personForm.name"
                                 @update:model-value="autoSavePerson('name')"></q-input>
                      </div>
                    </div>
                    <div class="__in">
                      <div>Email</div>
                      <div>
                        <email-field v-model="personForm.email" v-bind="inputs"
                                     @update:model-value="autoSavePerson('email')"></email-field>
                      </div>
                    </div>
                    <div class="__in">
                      <div>Phone</div>
                      <div>
                        <phone-input
                            v-bind="{ inputAttrs: inputs }"
                            v-model="personForm.phone"
                            @update:model-value="autoSavePerson('phone')"
                        ></phone-input>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="__gs">
                  <div class="__left">
                    Your Company
                  </div>
                  <div class="__right">
                    <div class="__in">
                      <div>Logo</div>
                      <div class="__logo">
                        <div class="__lform">
                          <image-form
                              file-path="avatar"
                              v-model="org.avatar"
                              :use-atc-store="useAtcStore"
                              @update:model-value="autoSaveOrg('avatar')"></image-form>
                        </div>
                        <div v-if="!orgForm.avatar" class="bg-ir-bg2 flex flex-center _fa">
                          <q-icon size="30px" color="ir-mid" name="mdi-image"></q-icon>
                        </div>
                        <q-img v-else class="_fa" fit="contain"
                               :src="orgForm.avatar.url || org._fastjoin?.files?.avatar?.url"></q-img>
                      </div>
                    </div>
                    <div class="__in">
                      <div>Name</div>
                      <div>
                        <q-input @blur="checkSave" v-bind="inputs" v-model="orgForm.name"
                                 @update:model-value="autoSaveOrg('name')"></q-input>
                      </div>
                    </div>
                    <div class="__in">
                      <div>Legal Name</div>
                      <div>
                        <q-input v-bind="inputs" v-model="orgForm.legalName"
                                 @update:model-value="autoSaveOrg('legalName')"></q-input>
                      </div>
                    </div>
                  </div>
                </div>
              </template>

            </q-tab-panel>
            <q-tab-panel class="_panel" name="employees">
              <div class="__c">
                <census-table :service-path="service-path" :store="store" flat full :id="item._id" :update="updateCensus">
                  <template v-slot:top>
                    <div class="tw-six text-xxs text-center">Add additional employee details to make enrollment smoother
                    </div>
                    <div class="q-pb-md tw-six text-xxs text-center text-a4">Employees can be added later if needed, or
                      you can email them a link
                    </div>
                  </template>
                </census-table>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="docs">
              <div class="__c">
                <smb-plan :store="store" :smb="smb" :modelValue="item"></smb-plan>
              </div>
            </q-tab-panel>
          </q-tab-panels>

        </template>
        <template v-else>

          <div class="__c">
            <div class="text-primary tw-six text-sm">Good Work!</div>
            <div class="text-xs q-pt-sm">You can fully manage your plan in your <q-chip dense square class="bg-primary text-white tw-six cursor-pointer" clickable @click="goToDash">Plan Admin Dashboard <q-icon class="q-ml-xs" name="mdi-chevron-right" color="white"></q-icon></q-chip>. Next steps will be:</div>
            <div class="row q-pt-md">
              <div class="col-12 col-md-4 q-pa-sm" v-for="(step, i) in steps" :key="`step-${i}`">
                <div class="__next">
                  <div class="row justify-center">
                    <q-img v-if="step.img" :src="step.img" class="h30 w30"></q-img>
                    <q-icon v-else size="30px" color="ir-grey-4" :name="step.icon"></q-icon>
                  </div>
                  <div class="text-center tw-six text-xs text-ir-deep">{{step.label}}</div>
                  <div class="text-center text-xxs">{{step.text}}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="__c">
            <div class="font-1r text-italic q-pa-md">You can edit these plan documents in your plan admin dashboard
            </div>

            <docs-display no-share no-print :mobile="true" :plan="plan"></docs-display>
          </div>
        </template>
      </div>
    </div>
</template>

<script setup>
  import caIcon from 'src/assets/common_cent_grey.svg'
  import TradLogin from 'components/auth/traditional/TradLogin.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';
  import CensusTable from 'pages/landing/sm-er/results/CensusTable.vue';
  import SmbPlan from 'components/plans/smb/forms/SmbPlan.vue';
  import DocsDisplay from 'components/plans/docs/cards/DocsDisplay.vue';
  import {useAtcStore} from 'stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useRoute} from 'vue-router';
  import {loginPerson} from 'stores/utils/login';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useOrgs} from 'stores/orgs';
  import {dataURLToBlob} from 'components/common/uploads';
  import {storjUpload} from 'components/common/uploads/services';
  import {usePlans} from 'stores/plans';

  const { person, pplStore, isAuthenticated } = loginPerson();

  const orgStore = useOrgs();
  const route = useRoute()
  const planStore = usePlans();

  const props = defineProps({
    store: { required: true },
    modelValue: { required: true, default: () => ({}) },
    localPath: String,
    smb:Boolean,
    servicePath: String
  })


  const inputs = { filled: true, dense: true, hideBottomSpace: true, bgColor: 'ir-bg2' }

  const { form: personForm, save: savePerson } = HForm({
    store: pplStore,
    value: person,
    notify: false,
  })

  const { autoSave: autoSavePerson } = HSave({
    store: pplStore,
    form: personForm,
    save: savePerson,
    pause: computed(() => !personForm.value._id)
  })

  const tab = ref('profile')

  const drId = ref()
  const { item } = idGet({
    store: props.store,
    value: computed(() => props.modelValue)
  })

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => item.value.plan)
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => item.value?.org)
  })
  const { form: orgForm, save: saveOrg } = HForm({
    store: orgStore,
    value: org,
    notify: false,
    afterFn: (val) => {
      if (val?._id && item.value._id) props.store.patch(item.value._id, { org: val._id })
      return val;
    }
  })

  const syncImage = async () => {
    const avatar = item.value.orgAvatar || orgForm.value.companyAvatar;
    if (avatar) orgForm.value.avatar = { url: avatar };
    const name = item.value.orgName || orgForm.value.companyName;
    if (name && !orgForm.value.name) orgForm.value.name = name;
    setTimeout(async () => {
      if (avatar) return;
      if (org.value.avatar?.url) return;
      const blob = dataURLToBlob(avatar);
      const emt = (evt, args) => {
        if (args?.url && evt === 'update:model-value') {
          orgForm.value.avatar = args;
          if (item.value.org) orgStore.patch(item.value.org, { avatar: args })
          else if (orgForm.value.name) saveOrg()
        }
      }
      await storjUpload(undefined, { emit: emt })({
        sm: blob,
        raw: {
          name: (orgForm.value.name || 'company_logo').split(' ').join('_').toLowerCase(),
          lastModifiedDate: new Date()
        }
      })
    }, 1000)
  }

  const checkSave = () => {
    if (orgForm.value?.name) saveOrg()
  }

  watch(item, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      const name = nv.orgName || nv.companyName;
      if (name) orgForm.value.name = name;
      if (isAuthenticated.value) syncImage()
    }
  }, { immediate: true });

  watch(isAuthenticated, (nv, ov) => {
    if (nv && !ov) syncImage()
  }, { immediate: true });

  const { autoSave: autoSaveOrg } = HSave({
    store: orgStore,
    form: orgForm,
    save: saveOrg,
    pause: computed(() => !orgForm.value._id)
  })

  watch(orgForm, (nv) => {
    if (nv?._id && !item.value?.org) props.store.patch(item.value._id, { org: nv._id });
  }, { immediate: true });

  const updateCensus = (employees) => {
    props.store.patch(item.value._id, { employees })
  }

  const tabs = computed(() => {
    return {
      'profile': {
        icon: 'mdi-domain',
        label: 'Profile'
      },
      'employees': {
        icon: 'mdi-face-man',
        label: 'Employees'
      },
      'docs': {
        icon: 'mdi-file-document',
        label: 'Plan Docs'
      }
    }
  })

  const steps = [
    {
      img: caIcon,
      label: 'Plan Wallet',
      text: 'Plan wallets fund your plan through payroll elections seamlessly and much more'
    },
    {
      icon: 'mdi-face-man',
      label: 'Enrollment',
      text: 'Invite your people to enroll in your now open-ended plan options turn-key'
    },
    {
      icon: 'mdi-stethoscope',
      label: 'Care Guide',
      text: 'Cooking up a plan is nice - but how is it? Care cost and quality are everything'
    }
  ]

  const goToDash = async () => {
    const fqdn = SessionStorage.getItem('fqdn') || 'commoncare.org'
    window.open(`admin.${fqdn}?client_ucan=${SessionStorage.getItem('client_ucan')}&plan_id=${plan.value._id}&org_id=${item.value.org}`)
  }

  onMounted(() => {
    if (route.query.reqId && props.localPath) {
      drId.value = route.query.reqId;
      LocalStorage.setItem(localPath, drId.value);
    }
  })

</script>

<style lang="scss" scoped>

  .__c, .__gs {
    margin: 20px 0;
    padding: max(2vw, 30px) max(2vw, 8px);
    border-radius: 20px;
    background: var(--ir-bg);
    box-shadow: 0 4px 6px var(--ir-light);
    width: 100%;
  }

  .__c {
    margin: 10px 0;
  }

  .__logo {
    height: 60px;
    width: 60px;
    overflow: hidden;
    position: relative;

    .__lform {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      opacity: 0;
    }
  }

  .__gs {
    display: grid;
    grid-template-columns: 200px 1fr;

    .__left {
      padding: 20px 15px;
      border-right: solid .3px var(--ir-light);
      text-align: right;
      font-weight: 600;
      color: var(--ir-mid);
      font-size: 1rem;
    }

    .__right {
      padding: 20px 15px;
    }
  }

  .__in {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: auto auto;
    width: 100%;
    max-width: 500px;

    > div {
      &:first-child {
        font-size: .8rem;
        font-weight: 400;
        padding: 0 10px 3px 10px;
        color: var(--ir-mid);
      }

      &:nth-child(2) {
        padding-bottom: 10px;
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .__gs {
      display: grid;
      grid-template-columns: 100%;
      grid-template-rows: auto auto;

      .__left {
        padding: 10px 8px;
        border-right: none;
        border-bottom: solid .3px var(--ir-light);
        text-align: left;
        font-weight: 600;
        color: var(--ir-mid);
        font-size: 1rem;
      }

      .__right {
        padding: 10px 8px;
      }
    }
  }

  .__next {
    width: 100%;
    padding: 20px 15px;
    border-radius: 8px;
    height: 100%;
    background: linear-gradient(135deg, var(--q-p0), white, var(--q-a0));
    //box-shadow: 0 4px 6px var(--ir-light);
  }
</style>
