import {computed, ComputedRef, ref, Ref} from 'vue';
import {useGroups} from 'stores/groups.js';
import {usePlans} from 'stores/plans.js';
import {useRoute} from 'vue-router';
import {idGet} from 'src/utils/id-get.js';
import {HFind} from 'src/utils/hFind.js';
import {LocalStorage} from 'symbol-auth-client';
import {useGrpMbrs} from 'stores/grp-mbrs';
import {useAtcStore} from 'stores/atc-store';

export const eligibleGroups = (plan?: Ref<any> | ComputedRef<any>) => {
    const groupStore = useGroups();
    const planStore = usePlans();
    const route = useRoute()

    const planId = computed(() => plan?.value || route.params.planId || LocalStorage.getItem('plan_id'));

    const {item: fullPlan} = idGet({
        value: planId,
        store: planStore as any,
        useAtcStore
    })

    const groupData = HFind({
        store: groupStore as any,
        limit: ref(50),
        params: computed(() => {
            return {
                query: {
                    _id: {$in: fullPlan.value?.groups || []}
                },
                runJoin: {groupOrg: true}
            }
        })
    })

    const byOrg = computed(() => {
        const obj: any = {};
        for (const grp of groupData.h$.data || []) {
            obj[grp.org] = {org: grp._fastjoin?.org || grp.org, groups: [...(obj[grp.org]?.groups || []), grp]}
        }
        return obj;
    })

    return {fullPlan, planId, groupData, groupStore, planStore, byOrg}
}

declare type Options = {
    query?: Ref<any>|ComputedRef<any>,
    limit: Ref<number>
}
export const planPeople = (plan: Ref<any> | ComputedRef<any>, { query, limit = ref(10) }:Options) => {
    const mbrStore = useGrpMbrs() as any;
    const {fullPlan, planId, groupData, groupStore, planStore, byOrg} = eligibleGroups(plan);

    const mbrs = HFind({
        store: mbrStore,
        limit,
        params: computed(() => {
            return {
                runJoin: {with_person: true},
                query: {
                    group: { $in: (groupData.h$.data || []).map((a:any) => a._id) },
                    ...query?.value || {}
                }
            }
        })
    })

    return {
        fullPlan, planId, groupData, groupStore, planStore, byOrg, limit, mbrs
    }
}
