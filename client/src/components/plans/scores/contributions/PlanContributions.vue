<template>
  <div class="_fw">
    <div class="flex items-center">
      <org-chip :model-value="org"></org-chip>
      <q-icon name="mdi-chevron-right"></q-icon>
      <q-chip color="ir-bg2" :label="group.name || 'No employee group specified'"></q-chip>
    </div>

    <div class="q-py-md" v-if="cafe.amount">
      <div class="text-sm tw-six">Care Wallet Allowance</div>
      <div class="_fw mw800">
      <div class="text-xs">For use on any option - or pay taxes and take it home. The reason to start it here is: when you choose qualified healthcare options the funds are tax-free.</div>
      </div>
      <div class="row items-center q-py-sm">
        <q-chip square color="ir-bg2">
          <span class="font-7-8r tw-six">Single:&nbsp;</span>
          <span class="font-1-1-2r tw-six alt-font text-primary">{{ cafeLabels.single.amt }}</span>
          <span class="font-7-8r tw-six alt-font text-primary">{{ cafeLabels.single.suffix }}</span>
        </q-chip>
        <q-chip square color="ir-bg2">
          <span class="font-7-8r tw-six">Family:&nbsp;</span>
          <span
              class="font-1-1-2r tw-six alt-font text-accent">{{
              dollarString($max(cafe.amount, cafe.family || 0), '$', 0)
            }}</span>
          <span class="font-7-8r tw-six alt-font text-accent">{{ cafeLabels.family.suffix }}</span>

        </q-chip>
      </div>
    </div>

    <div class="q-py-md">
      <div class="text-sm tw-six">Coverage Allowance</div>
      <div class="_fw mw800">
        <div class="text-xs">
          For our scoring, the only reason to assign funds to a coverage is a small amount to equalize tax outcomes. Otherwise, its better to put the funds in a flexible 125 plan rather than trap them in use-it-or-lose-it.
        </div>

      </div>

      <div class="__covs">
        <table>
          <thead>
          <tr>
            <th>Coverage</th>
            <th>Carrier</th>
            <th>Single Allowance</th>
            <th>Family Allowance</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(c, i) in c$.data" :key="`cov-${i}`">
            <td>{{ c.name }}</td>
            <td>
              <q-chip color="ir-bg2" v-if="c.carrierName">
                <q-avatar color="white" v-if="byId.bySource[c._id]">
                  <img :src="byId.bySource[c._id].url" alt=""/>
                </q-avatar>
                <span class="font-1r">{{ c.carrierName }}</span>
              </q-chip>
              <org-chip class="font-1r" v-else-if="c.issuer" :model-value="c.issuer"></org-chip>
              <q-chip v-else class="bg-ir-bg2 font-1r" label="No Carrier"></q-chip>
            </td>
            <td>
              <span v-if="cById[c._id].single">{{ dollarString(cById.single, '$', 0) }}</span>
              <span v-else>N/A</span>
            </td>
            <td>
              <span v-if="cById[c._id].family">{{ dollarString(cById.family, '$', 0) }}</span>
              <span v-else>N/A</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
  import OrgChip from 'components/orgs/cards/OrgChip.vue';

  import {computed} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {useEnrollments} from 'stores/enrollments';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useGroups} from 'stores/groups';
  import {dollarString} from 'symbol-syntax-utils';
  import {$max} from 'src/utils/global-methods';
  import {useCoverages} from 'stores/coverages';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';

  const { person } = loginPerson()

  const enrollmentStore = useEnrollments();
  const orgStore = useOrgs();
  const groupStore = useGroups();
  const coverageStore = useCoverages();

  const props = defineProps({
    plaN: { required: true },
    groupId: { required: true }
  })

  const plan = computed(() => props.plaN || {})


  const { h$: e$ } = HFind({
    store: enrollmentStore,
    pause: computed(() => props.groupId || !person.value._id),
    params: computed(() => {
      return {
        query: {
          $sort: { planYear: -1 },
          person: person.value._id,
          plan: plan.value._id
        }
      }
    })
  })

  const groupId = computed(() => {
    if (props.groupId) return props.groupId
    else {
      let groupId;
      const e = e$.data[0];
      if (!e) return undefined
      else groupId = e.group;
      if (!groupId) {
        groupId = (person.value.inGroups || []).filter(a => (plan.value.groups || []).includes(a))[0]
      }
      return groupId
    }
  })
  const { item: group } = idGet({
    store: groupStore,
    value: groupId
  })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => group.value.org || plan.value.org)
  })

  const coverageIds = computed(() => Object.keys(plan.value.coverages || {}))
  const { h$: c$ } = HFind({
    store: coverageStore,
    limit: computed(() => coverageIds.value.length),
    params: computed(() => {
      return {
        runJoin: { add_files: true },
        query: { covered: 'group', _id: { $in: coverageIds.value } },
      }
    })
  })

  const { byId } = manageFindUploads({ sources: c$, paths: ['carrierLogo']})

  const cById = computed(() => {
    const obj = {};
    for (let i = 0; i < c$.data.length; i++) {
      obj[c$.data[i]._id] = (plan.value.coverages || {})[c$.data[i]._id]?.employerContribution || {}
    }
    return obj
  })

  const cafe = computed(() => {
    return (plan.value.employerContribution || {})[groupId.value] || {}
  })
  const cafeLabels = computed(() => {
    const p = cafe.value.type === 'percent'
    const format = (amt) => {
      if (p) return `${dollarString(amt, '', 0)}%`
      else return dollarString(amt, '$', 0)
    }
    const suffix = p ? cafe.value.match ? ' of your pay as a match' : ' of your pay' : '/mo'
    return {
      single: { amt: format(cafe.value.amount || 0), suffix },
      family: { amt: format(cafe.value.family || 0), suffix },
    }
  })


</script>

<style lang="scss" scoped>

  .__covs {
    margin: 20px 0;
    width: 100%;
    overflow-x: scroll;
    padding: 30px 15px;
    border-radius: 15px;
    box-shadow: 2px 2px 8px var(--ir-light);
    background: var(--ir-bg1);

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 1rem;

      tr {
        th {
          padding: 4px 8px;
          font-weight: 600;
          color: var(--ir-mid);
          text-align: left;
        }

        td {
          padding: 4px 8px;
          border-bottom: solid .2px var(--ir-light);
        }

        &:last-child {
          td {
            border-bottom: none;
          }
        }
      }
    }
  }
</style>
