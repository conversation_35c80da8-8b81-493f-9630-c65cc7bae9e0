<template>
  <div>

  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useCoverages} from 'stores/coverages';

  const coverageStore = useCoverages();

  const props = defineProps({
    cov: { required: true },
    config: { required: true }
  })

  const { item:coverage } = idGet({
    store: coverageStore,
    value: computed(() => props.cov)
  })
</script>

<style lang="scss" scoped>

</style>
