<template>
  <div class="_fw">

    <q-tab-panels keep-alive animated class="_panel" :model-value="!viewing">
      <q-tab-panel class="_panel" :name="true">

        <q-chip color="ir-bg2" clickable>
          <default-avatar v-if="!!personFilter" :model-value="personFilter" :use-atc-store="useAtcStore"></default-avatar>
          <span class="q-mx-sm">{{ personFilter?.name || 'Select Participant' }}</span>
          <q-icon name="mdi-menu-down" v-if="!personFilter"></q-icon>
          <q-btn v-else dense flat size="xs" color="red" icon="mdi-close" @click="personFilter = undefined"></q-btn>

          <q-popup-proxy v-model="filterOpen">
            <div class="w400 mw100 q-pa-sm">
              <q-input v-model="search.text" dense filled>
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
              <q-list separator>
                <q-item v-for="(p, i) in p$.data" :key="`p-${i}`" clickable @click="selectPerson(p)">
                  <q-item-section>
                    <q-item-label>{{ p.firstName }} {{ p.lastName }}</q-item-label>
                    <q-item-label caption>{{ p.email || 'No Email Listed' }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </q-popup-proxy>
        </q-chip>
        <status-chip flat picker multiple v-model="statusFilter"></status-chip>

        <div class="_fw">
          <plan-interval-chip v-model="interval" color="ir-bg2"></plan-interval-chip>
        </div>
        <div class="row justify-end">
          <q-btn @click="download" flat dense color="accent" icon="mdi-download" label="CSV"></q-btn>
        </div>
        <q-scroll-area id="I_S" :style="{ height: tableHeight }" @scroll="senseScrollLoad">
          <div class="__table">
            <table>
              <thead>
              <tr>
                <th v-for="(col, i) in cols" :key="`r-title-${i}`" class="__title">
                  <span v-if="col.title">{{ col.title }}</span>
                </th>
              </tr>
              <tr>
                <th v-for="(col, i) in cols" :key="`r-head-${i}`" :class="col.class">{{ col.header }}</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(e, i) in e$.data" :key="`e-${i}`">
                <td>{{ e._fastjoin?.person?.name || 'No Name' }}</td>
                <td>
                  <status-chip size="sm" :model-value="e.status"></status-chip>
                </td>
                <td v-for="(col, idx) in cols.slice(2)" :key="`cd-${i}-${idx}`" :class="col.class">{{ col.value(e) }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </q-scroll-area>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">

      </q-tab-panel>
    </q-tab-panels>
    <pagination-row
        v-bind="{ pagination, h$:e$, pageRecordCount, limit}"
    ></pagination-row>
  </div>
</template>

<script setup>
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';
  import PlanIntervalChip from 'components/plans/utils/PlanIntervalChip.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {HFind, hInfiniteScroll} from 'src/utils/hFind';
  import {useAtcStore} from 'stores/atc-store';
  import {useEnrollments} from 'stores/enrollments';
  import {HQuery} from 'src/utils/hQuery';
  import {usePpls} from 'stores/ppls';
  import {useGroups} from 'stores/groups';
  import {_get, dollarString} from 'symbol-syntax-utils';
  import {$limitStr} from 'src/utils/global-methods';
  import {useCoverages} from 'stores/coverages';
  import {cafeKeys} from 'src/components/plans/utils';
  import {formatDate} from 'src/utils/date-utils';
  import { intervals } from '../utils/intervals';
  import {useRoute, useRouter} from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const enrollmentStore = useEnrollments();
  const pplStore = usePpls();
  const groupStore = useGroups();
  const coverageStore = useCoverages();

  const props = defineProps({
    plan: { required: true },
    planYear: { required: true }
  })

  const interval = ref('monthly');

  const getC = (e, path) => (_get(e.contributions, path, 0) || 0) * intervals[interval.value].factor

  const viewing = ref(undefined);
  const statusFilter = ref(['complete']);
  const personFilter = ref()
  const filterOpen = ref(false)
  const selectPerson = (v) => {
    personFilter.value = v;
    filterOpen.value = false;
  }

  watch(statusFilter, (nv, ov) => {
    if(nv.length !== ov?.length){
      const { href } = router.resolve({ ...route, query: { ...route.query, status: nv.join(',') }})
      window.history.pushState({}, '', href)
    }
  })

  const versions = computed(() => Object.keys(props.plan?.enrollments || {}).filter(a => a.includes(props.planYear)))

  const groupIds = computed(() => props.plan?.groups || []);
  const groups = ref({ data: [] })
  watch(groupIds, async (nv, ov) => {
    if (nv?.length && nv.length !== ov?.length) {
      groups.value = await groupStore.find({
        query: {
          _id: { $in: groupIds.value },
          $limit: groupIds.value.length,
          $select: ['_id', 'name', 'org']
        }
      })
          .catch(err => {
            console.error(`Error finding groups for plan payroll report ${err.message}`)
            return groups.value
          })
    }
  }, { immediate: true })
  const limit = ref(25)
  const { h$: e$, pagination, pageRecordCount } = HFind({
    store: enrollmentStore,
    limit,
    params: computed(() => {
      const query = {
        plan: props.plan?._id,
        version: { $in: versions.value }
      }
      if (personFilter.value) query.person = personFilter.value._id
      if(statusFilter.value.length) query.status = { $in: statusFilter.value }
      return {
        query,
        runJoin: { enrollment_person: true, sum_contributions: true }
      }
    })
  })
  const { senseScrollLoad } = hInfiniteScroll({ h$: e$, loadNum: 25 })
  const tableHeight = computed(() => (Math.min(window.innerHeight * .8, Math.min(25, Math.max(e$.total, 5)) * 55)) + 'px')

  const { search, searchQ } = HQuery({})
  const { h$: p$ } = HFind({
    store: pplStore,
    pause: computed(() => !filterOpen.value),
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          inOrgs: { $in: (groups.value?.data || []).map(a => a.org) }
        }
      }
    })
  })

  const coverageIds = computed(() => Object.keys(props.plan.coverages || {}))
  const coverages = ref({ data: [] })
  const cById = computed(() => {
    const obj = {};
    for (let i = 0; i < coverages.value.data.length; i++) {
      obj[coverages.value.data[i]._id] = coverages.value.data[i]
    }
    return obj;
  })
  watch(coverageIds, async (nv, ov) => {
    if (nv.length && nv.length !== ov?.length) {
      coverages.value = await coverageStore.find({ query: { _id: { $in: coverageIds.value }, covered: { $ne: 'individual' } } })
          .catch(err => {
            console.error(`Error finding coverages for plan payroll report ${err.message}`)
            return { data: [] }
          })
    }
  }, { immediate: true })

  const cols = computed(() => {
    const covs = [];
    for (let i = 0; i < (coverages.value.data || []).length; i++) {
      const cov = coverages.value.data[i] || {};
      covs.push({
        title: $limitStr(cById.value[cov._id]?.name, 30, '...'),
        tooltip: cById.value[coverageIds.value[i]]?.name,
        header: 'Total',
        value: (e) => dollarString(getC(e, `byCoverage.${cov._id}.needed`), '$', 2),
        class: i % 2 === 0 ? '__even __h' : '__h'
      })
      covs.push({
        header: 'ER',
        value: (e) => dollarString(getC(e, `byCoverage.${cov._id}.employer`), '$', 2),
        class: i % 2 === 0 ? '__even' : ''
      })
      covs.push({
        header: 'EE',
        value: (e) => dollarString(getC(e, `byCoverage.${cov._id}.employee`), '$', 2),
        class: i % 2 === 0 ? '__even' : ''
      })
    }
    const cafes = [];
    const cafs = Object.keys(props.plan?.cafe || {}).filter(a => props.plan.cafe[a].active)
    for (let i = 0; i < cafs.length; i++) {
      cafes.push({
        class: '__125',
        header: cafeKeys[cafs[i]].shortName,
        value: (e) => dollarString(getC(e, `byPlan.${cafs[i]}`), '$', 2)
      })
    }
    if (cafes[0]) cafes[0].title = 'Sec. 125 Plan Accounts'
    return [
      {
        header: 'Participant',
        value: (e) => e._fastjoin?.person?.name,
      },
      {
        header: 'Status',
        value: (e) => e.status,
      },
      {
        class: '__election_h',
        title: 'Employee Elections',
        header: 'Total',
        value: (e) => dollarString(getC(e, 'employee.total'), '$', 2)
      },
      {
        class: '__election',
        header: 'Pre-Tax',
        value: (e) => dollarString(getC(e, 'employee.preTax'), '$', 2)

      },
      {
        class: '__election',
        header: 'Post-Tax',
        value: (e) => dollarString(getC(e, 'employee.postTax'), '$', 2)

      },
      {
        class: '__net_ded',
        header: 'Net Deduction',
        tooltip: 'Total amount to withhold from employee paycheck after all elections and employer contributions. Negative balances indicate employer contributions exceed elected benefit costs.',
        value: (e) => dollarString(getC(e, 'needed.total') - (getC(e, 'employer.cafe') + getC(e, 'employer.coverages')), '$', 2)
      },
      // {
      //   class: '__net_ded',
      //   header: 'Cash Out',
      //   tooltip: 'This is the portion of cafeteria funds not assigned to a benefit - and therefore is paid out like regular wages',
      //   value: (e) => dollarString(getC(e, 'byPlan.cash') - getC(e, 'needed.postTax'), '$', 2)
      // },
      {
        class: '__er_h',
        title: 'Employer Contributions',
        header: 'Total',
        value: (e) => dollarString(getC(e, 'employer.cafe') + getC(e, 'employer.coverages'), '$', 2)
      },
      {
        class: '__er',
        header: 'Sec. 125',
        value: (e) => dollarString(getC(e, 'employer.cafe'), '$', 2),
      },
      {
        class: '__er',
        header: 'Premiums',
        value: (e) => dollarString(getC(e, 'employer.coverages'), '$', 2)
      },
      ...covs,
      ...cafes
    ]
  })

  const download = () => {
    const csvData = [[],[]];
    for(let i = 0; i < cols.value.length; i++){
      csvData[0].push(cols.value[i].title || '');
      csvData[1].push(cols.value[i].header || '');
    }
    for(let idx = 0; idx < e$.data.length; idx++){
      csvData.push([]);
      for(let i = 0; i < cols.value.length; i++) {
        csvData[idx+2].push(cols.value[i].value(e$.data[idx]))
      }
    }

    csvData.unshift([`Plan Year: ${props.planYear}`, `participant_id: ${personFilter.value?._id || 'all'}`, `Amount interval: ${interval.value}`])

    const content = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob([content], { type: 'text/csv'});
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `Enrollment_payroll_${props.planYear}_${formatDate(new Date(), 'MM-DD-YYYY')}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  onMounted(() => {
    if(route.query.status) statusFilter.value = route.query.status.split(',')
  })

</script>

<style lang="scss" scoped>

  .__table {
    width: 100%;
    overflow-x: scroll;
    overflow-y: visible;
    padding: 20px 0;
    font-family: var(--alt-font);

    table {

      border-collapse: collapse;
      width: 100%;
      font-size: .8rem;


      th {
        padding: 4px 8px;
        font-weight: 600;
        color: var(--ir-deep);
        text-align: left;
        align-content: end;
        background-color: transparent !important;
        min-width: 100px;
        max-width: 150px;
      }

      .__title {
        white-space: nowrap;
        overflow: visible;
        font-weight: 600;
        color: var(--ir-mid);
      }

      tbody {
        tr {
          td {
            text-align: left;
            padding: 4px 8px;
            //border-bottom: solid .3px rgba(0,0,0,.1)
          }

          &:last-child {
            td {
              border-bottom: none;
            }
          }

          &:nth-child(even) {
            td {
              background-image: linear-gradient(180deg, rgba(0, 0, 0, .05), rgba(0, 0, 0, .05));
              background-blend-mode: multiply;
            }
          }

        }
      }

      .__election {
        //background-color: var(--q-p0);
        color: var(--q-p9);
      }

      .__election_h {
        font-weight: 600;
        background-color: var(--q-p0);
        color: var(--q-p9);
      }

      .__net_ded {
        background-color: var(--q-a0);
        color: var(--q-a8);
        font-weight: 600;
      }

      .__er {
        //background-color: var(--q-s0);
        color: var(--q-s9);
      }

      .__er_h {
        color: var(--q-s9);
        font-weight: 600;
        background-color: var(--q-s0);
      }

      .__even {
        background-color: var(--ir-bg1);
        color: var(--ir-deep);
      }

      .__h {
        font-weight: 600;
      }

      .__125 {
        background-color: var(--q-ir-yellow-1);
      }

    }
  }


  #I_S {
    width: 100%;
  }
</style>
