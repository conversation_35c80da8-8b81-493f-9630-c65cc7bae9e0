<template>
  <q-page>
    <div class="row justify-center __top">
      <div class="__blob"></div>
      <div class="__blob1"></div>
      <div class="__s"></div>
      <div class="pd15 __cent">
        <div class="__rc __gh">
          <div class="mnh60"></div>
          <div class="__lines pw3">
            <div>
              <!--              TOP SECTION-->
              <div>
                <div></div>
                <div>
                  <div></div>
                  <div></div>
                </div>
              </div>
              <!--              2nd SECTION-->
              <div></div>

              <!--              BOTTOM SECTION-->
              <div>
                <div>
                  <div></div>
                  <div></div>
                </div>
                <div></div>
              </div>

              <!--              MIDDLE LINE-->
              <div></div>
            </div>
          </div>
        </div>
        <div class="__rc relative-position">
          <div class="q-py-md pw3">
            <div v-if="!plan._id" class="text-md tw-five">Try switching to an</div>
            <div v-else>
              <org-chip size="lg" :model-value="org"></org-chip>
<!--              <div class="text-md tw-five">offers an amazing</div>-->
            </div>
            <div class="text-xxxl tw-five _l1-3">
              Employee directed health plan
            </div>
            <div class="text-sm q-pt-sm tw-five">High quality coverage, direct care memberships, tax-free spend
              - always your choice. Never use-it-or-lose-it.
            </div>
          </div>
          <div class="pd12 pw3">
            <plan-structure
                :mm-logos="hasShop ? undefined : mms.length ? mms.map(a => byId.bySource[a._id]) : undefined"
                :hs-logos="hss.length ? hss.map(a => byId.bySource[a._id]) : undefined"></plan-structure>
          </div>
        </div>

      </div>
      <div class="__s"></div>

    </div>

    <old-way></old-way>

    <div class="_fw pw2 row justify-center">
      <div class="_cent">
        <instead-of></instead-of>
      </div>
    </div>
    <div class="_fw bg-white">


      <new-way :employee="person" :employer="org"></new-way>
    </div>

    <div class="__brk"></div>
    <div class="__brk2"></div>

    <div class="row justify-center pd10 bg-a0">
      <div class="_cent">
        <div class="row __trans">
          <div class="col-12 col-md-6 q-pb-md q-pt-xl pw3">
<!--            <div class="text-primary text-xxl tw-six _l1-2">Transparency is 👑</div>-->
            <!--            <div class="q-py-sm text-xs">You want access to high quality healthcare through your employer - you don't-->
            <!--              want to forfeit your compensation if a particular option doesn't suit you.-->
            <!--            </div>-->

            <div class="text-sm tw-five">A great employer values transparency 👑
            </div>
            <div class="text-xl tw-five q-px-xs">Wages and benefits -<br> both are <span class="text-primary">your</span> money.
              <q-icon v-if="$q.screen.gt.sm" name="mdi-chevron-right" size="45px" color="primary"></q-icon>
              <q-icon v-else name="mdi-chevron-down" size="45px" color="primary"></q-icon>
            </div>

            <div class="text-xs q-py-md">For any job, there is a <span class="text-primary tw-six">total</span> budget. Payroll tax, insurance, benefits all cut into that <span class="text-primary tw-six">total</span> - meaning they reduce your take-home pay dollar for dollar. Adjust the numbers here and see for yourself.</div>
            <div class="text-xs">That's why the best employers offer a clear allowance and let you choose where it goes - premiums, expenses, savings - or just take it home.</div>
          </div>

          <div class="col-12 col-md-6 q-py-md pw2">
            <de-mystify :household="hh" :cam="cams$.data[0]" :enrollment="e$.data[0]" :plan="plan"></de-mystify>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import PlanStructure from 'components/plans/preview/cards/PlanStructure.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import DeMystify from 'components/plans/preview/cards/DeMystify.vue';
  import OldWay from './sections/OldWay.vue';
  import NewWay from './sections/NewWay.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useOrgs} from 'stores/orgs';
  import {useRoute} from 'vue-router';
  import {useGroups} from 'stores/groups';
  import {HFind} from 'src/utils/hFind';
  import {useEnrollments} from 'stores/enrollments';
  import {loginPerson} from 'stores/utils/login';
  import {useCoverages} from 'stores/coverages';
  import {useHouseholds} from 'stores/households';
  import {useCams} from 'stores/cams';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';
  import InsteadOf from 'pages/landing/business/compare/InsteadOf.vue';

  const route = useRoute();

  const { person: lPerson, pplStore } = loginPerson();

  const planStore = usePlans();
  const orgStore = useOrgs();
  const groupStore = useGroups();
  const enrollmentStore = useEnrollments();
  const coverageStore = useCoverages();
  const hhStore = useHouseholds();
  const camStore = useCams();

  const { item: plan } = idGet({
    store: planStore,
    routeParamsPath: 'planId'
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => lPerson.value || route.query.personId)
  })

  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => person.value.household)
  })

  const { h$: e$ } = HFind({
    store: enrollmentStore,
    pause: computed(() => route.query.groupId || !person.value._id),
    params: computed(() => {
      return {
        query: {
          $sort: { planYear: -1 },
          person: person.value._id,
          plan: plan.value._id
        }
      }
    })
  })
  const groupId = computed(() => {
    if (route.params.groupId) return route.params.groupId
    else {
      let groupId;
      const e = e$.data[0];
      if (!e) return undefined
      else groupId = e.group;
      if (!groupId) {
        groupId = (person.value.inGroups || []).filter(a => (plan.value.groups || []).includes(a))[0]
      }
      return groupId
    }
  })
  const { item: group } = idGet({
    store: groupStore,
    value: groupId
  })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => group.value.org || plan.value.org)
  })

  const { h$: cams$ } = HFind({
    store: camStore,
    limit: ref(1),
    pause: computed(() => !person.value._id || !org.value._id),
    params: computed(() => {
      return {
        query: {
          org: org.value._id,
          person: person.value._id,
          off: { $ne: true },
          $sort: { createdAt: -1 }
        }
      }
    })
  })

  const coverageIds = computed(() => Object.keys(plan.value.coverages || {}))
  const { h$: c$ } = HFind({
    store: coverageStore,
    limit: computed(() => coverageIds.value.length),
    params: computed(() => {
      return {
        _id: { $in: coverageIds.value,  type: { $in: ['hs', 'mm', 'hra']}}
      }
    })
  })

  const hasShop = computed(() => c$.data.filter(a => ['mm', 'hra', 'hs'].includes(a.type) && a.shop).length)

  const mms = computed(() => c$.data.filter(a => a.type === 'mm'))
  const hss = computed(() => c$.data.filter(a => a.type === 'hs'))

  const { byId } = manageFindUploads({ sources: c$, paths: ['carrierLogo']})
</script>

<style lang="scss" scoped>
  //$border: solid .5px var(--q-p6);
  $border: solid .5px rgba(255, 255, 255, .5);
  $lines: repeating-linear-gradient(
          -45deg,
          var(--q-p4) 0px,
          var(--q-p4) 2px,
          transparent 2px,
          transparent 9px
  );
  $lines-rev: repeating-linear-gradient(
          45deg,
          var(--q-p4) 0px,
          var(--q-p4),
          transparent 2px,
          transparent 9px
  );

  .__top {
    background: var(--q-primary);
    position: relative;
    overflow: hidden;
    border-top: $border;
    color: white;

    .__cent {
      border-left: $border;
      border-right: $border;
      position: relative;
      z-index: 1;
      width: max(600px, min(92vw, 1300px));
      max-width: 100%;

      .__gh {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;

        .__lines {
          border-left: $border;
          height: 100%;
          position: relative;

          > div {
            display: grid;
            grid-template-rows: 1fr min(350px, 50vh) 1fr;
            height: 100%;
            position: relative;
            border-left: $border;
            border-right: $border;

            > div {
              &:nth-child(1) {
                width: 100%;
                height: 100%;
                display: grid;
                grid-template-rows: 55fr 45fr;

                > div {
                  &:nth-child(2) {
                    width: 100%;
                    height: 100%;
                    display: grid;
                    grid-template-columns: 1fr 1fr;

                    > div {
                      &:first-child {
                        background: $lines;
                      }

                      &:nth-child(2) {
                        background: $lines-rev;
                      }
                    }
                  }
                }
              }

              &:nth-child(2) {
                height: 100%;
                background: rgba(255, 255, 255, .1);
                display: flex;
                align-items: center;
              }

              &:nth-child(3) {
                border-top: $border;

                width: 100%;
                height: 100%;
                display: grid;
                grid-template-rows: 55fr 45fr;

                > div {
                  &:nth-child(1) {
                    width: 100%;
                    height: 100%;
                    display: grid;
                    grid-template-columns: 1fr 1fr;

                    > div {
                      &:first-child {
                        background: $lines;
                      }

                      &:nth-child(2) {
                        background: $lines-rev;
                      }
                    }
                  }
                }
              }

              &:nth-child(4) {
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                height: 100%;
                width: 0;
                border-left: $border;
              }
            }
          }
        }
      }

      .__rc {
        width: 100%;
        display: grid;
        grid-template-columns: 50fr 50fr;
        align-items: center;


        > div {
          width: 100%;
        }
      }

      @media screen and (max-width: 1023px) {
        .__rc {
          grid-template-columns: 1fr;
        }
      }
    }

    .__c {
      width: 100%;
      padding: 30px 15px;
      border-radius: 15px;
      //background: rgba(255,255,255,.8);
    }

    .__blob {
      position: absolute;
      z-index: 0;
      width: 120vw;
      height: 120vw;
      border-radius: 50%;
      animation: roam 20s infinite;
      opacity: 1;
      background: radial-gradient(var(--q-p4) -50%, transparent 50%);
      transform: translate(5%, 0);
      bottom: -50vw;
      left: -60vw;
    }

    .__blob1 {
      position: absolute;
      z-index: 0;
      width: 120vw;
      height: 120vw;
      border-radius: 50%;
      animation: roam 20s infinite;
      opacity: 1;
      background: radial-gradient(var(--q-p4) -50%, transparent 50%);
      transform: translate(5%, 0);
      top: -50vw;
      right: -50vw;
    }
  }

  .__s {
    width: 50px;
    background: $lines-rev;

    &:last-child {
      background: $lines;
    }
  }

  .__trans {
    padding: 35px max(10px, 2vw);
    background: white;
    border-radius: 25px;
    background: repeating-linear-gradient(
            -45deg,
            var(--q-a1) 0px,
            var(--q-a1),
            white 2px,
            white 9px)
  }


  .__brk, .__brk2 {
    width: 100%;
    height: 50px;
    background: repeating-linear-gradient(
            45deg,
            var(--q-p2) 0px,
            var(--q-p2) 2px,
            var(--q-primary) 2px,
            var(--q-primary) 9px
    )
  }
  .__brk2 {
    background: repeating-linear-gradient(
            -45deg,
            white 2px,
            white 0px,
            var(--q-accent) 2px,
            var(--q-accent) 9px
    )
  }
</style>
