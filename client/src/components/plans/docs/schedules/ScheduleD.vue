<template>
  <div style="width: 100%;">
    <div style="padding: 20px 0; font-size: 1.5rem; font-weight: 700; color: #6e6e6e">Schedule D</div>
    <div style="font-size: 1.25rem; font-weight: 600;">Limits</div>
    <div style="padding: 25px 0; width: 100%;">
      <table style="border-collapse: collapse; text-align: left; width: 100%; max-width: 700px;">
        <template v-for="(k, i) in features" :key="`k-${i}`">
          <tr style="border-bottom: solid 1px #999;" >
            <th style="font-weight: 600; padding: 10px 0;">{{keys[k]?.shortName}} Annual Limit</th>
          </tr>
          <tr>
            <td>Single</td>
            <td>{{dollarString(complianceSettings[k]?.limits?.single, '$', 0, 'N/A')}}</td>
          </tr>
          <tr>
            <td>Family</td>
            <td>{{dollarString(complianceSettings[k]?.limits?.family, '$', 0, 'N/A')}}</td>
          </tr>
        </template>

      </table>

    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {complianceSettings} from 'components/plans/utils';
  import { cafeKeys, hraKeys } from 'components/plans/utils';
  import {dollarString} from 'src/utils/global-methods';

  const store = usePlans();

  const props = defineProps({
    plan: { required: false }
  })

  const limit = ref(20);

  const { item: fullPlan } = idGet({
    store,
    value: computed(() => props.plan),
    onWatch: (val) => {
      const l = Object.keys(val?.coverages || {})?.length;
      if (l) limit.value = l;
    }
  })

  const keys = ref({...cafeKeys, ...hraKeys});
  const features = computed(() => [...Object.keys(fullPlan.value?.cafe || {}).filter(a => fullPlan.value.cafe[a].active), ...Object.keys(fullPlan.value?.hra || {}).filter(a => fullPlan.value.hra[a].active)])

</script>

<style lang="scss" scoped>

</style>
