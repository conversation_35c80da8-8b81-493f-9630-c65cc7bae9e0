<template>
  <div class="_fw">
    <!--    <div class="q-pa-md">-->
    <!--      <div class="tw-six font-1-1-4r cursor-pointer">-->
    <!--        {{ plan?.name }}-->
    <!--      </div>-->
    <!--      <div class="text-grey-7 tw-five">Plan Document Editor</div>-->
    <div class="row items-center q-py-xs">
      <q-space></q-space>
      <remove-proxy-btn v-if="active" name="doc" :base="{ icon: 'mdi-close' }"
                     :label="`Remove ${docPaths[usePath]?.shortName || 'Plan'} Doc`"
                     @remove="remove(usePath)">
      </remove-proxy-btn>
    </div>
    <q-tabs class="bg-grey-2 br5" active-color="primary" align="left" :model-value="usePath"
            @update:model-value="setPath">
      <q-tab class="tw-six" v-for="(k, i) in Object.keys(docPaths).filter(a => docPaths[a].active)" :key="`tab-${i}`"
             :name="k" :label="docPaths[k].shortName"></q-tab>
    </q-tabs>
    <!--    </div>-->

    <div class="_fw" v-if="!active">

      <div v-show="!docLoading" class="_fw">
        <q-tabs active-color="primary" no-caps v-model="searchTab" align="left">
          <q-tab label="Templates" name="template"></q-tab>
          <q-tab label="Your Docs" name="docs"></q-tab>
        </q-tabs>
        <q-tab-panels animated class="_panel" v-model="searchTab">
          <q-tab-panel class="_panel" name="template">
            <docs-search
                :plan-class="helpers.class"
                :sub-class="helpers.subClass"
                @update:model-value="selectTemplate"
            ></docs-search>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="docs">
            <docs-cleanup
                :plan="plan"
                :plan-class="helpers.class"
                :sub-class="helpers.subClass"
                @select="selectExisting"
            ></docs-cleanup>
          </q-tab-panel>
        </q-tab-panels>

      </div>
      <div v-if="docLoading" class="q-pa-lg">
        <q-spinner color="primary" size="50px"></q-spinner>
      </div>
    </div>
    <div class="_fw" v-else>
      <plan-doc-form v-if="!reload" :model-value="active"></plan-doc-form>
    </div>
  </div>
</template>

<script setup>
  import DocsSearch from 'components/plans/docs/lists/DocsSearch.vue';
  import PlanDocForm from 'components/plans/docs/forms/PlanDocForm.vue';
  import DocsCleanup from 'components/plans/docs/lists/DocsCleanup.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {useRoute, useRouter} from 'vue-router';
  import {computed, nextTick, ref, watch} from 'vue';
  import {hraKeys, cafeKeys, planDocPaths} from '../../utils';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {$errNotify} from 'src/utils/global-methods';
  import {usePlanDocs} from 'stores/plan-docs';
  import {_get} from 'symbol-syntax-utils';

  const store = usePlans();
  const docStore = usePlanDocs();
  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    path: String,
    planId: String
  })

  const plan_id = computed(() => props.planId || route.query.planId);

  const { item: plan } = idGet({
    value: plan_id,
    store
  })

  const { docPaths } = planDocPaths(plan);

  const searchTab = ref('template');
  const usePath = ref('doc');

  const active = computed(() => usePath.value ? _get(plan.value, usePath.value) : undefined)

  const helpers = computed(() => {
    const arr = (usePath.value || '').split('.');
    if (arr.length === 1) return { class: usePath.value }
    else {
      const obj = {
        cafe: cafeKeys,
        hra: hraKeys
      }
      const classes = obj[arr[0]] || {}
      return {
        class: classes[arr[1]]?.class,
        subClass: classes[arr[1]].subClass
      }
    }
  })

  const docLoading = ref(false);

  const selectTemplate = async (dt) => {
    if (plan.value) {
      docLoading.value = true;
      const { name, class: dtClass, subClass, sections } = dt;
      console.log('select template', dt, usePath.value);
      const newDoc = await docStore.create({
        plan: plan.value._id,
        name,
        class: dtClass,
        subClass,
        sections,
        template: dt._id,
        path: usePath.value
      })
          .catch(() => $errNotify(`Error copying template - try again`))
      console.log('created', newDoc);
      await store.patch(plan.value._id, { $set: { [usePath.value]: newDoc._id } })
      docLoading.value = false;
    }
  }

  const remove = async (path) => {
    await store.patch(plan.value._id, { $unset: { [path]: '' } })
  }

  const selectExisting = async (doc) => {
    await store.patch(plan.value._id, { $set: { [usePath.value]: doc._id } })
  }

  const reload = ref(false);

  const setPath = (p) => {
    reload.value = true;
    usePath.value = p;
    nextTick(() => reload.value = false);
    const { href } = router.resolve({ ...route, query: { ...route.query, path: p } })
    window.history.pushState({}, '', href)
  }

  watch(() => route.query.path, (nv) => {
    if (nv) setPath(nv.split('_').join('.'));
  }, { immediate: true })

  watch(() => props.path, (nv) => {
    if (nv) setPath(nv)
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
