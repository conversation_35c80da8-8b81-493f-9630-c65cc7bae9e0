<template>
  <div class="_fw">
        <div class="_form_grid">
          <template v-for="(k, i) in Object.keys(docPaths)" :key="`dp-${i}`">
            <div class="_form_label">
              {{ docPaths[k].shortName }} Docs
            </div>
            <div class="q-pa-sm">
              <q-chip
                  v-if="!docPaths[k].doc && !docLoading"
                  clickable
                  @click="openDocAssign(k)"
              >
                <span class="q-mr-sm">Add Docs</span>
                <q-icon color="primary" name="mdi-plus"></q-icon>
              </q-chip>
              <q-spinner color="primary" v-else-if="docLoading"></q-spinner>
              <div v-else class="flex items-center">
                <plan-doc-chip
                    outline
                    :model-value="docPaths[k].doc"
                    clickable
                    @click="openDocAssign(k)"
                ></plan-doc-chip>
                <q-btn size="sm" dense flat icon="mdi-dots-vertical">
                  <q-menu>
                    <div class="w200 bg-white">
                      <q-list separator>
                        <remove-proxy-btn no-caps icon="" @remove="remove(k)" remove-label="Confirm Remove">
                          <span class="text-ir-text q-mr-sm">Remove Module</span>
                          <q-icon color="red" name="mdi-delete"></q-icon>
                        </remove-proxy-btn>
                      </q-list>
                    </div>
                  </q-menu>
                </q-btn>
              </div>
            </div>
          </template>

        </div>

  </div>
</template>

<script setup>
  import PlanDocChip from 'components/plans/docs/cards/PlanDocChip.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {idGet} from 'src/utils/id-get';
  import {planDocPaths} from '../../utils';

  const store = usePlans();

  const emit = defineEmits(['update:model-value', 'update:path'])
  const props = defineProps({
    modelValue: { required: true },
  })

  const { item:plan } = idGet({
    value: computed(() => props.modelValue),
    store
  })

  const docLoading = ref(false);

  const { docPaths } = planDocPaths(plan);

  const remove = async (path) => {
    store.patchInStore(plan.value._id, { [path]: undefined })
    await store.patch(plan.value._id, { $unset: { [path]: '' }})
  };
  const openDocAssign = (path) => {
    emit('update:path', path);
  }
</script>

<style lang="scss" scoped>

</style>
