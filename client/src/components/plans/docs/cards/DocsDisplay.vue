<template>
  <div class="_fw">
    <div class="q-py-lg row justify-end items-center" v-if="!noPrint || !noShare">
      <q-btn v-if="!noShare" class="q-mr-sm _p_btn" @click="share" label="Share" icon-right="mdi-share"></q-btn>
      <q-btn v-if="!noPrint" class="_s_btn" label="Print" icon-right="mdi-file-pdf-box" @click="openPrint"></q-btn>
    </div>
    <div class="_fw">

      <div class="q-py-sm font-1-1-2r tw-six">{{ fullPlan?.name }}</div>
      <div class="q-pb-lg">
        <div class="q-pb-sm">
          <div class="text-grey-7 font-1r">Provided for:</div>
        </div>
        <div class="_fw">
          <div class="row  items-center" v-for="(orgId, i) in Object.keys(byOrg)" :key="`grp-${i}`">
            <default-chip :store="orgStore" :model-value="byOrg[orgId].org" :chip-attrs="{}" :use-atc-store="useAtcStore"></default-chip>
            <div>
              <span v-for="(grp, idx) in byOrg[orgId].groups || []"
                    :key="`grp-${i}-${idx}`">{{ idx === 0 ? '' : ', ' }}{{ grp.name }}</span>
            </div>
          </div>
        </div>
        <div class="font-3-4-r tw-six text-grey-8 q-pa-sm">
          ERISA Plan #: {{fullPlan?.info?.numberEin?.split('-').join('')}}{{fullPlan?.info?.number}}
        </div>
      </div>
    </div>
    <div class="q-pa-md">
      <q-btn flat no-caps @click="aichat = true">
        <ai-logo opaque></ai-logo>
        <span class="q-ml-sm tw-six">Ask CommonAI</span>
      </q-btn>

      <common-dialog v-model="aichat" setting="right">
        <ai-plan-doc-chat :org-id="fullPlan?.org" :plan-id="fullPlan?._id" :model-value="generalDoc"></ai-plan-doc-chat>
      </common-dialog>

    </div>
    <q-list separator v-if="isSet" class="text-left">
      <q-expansion-item group="121" v-for="(k, i) in Object.keys(docs)" :key="`plan-${i}`">
        <template v-slot:header>
          <q-item class="_fw">
            <q-item-section avatar>
              <q-icon name="mdi-file-document" :color="planClasses[docs[k].class]?.color"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label class="font-1-1-4r tw-six text-grey-8">{{ docs[k].name }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <doc-display :model-value="docs[k]"></doc-display>
      </q-expansion-item>
      <q-expansion-item group="121" v-for="(k, i) in Object.keys(schedules)" :key="`schedule-${i}`">
        <template v-slot:header>
          <q-item class="_fw">
            <q-item-section avatar>
              <q-icon name="mdi-file-document" color="blue-10"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label class="font-1-1-4r tw-six text-grey-8">Schedule {{ k.toUpperCase() }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <div class="q-pa-md">
          <component
              :is="schedules[k].component"
              v-bind="schedules[k].attrs"
          ></component>
        </div>
      </q-expansion-item>
    </q-list>
    <div v-else class="q-pa-lg">
      <q-spinner color="primary" size="50px"></q-spinner>
    </div>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import DocDisplay from 'components/plans/docs/cards/DocDisplay.vue';
  import AiPlanDocChat from 'components/plans/docs/ai/AiPlanDocChat.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'stores/atc-store';

  import {planClasses} from '../../utils';
  import {useOrgs} from 'stores/orgs';
  import {docDisplay} from '../utils/display';
  import {computed, ref} from 'vue';


  const orgStore = useOrgs();

  const props = defineProps({
    plan: { required: false },
    mobile: Boolean,
    noPrint: Boolean,
    noShare: Boolean,
  })

  const { share, byOrg, isSet, docs, fullPlan, openPrint, schedules, planDocs } = docDisplay(computed(() => props.plan))

  const aichat = ref(false)
  const generalDoc = computed(() => {
    return (planDocs.value.general || planDocs.value[Object.keys(planDocs.value)[0]]) || {}
  })
</script>

<style lang="scss" scoped>
  .__grps {
    padding: 20px 10px;
    border-radius: 10px;
    border: solid 3px #999;
  }
</style>
