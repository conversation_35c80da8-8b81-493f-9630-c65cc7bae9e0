<template>
  <q-chip
      v-bind="{
        color: planClasses[doc?.class]?.color || 'grey-5',
        class: 'tw-six',
        label: doc?.name,
        icon: 'mdi-file-document',
        ...$attrs
      }"
  >
  </q-chip>
</template>

<script setup>

  import {planClasses} from 'components/plans/utils';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {usePlanDocs} from 'stores/plan-docs';
  import {useDocTemplates} from 'stores/doc-templates';

  const props = defineProps({
    modelValue: { required: true },
    template: <PERSON>olean
  })

  const { item: doc } = idGet({
    value: computed(() => props.modelValue),
    store: props.template ? useDocTemplates() : usePlanDocs()
  })
</script>

<style lang="scss" scoped>

</style>
