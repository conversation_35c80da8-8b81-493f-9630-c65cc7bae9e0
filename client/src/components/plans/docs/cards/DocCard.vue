<template>
  <div class="_fw relative-position">
    <div class="tw-six text-ir-grey-8">{{doc?.name}}</div>
    <div class="font-7-8r text-ir-grey-7">{{doc?.description}}</div>
    <div class="row items-center q-pt-sm">
      <div class="font-3-4r">Type:</div>
      <plan-doc-chip outline :model-value="doc" template></plan-doc-chip>
    </div>
  </div>
</template>

<script setup>
  import PlanDocChip from 'components/plans/docs/cards/PlanDocChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useDocTemplates} from 'stores/doc-templates';
  import { usePlanDocs } from 'stores/plan-docs';

  const props = defineProps({
    modelValue: { required: true },
    template: Boolean
  })

  const { item: doc } = idGet({
    store: props.template ? useDocTemplates() : usePlanDocs(),
    value: computed(() => props.modelValue)
  })

</script>

<style lang="scss" scoped>

</style>
