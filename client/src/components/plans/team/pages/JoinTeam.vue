<template>
  <q-page class="bg-a0 mnh80">
    <div class="row justify-center _fh">
      <div class="_sent pd4 pw2 _fh mnh80">
        <template v-if="!login?._id">
          <div class="__c">
            <auth-card></auth-card>
          </div>
        </template>
        <template v-else>
          <div class="__c">
            <div class="text-xs tw-five">You've been invited to bid on the
              <role-chip class="_i_i text-xs tw-six" :model-value="$route.params.role" color="ir-grey-2"></role-chip>
              role for
            </div>

            <div class="text-xs _l1-1 tw-six alt-font">{{ plan?.name }}</div>
          </div>

          <div class="__c">

            <div class="q-pb-md text-xs tw-five">Choose an organization you will bid as</div>
            <div class="_form_grid _f_g_r">
              <div class="_form_label">Your Organization</div>
              <div class="q-pa-sm">
                <div>
                  <q-chip class="_i_i" v-if="!orgId" color="ir-grey-2" clickable icon-right="mdi-menu-down"
                          label="Select Org">
                  </q-chip>
                  <org-chip class="_i_i" color="ir-grey-2" v-else :model-value="orgId">
                    <template v-slot:right>
                      <q-icon name="mdi-menu-down"></q-icon>
                    </template>
                  </org-chip>
                  <q-menu>
                    <div class="w300 mw100 q-pa-sm bg-white">
                      <q-input dense filled v-model="search.text">
                        <template v-slot:prepend>
                          <q-icon name="mdi-magnify"></q-icon>
                        </template>
                      </q-input>
                      <q-list separator>
                        <q-item-label header>Select an organization</q-item-label>
                        <default-item
                            v-for="(org, i) in o$.data"
                            :key="`o-${i}`"
                            :model-value="org" clickable
                            :use-atc-store="useAtcStore"
                            @click="orgId = org._id"
                        ></default-item>
                      </q-list>
                    </div>
                  </q-menu>
                </div>
              </div>
              <div class="_form_label">DBA</div>
              <div class="q-pa-sm">
                <div>
                  <q-chip v-if="!hostId" color="ir-grey-2" icon-right="mdi-menu-down" label="Select DBA"></q-chip>
                  <host-chip v-else :model-value="host" color="ir-grey-2">
                    <template v-slot:right>
                      <q-icon name="mdi-menu-down"></q-icon>
                    </template>
                  </host-chip>

                  <q-menu>
                    <div class="w300 mw100 q-pa-sm bg-white">
                      <q-list separator>
                        <q-item-label header>Select DBA</q-item-label>
                        <default-item name-path="dba" clickable @click="hostId = h._id"
                                      v-for="(h, i) in hostOptions.data" :key="`h-${i}`"
                                      :model-value="h" :use-atc-store="useAtcStore"></default-item>
                      </q-list>
                    </div>
                  </q-menu>
                </div>
              </div>
            </div>

            <div class="row justify-end q-pt-lg">
              <q-btn @click="accept" push v-if="host?._id" class="_a_btn tw-six" no-caps :label="`Enable ${host?.dba} to bid`"></q-btn>
            </div>
          </div>
        </template>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import AuthCard from 'components/auth/AuthCard.vue';
  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import HostChip from 'components/hosts/cards/HostChip.vue';
  import {useAtcStore} from 'stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {loginPerson} from 'stores/utils/login';
  const { person, login } = loginPerson()
  import {computed, ref, watch} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useHosts} from 'stores/hosts';
  import {useOrgs} from 'stores/orgs';
  import {HFind} from 'src/utils/hFind';
  import {useRoute} from 'vue-router';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';
  import {HQuery} from 'src/utils/hQuery';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const route = useRoute();

  const planStore = usePlans();
  const hostStore = useHosts();
  const orgStore = useOrgs();

  const envStore = useEnvStore();
  const { getOrgId:lsOrg } = contextItems(envStore);

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => route.params.planId)
  })

  const hostOptions = ref({ total: 0, data: [] })
  const hostId = ref();
  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => hostId.value || LocalStorage.getItem('host_id'))
  })

  const orgId = ref();
  watch(orgId, async (nv, ov) => {
    if (nv && nv !== ov) {
      hostOptions.value = await hostStore.find({ query: { org: nv } })
      if (hostOptions.value.total === 1) hostId.value = hostOptions.value.data[0]._id
    }
  }, { immediate: true })


  watch(lsOrg, (nv, ov) => {
    if (nv && nv !== ov) orgId.value = nv;
  }, { immediate: true })

  const { search, searchQ } = HQuery({})
  const { h$: o$ } = HFind({
    store: orgStore,
    pause: computed(() => !!orgId.value || !person.value?._id),
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          _id: { $in: (person.value?.inOrgs || []).filter(a => !!a) },
          ...searchQ.value
        }
      }
    })
  })

  const accept = async () => {
    const h = await hostStore.patch(host.value._id, { updatedAt: new Date() }, { runJoin: { rfp: { planId: plan.value._id, hostId: host.value._id, role: route.params.role }}})
        .catch(err => {
          $errNotify(`Error enabling bids: ${err.message}`)
          return undefined
        })
    if(h) $successNotify(`You can now create bids for ${plan.value.name}`)
  }


</script>

<style lang="scss" scoped>

  .__c {
    margin: 20px 0;
    padding: 30px min(30px, 2vw);
    border-radius: 15px;
    background: white;
    box-shadow: 2px 2px 12px -2px var(--q-a2);
  }
</style>
