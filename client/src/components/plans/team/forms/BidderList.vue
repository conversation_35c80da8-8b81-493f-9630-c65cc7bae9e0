<template>
  <div class="_fw">
    <q-list separator>
      <q-item-label header>Approved to bid for {{ role?.label }}</q-item-label>
      <q-input dense filled v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify">
          </q-icon>
        </template>
      </q-input>
      <default-item
          name-path="dba"
          v-for="(item, idx) in open.data"
          :key="`open-${idx}`"
          :model-value="item"
          :use-atc-store="useAtcStore">
        <template v-slot:side>
          <remove-proxy-btn dense flat icon="mdi-close" :name="item.dba" @remove="emit('remove', item)"></remove-proxy-btn>
        </template>
      </default-item>
    </q-list>
  </div>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed} from 'vue';
  import {useHosts} from 'stores/hosts';
  import {useAtcStore} from 'stores/atc-store';
  import {HQuery} from 'src/utils/hQuery';

  const hostStore = useHosts();

  const emit = defineEmits(['remove', 'add']);
  const props = defineProps({
    role: Object,
    hosts: {
      default: () => {
        return []
      }
    }
  })

  const { search, searchQ } = HQuery({ keys: ['dba']})
  const { h$: open } = HFind({
    store: hostStore,
    limit: computed(() => Math.min(props.hosts.length, 20)),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: props.hosts }
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
