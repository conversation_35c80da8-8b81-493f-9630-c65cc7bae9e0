<template>
  <div class="_fw">
    <div class="row justify-center">

      <div class="_form_grid">
        <div class="_form_label">Plan Name</div>
        <div class="__r">
          <q-input dense filled v-model="form.name" @update:model-value="autoSave('name')"></q-input>
        </div>
        <div class="_form_label">Features</div>
        <div class="_fw q-pa-md">
          <div class="font-7-8r">Types of benefits employees can elect (can edit later).</div>
          <div class="font-3-4r text-italic">Features don't change your price - still free for 30 days and then $5
            PEPM
          </div>
          <div class="__cafe" v-for="(k, i) in cKeys" :key="`cafe-${i}`">
            <div>
              <q-checkbox
                  :disable="['cash', 'pop'].includes(k)"
                  :model-value="!!(form.cafe || {})[k]?.active"
                  @update:model-value="setActive(k, $event)"
              ></q-checkbox>
            </div>
            <div class="flex items-center">
              <div>{{ cafeKeys[k].name }}</div>
              <q-icon class="q-ml-sm" name="mdi-information" color="accent"></q-icon>
              <q-tooltip class="text-xxs tw-six">{{ cafeKeys[k].description }}</q-tooltip>
            </div>
          </div>
        </div>

        <div></div>
        <div class="__r">
          <q-btn :disable="generating" @click="generateDocs()" class="_a_btn tw-six" no-caps>
            <span class="q-mr-sm">Generate Docs</span>
            <q-icon v-if="!generating" name="mdi-file-refresh"></q-icon>
            <q-spinner v-else color="white"></q-spinner>
          </q-btn>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {usePlans} from 'stores/plans';
  import {HForm, HSave} from 'src/utils/hForm';
  import {cafeDefaults, cafeKeys} from 'components/plans/utils';
  import {useDocTemplates} from 'stores/doc-templates';
  import {$infoNotify} from 'src/utils/global-methods';
  import {usePlanDocs} from 'stores/plan-docs';

  const orgStore = useOrgs();
  const planStore = usePlans();
  const dtStore = useDocTemplates();
  const planDocStore = usePlanDocs();

  const cKeys = Object.keys(cafeKeys).filter(a => !['def'].includes(a));

  const props = defineProps({
    modelValue: { required: true },
    smb: Boolean,
    store: { required: true }
  })

  const item = computed(() => props.modelValue);

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => item.value.plan)
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => plan.value.org || item.value.org),
    onWatch: (val) => {
      if (val?.name && !form.value.name) {
        setTimeout(() => {
          // console.log('setting form name', val.name)
          form.value.name = `${val.name} Employee Health Plan`
        }, 1000)
      }
    }
  })

  const cafeFn = (key, defs) => {
    return {
      limits: {
        family: 0,
        single: 0
      },
      ...cafeDefaults()[key || 'cash'],
      ...defs
    }
  }

  const formFn = (defs) => {
    const obj = { cafe: {} };
    for (const k of cKeys) {
      obj.cafe[k] = cafeFn(k)
    }
    const merged = { ...obj, ...defs }
    merged.cafe.pop = { ...merged.cafe.pop, active: true }
    merged.cafe.cash = { ...merged.cafe.cash, active: true }
    return merged;
  }

  const { form, save } = HForm({
    store: planStore,
    value: plan,
    beforeFn: (val) => {
      if (!val.org) val.org = org.value._id
      return val;
    },
    afterFn: (val) => {
      if (item.value._id) props.store.patch(item.value._id, { plan: val._id })
    },
    formFn
  })

  const { autoSave } = HSave({ form, save, store: planStore, pause: computed(() => !form.value._id) })

  const generating = ref(false)
  const generateDocs = async (path) => {
    if (!form.value._id) {
      if (!form.value.name) return $infoNotify('Name plan before docs are generated')
      await save()
      console.log('saved', form.value);
    }
    generating.value = true;
    try {
      const classMap = {
        'hsa': { class: '125', subClass: 'HSA' },
        'fsa': { class: '125', subClass: 'FSA' },
        'dcp': { class: '125', subClass: 'DCAP' },
        'pop': { class: '125', subClass: 'POP' },
        'core': { class: 'core' },
        'spd': { class: 'spd' }
      }
      const paths = path ? [path, 'spd', 'core'] : [...Object.keys(form.value.cafe).filter(a => form.value.cafe[a].active), 'spd', 'core'];
      for (const p of paths) {
        const docs = await dtStore.find({
          query: {
            smb: props.smb,
            $sort: { priority: 1 },
            $limit: 1, ...classMap[p]
          }
        })
            .catch(err => console.error(err.message))
        if (docs?.total) {
          const plandoc = { plan: form.value._id, template: docs.data[0]._id };
          ['name', 'description', 'class', 'subClass', 'sections', 'smb'].forEach(p => {
            plandoc[p] = docs.data[0][p]
          })
          const added = await planDocStore.create(plandoc)
              .catch(err => console.error(err.message))
          if (added) {
            if (['spd', 'core'].includes(p)) {
              const map = { spd: 'spd', core: 'doc' }
              form.value[map[p]] = added._id
              autoSave(map[p])
            }
          }
        }
      }
    } catch (err) {
      console.error(err.message)
    } finally {
      generating.value = false;
    }
  }

  const setActive = (k, val) => {
    form.value.cafe[k] = { ...form.value.cafe[k], active: val }
    if (!form.value.cafe[k].doc) generateDocs(k);
    else autoSave(`cafe.${k}`)
  }


</script>

<style lang="scss" scoped>

  .__r {
    padding: 10px;
    width: 100%;
    max-width: 500px;
    min-width: min(500px, 85vw)
  }

  .__cafe {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;

    > div {
      padding: 3px;

      &:nth-child(2) {
        font-weight: 600;
        font-size: 1rem;
        color: var(--ir-deep);
      }
    }
  }
</style>
