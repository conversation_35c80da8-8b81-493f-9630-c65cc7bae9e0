<template>
  <div class="_fw">
    <template v-if="org._id || person._id">
      <div class="font-7-8r tw-six text-ir-mid">Your Company</div>
      <q-chip color="ir-bg2" clickable>
        <default-avatar v-if="org._id" :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
        <span v-if="org._id">{{ org.dba || org.name }}</span>
        <span v-else>Select Company</span>
        <q-icon name="mdi-menu-down" class="q-ml-sm"></q-icon>

        <q-popup-proxy>
          <div class="w400 mw100 q-pa-sm">
            <q-input v-model="search.text" dense filled>
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>
            <q-list separator>
              <q-item clickable @click="addDialog = true">
                <q-item-section avatar>
                  <q-icon color="primary" name="mdi-plus"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label>Add New</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-for="(o, i) in o$.data" :key="`o-${i}`" clickable @click="selectedOrg = o">
                <q-item-section avatar>
                  <default-avatar :model-value="o" :use-atc-store="useAtcStore"></default-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ o.dba || o.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-popup-proxy>
      </q-chip>

    </template>
    <template v-if="person._id">
      <div class="font-7-8r tw-six text-ir-mid q-pt-sm">Your Role</div>
      <template v-if="c$.data[0]">
        <comp-card service="cams" :model-value="c$.data[0]"></comp-card>
      </template>
      <div v-else class="q-pa-lg text-italic font-1r">No roles added for you yet. If this seems like an error - contact support.</div>
    </template>
    <template v-else>
      <div class="q-py-md">
        <trad-login></trad-login>
      </div>
    </template>

    <common-dialog v-model="addDialog" setting="smmd">
      <div class="_fw q-pa-md bg-ir-bg">
        <org-form @update:model-value="addOrg"></org-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import TradLogin from 'components/auth/traditional/TradLogin.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import CompCard from 'components/comps/cards/CompCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import OrgForm from 'components/orgs/forms/OrgForm.vue';

  import {loginPerson} from 'stores/utils/login';
  import {useAtcStore} from 'stores/atc-store';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useOrgs} from 'stores/orgs';
  import {HQuery} from 'src/utils/hQuery';
  import {useCams} from 'stores/cams';
  import {useEnvStore} from 'stores/env';


  const orgStore = useOrgs();
  const camStore = useCams();
  const envStore = useEnvStore();

  const { person } = loginPerson();

  const selectedOrg = ref();
  const addDialog = ref(false);

  const addOrg = (val) => {
    selectedOrg.value = val;
    envStore.setOrgId(val._id)
    addDialog.value = false;
  }
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => selectedOrg.value || envStore.getOrgId)
  })

  const noPersonPause = computed(() => !person.value._id);
  const { h$: c$ } = HFind({
    store: camStore,
    pause: noPersonPause,
    params: computed(() => {
      return {
        query: {
          person: person.value._id,
          org: org.value._id
        }
      }
    })
  })

  const { search, searchQ } = HQuery({})
  const { h$: o$ } = HFind({
    store: orgStore,
    pause: noPersonPause,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: c$.data.map(a => a.org).filter(a => !!a) }
        }
      }
    })
  })

</script>

<style lang="scss" scoped>

</style>
