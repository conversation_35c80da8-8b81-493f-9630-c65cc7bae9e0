<template>
  <div class="_fw">

    <template v-if="!canEdit.ok">
      <div class="q-pa-lg">You do not have permission to view this banking detail</div>
    </template>
    <template v-else>
      <add-edit-account
          v-if="!noAdd"
          :org="fullOrg"
          :model-value="editing"
          :dialog="!!editing"
          @update:model-value="setEditing"
          @update:dialog="toggleDialog"
      ></add-edit-account>
      <q-list separator>
        <account-item
            :model-value="acct" v-for="acct in a$.data"
            :key="acct._id"
            clickable
            @click="setEditing(acct)"
            >
          <template v-slot:side>
            <slot name="side" v-bind="{ acct }">
              <q-icon v-if="!acct.verification?.verified" color="accent" name="mdi-alert-box">
                <q-tooltip class="text-xxs tw-six">Finish account verification before using</q-tooltip>
              </q-icon>
            </slot>
          </template>
        </account-item>
      </q-list>
    </template>


  </div>
</template>

<script setup>
  import AccountItem from 'components/accounts/cards/AccountItem.vue';
  import AddEditAccount from 'components/accounts/forms/AddEditAccount.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {useBankAccounts} from 'stores/bank-accounts';
  import {HFind} from 'src/utils/hFind';

  const { login } = loginPerson()

  const store = useOrgs();
  const accountStore = useBankAccounts();

  const emit = defineEmits(['update:model-value', 'add-edit'])
  const props = defineProps({
    org: { required: true },
    noEdit: Boolean,
    noAdd: Boolean,
    query: Object
  })

  const { item: fullOrg } = idGet({
    store,
    value: computed(() => props.org)
  })

  const { canEdit } = clientCanU({
    subject: fullOrg,
    or: true,
    caps: computed(() => [[`orgs:${fullOrg.value?._id}`, ['orgAdmin']], [`orgs:${fullOrg.value?._id}`, ['WRITE']], ['orgs', ['WRITE']]]),
    cap_subjects: computed(() => fullOrg.value._id),
    login
  })

  const editing = ref('');

  const { h$:a$ } = HFind({
    store: accountStore,
    limit: computed(() => Object.keys(fullOrg.value?.accounts || {}).length),
    params: computed(() => {
      return {
        query: {
          owner: fullOrg.value._id,
          ...props.query
        }
      }
    })
  })

  const removeAccount = (id) => {
    accountStore.remove(id)
  }

  const toggleDialog = (val) => {
    if(!val) editing.value = undefined;
  }

  const setEditing = (val) => {
    if(!val || editing.value?._id === val._id) {
      editing.value = undefined
      emit('add-edit', val)
    } else {
      if(!props.noEdit){
        editing.value = val;
      }
      const accounts = fullOrg.value?.accounts || {};
      let newDefault = val._id;
      for(const k in accounts){
        if(accounts[k].default){
          newDefault = false;
          break;
        }
      }
      if(newDefault){
        store.patch(fullOrg.value._id, { $set: { [`accounts.${val._id}.default`]: true }})
      }
    }
    emit('update:model-value', val)
  }

</script>

<style lang="scss" scoped>

</style>
