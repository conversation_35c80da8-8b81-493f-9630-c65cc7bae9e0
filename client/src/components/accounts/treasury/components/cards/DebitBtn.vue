<template>
  <q-btn v-bind="{class: '_bub', ...$attrs}" @click="dialog = !dialog">
    <slot name="default"></slot>
    <common-dialog v-model="dialog" setting="smmd">
      <div class="_fw bg-white q-pa-md">
        <div class="row items-center">
          <q-chip :text-color="tab === 'org' ? 'p6' : ''" size="sm" color="transparent" clickable @click="tab = 'org'"
                  label="Company"></q-chip>
          <div>|</div>
          <q-chip :text-color="tab === 'amount' ? 'p6' : ''" size="sm" color="transparent" clickable
                  @click="tab = 'amount'" label="Details"></q-chip>
          <template v-if="tab === 'enter'">
            <div>|</div>
            <q-chip size="sm" text-color="p6" color="transparent" label="Account Number"></q-chip>
          </template>
          <template v-if="tab === 'add'">
            <div>|</div>
            <q-chip size="sm" text-color="p6" color="transparent" label="Add Company"></q-chip>
          </template>
        </div>
        <q-separator class="q-my-sm"></q-separator>
        <q-tab-panels v-model="tab" class="_panel" animated>
          <q-tab-panel class="_panel" name="org">
            <div class="__title">Who are you sending funds to?</div>
            <q-input v-model="search.text" placeholder="Search Companies" dense filled>
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
              <template v-slot:after>
                <q-btn @click="tab = 'add'" dense flat color="primary" icon="mdi-plus"></q-btn>
              </template>
            </q-input>
            <q-list separator>
              <default-item v-for="(org, i) in o$.data" :key="`org-${i}`" :model-value="org"
                            :use-atc-store="useAtcStore"
                            @click="setOrg(org)"></default-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel name="add" class="_panel">
            <quick-add
                :input-attrs="{ dense: true, filled: true }"
                @update:model-value="setOrg"
                :store="orgStore"
            >
              <template v-slot:fields="scope">
                <div class="_form_label">Address</div>
                <div class="q-pa-sm">
                  <tomtom-autocomplete
                      dense
                      filled
                      :model-value="scope.form.address"
                      @update:model-value="scope.setForm($event, 'address')"
                      :use-tomtom-geocode="useTomtomGeocode"
                  ></tomtom-autocomplete>
                </div>
              </template>
            </quick-add>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="amount">
            <div class="__title">Amount to add</div>
            <div class="__g">
              <div class="__l">To</div>
              <div class="q-px-sm">
                <default-chip :model-value="selectedOrg" :use-atc-store="useAtcStore"></default-chip>
              </div>
              <div class="__l">Account</div>
              <div class="q-px-xs font-7-8r">
                <q-chip v-if="!selectedCa.total" clickable @click="tab = 'enter'">
                  <span class="q-mr-sm">{{last4 ? `Account ending ***${last4}` : 'Add Account Info'}}</span>
                  <q-icon v-if="!last4" name="mdi-plus" color="green"></q-icon>
                </q-chip>
                <div v-else class="q-pa-sm font-7-8r">{{selectedOrg?.name}} has a <span class="tw-six text-primary">CareAccount</span> to send to ✅</div>
              </div>
              <div class="__l">Amount</div>
              <div>
                <money-input :decimal="2" prefix="$" :model-value="(form.amount || 0) / 100" @update:model-value="form.amount = $event * 100"></money-input>
              </div>
              <div class="__l">Memo</div>
              <div class="q-px-sm">
                <q-input autogrow v-model="form.description"></q-input>
              </div>
            </div>
            <div class="q-pa-md row justify-end">
              <q-btn push class="_p_btn" no-caps icon-right="mdi-check-circle" label="Confirm"
                     @click="tab = 'confirm'"></q-btn>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="enter">
            <one-time-account
                :amount="form.amount"
                :care_account="care_account"
                :org="selectedOrg"
                @update:model-value="setPm"></one-time-account>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="confirm">
            <div class="_form_grid">
              <div class="_form_label">Sending</div>
              <div class="q-px-sm">{{dollarString((form.amount || 0) / 100, '$', 2)}}</div>
              <div class="_form_label">From</div>
              <div class="q-px-sm">
                <org-chip :model-value="org"></org-chip>
              </div>
              <div class="_form_label">To</div>
              <div class="q-px-sm">
                <org-chip :model-value="selectedOrg"></org-chip>
              </div>
            </div>
            <div class="row justify-end">
              <q-btn rounded class="_a_btn tw-six" no-caps label="Send" icon-right="mdi-send" @click="sendTransfer"></q-btn>
            </div>
          </q-tab-panel>
        </q-tab-panels>

      </div>
    </common-dialog>
  </q-btn>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import QuickAdd from 'components/orgs/forms/QuickAdd.vue';
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import OneTimeAccount from 'components/accounts/forms/OneTimeAccount.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';

  import {computed, ref} from 'vue';
  import {useBanking} from 'stores/banking';
  import {useAtcStore} from 'stores/atc-store';
  import {useTomtomGeocode} from 'stores/tomtom-geocode';
  import {dollarString} from 'src/utils/global-methods';
  import {HFind} from 'src/utils/hFind';
  import {useOrgs} from 'stores/orgs';
  import {HQuery} from 'src/utils/hQuery';
  // import {SessionStorage} from 'symbol-auth-client';
  import {useCareAccounts} from 'stores/care-accounts';

  const orgStore = useOrgs();
  const bankStore = useBanking();
  const caStore = useCareAccounts();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    moov_account: { required: true },
    care_account: { required: true },
    org: { required: true }
  })

  const formFn = (defs) => {
    return { amount: 0, currency: 'usd', description: '', ...defs }
  }
  const form = ref(formFn());

  const tab = ref('org');
  const dialog = ref(false);
  const selectedOrg = ref();
  const selectedCa = ref({ data: [] })
  const setOrg = async (val) => {
    selectedOrg.value = val;
    tab.value = 'amount';
    form.value.description = `Transfer from ${props.org.name} to ${selectedOrg.value.name}`
    selectedCa.value = await caStore.find({ query: { $limit: val.careAccounts?.length || 1, _id: { $in: val.careAccounts || [] }}})
        .catch(err => {
          console.error(`Could not load care accounts: ${err.message}`)
          return { data: [] }
        })
  }

  const knownOrgs = computed(() => {
    const { owners = [], controls = {}, customers = [], asg = {} } = props.org || {};
    const arr = [...customers];
    for (let i = 0; i < owners.length; i++) {
      if (owners[i].idService === 'orgs') arr.push(owners[i].id)
    }
    for (const ctrl in controls) {
      for (const org in controls[ctrl].orgs || {}) {
        arr.push(org)
      }
    }
    for (const ctrl in asg) {
      for (const org in asg[ctrl].orgs || {}) {
        arr.push(org)
      }
    }
    return arr;
  })

  const { search, searchQ } = HQuery({})
  const { h$: o$ } = HFind({
    store: orgStore,
    limit: computed(() => knownOrgs.value.length),
    params: computed(() => {
      return {
        query: {
          _owners: { id: props.org._id },
          $or: [
            { _id: { $in: knownOrgs.value } },
          ],
          // ['owners.id']: props.org._id,
          ...searchQ.value
        }
      }
    })
  })

  const setPm = (val) => {
    form.value.destination_payment_method_data = val;
    tab.value = 'amount'
  }
  const last4 = computed(() => {
    const num = form.value.destination_payment_method_data?.us_bank_account?.account_number || ''
    if(!num) return '';
    else return num.slice(num.length - 4)
  })
  const loading = ref(false);
  const sendTransfer = async () => {
    console.log('write moov send transfer fn')
    // loading.value = true;
    // if(selectedCa.value.total) form.value.destination_payment_method_data = { type: 'financial_account', financial_account: selectedCa.value.data[0].stripe_id }
    // const sent = await bankStore.get(props.moov_account.id, {
    //   banking: {
    //     stripe: {
    //       method: 'outbound_payment',
    //       args: [formFn({
    //         ...form.value,
    //         end_user_details: {
    //           present: true,
    //           ip_address: SessionStorage.getItem('sessionPrint').ipInfo.ip
    //         },
    //         financial_account: props.care_account.stripe_id
    //       })]
    //     }
    //   }
    // })
    //     .catch(err => {
    //       $errNotify('Error adding funds - try again')
    //       console.error(`Error adding funds: ${err.message}`)
    //     })
    // if (sent.id) {
    //   emit('update:model-value', sent);
    //   form.value = formFn();
    //   dialog.value = false;
    // }
  }

</script>

<style lang="scss" scoped>
  .__title {
    padding: 10px;
    font-weight: bold;
    color: #999;
    font-size: .9rem;
  }

  .__g {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    justify-content: center;

    div {
      padding: 5px 10px;
    }
  }

  .__l {
    font-weight: 600;
    font-size: .7rem;
    color: #999;
    border-right: solid .5px #999;
  }
</style>
