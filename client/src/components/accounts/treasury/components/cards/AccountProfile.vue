<template>
  <div>

  </div>
</template>

<script setup>

  import {HFind} from 'src/utils/hFind';
  import {usePpls} from 'stores/ppls';
  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';

  const pplStore = usePpls();
  const orgStore = useOrgs();
  const props = defineProps({
    account: { required: true },
    org: { required: true }
  })

  const { item:fullOrg } = idGet({
    store: orgStore,
    value: computed(() => props.org)
  })

  const { h$:o$ } = HFind({
    store: pplStore,
    limit: computed(() => fullOrg.value?.owners?.length),
    params: computed(() => {
      return {
        _id: { $in: fullOrg.value?.owners }
      }
    })
  })

      // ["business_profile.url","company.owners_provided","company.phone","company.tax_id","external_account","owners.address.city","owners.address.line1","owners.address.postal_code","owners.address.state","owners.dob.day","owners.dob.month","owners.dob.year","owners.email","owners.first_name","owners.last_name","owners.ssn_last_4","representative.address.city","representative.address.line1","representative.address.postal_code","representative.address.state","representative.dob.day","representative.dob.month","representative.dob.year","representative.email","representative.first_name","representative.last_name","representative.phone","representative.relationship.executive","representative.relationship.title","representative.ssn_last_4","settings.card_issuing.tos_acceptance.date","settings.card_issuing.tos_acceptance.ip","tos_acceptance.date","tos_acceptance.ip"]
      // ["business_profile.annual_revenue.amount","business_profile.annual_revenue.currency","business_profile.annual_revenue.fiscal_year_end","business_profile.estimated_worker_count","business_profile.mcc","business_profile.url","company.owners_provided","company.phone","company.tax_id","external_account","owners.address.city","owners.address.line1","owners.address.postal_code","owners.address.state","owners.dob.day","owners.dob.month","owners.dob.year","owners.email","owners.first_name","owners.last_name","owners.ssn_last_4","representative.address.city","representative.address.line1","representative.address.postal_code","representative.address.state","representative.dob.day","representative.dob.month","representative.dob.year","representative.email","representative.first_name","representative.last_name","representative.phone","representative.relationship.executive","representative.relationship.title","representative.ssn_last_4","settings.card_issuing.tos_acceptance.date","settings.card_issuing.tos_acceptance.ip","tos_acceptance.date","tos_acceptance.ip"]
</script>

<style lang="scss" scoped>

</style>
