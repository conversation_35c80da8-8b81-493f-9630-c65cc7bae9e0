<template>
  <div class="_fw">

    <template v-if="isAllowed">
      <div class="q-pa-sm tw-six font-1r">{{expense?.name}}</div>
      <div class="row justify-center __bg pw2 pd4">
        <expense-cards
            :budget="budget"
            :model-value="expense._fastjoin?.limit_cards"
            :org="org"
            v-model:active="active"
        ></expense-cards>
      </div>

      <div class="_fw pw2 pd4">
        <expense-transactions :model-value="expense" :org="org" :card="(expense._fastjoin?.limit_cards || [])[active]"></expense-transactions>
      </div>

    </template>
    <template v-else>
      <q-spinner v-if="!org?._id" size="40px" class="q-ma-xl" color="primary"></q-spinner>
      <div v-else class="q-pa-lg text-italic font-1r">You don't have permission to view this card</div>
    </template>
  </div>
</template>

<script setup>
  import ExpenseCards from './ExpenseCards.vue';
  import ExpenseTransactions from '../lists/ExpenseTransactions.vue';

  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useRoute} from 'vue-router';
  import {useBudgets} from 'stores/budgets';
  import {useOrgs} from 'stores/orgs';
  import {loginPerson} from 'stores/utils/login';

  const { person, login } = loginPerson()

  const budgetStore = useBudgets();
  const expenseStore = useExpenses();
  const orgStore = useOrgs();
  const route = useRoute();

  import {useExpenses} from 'stores/expenses';

  const props = defineProps({
    expenseId: { required: false }
  })

  const { item:expense } = idGet({
    store: expenseStore,
    value: computed(() => props.expenseId || route.params.expenseId),
    params: ref({ runJoin: { limit_cards: true } }),
    refreshOn: (val) => !val?._fastjoin?.limit_cards
  })
  const { item: budget } = idGet({
    store: budgetStore,
    value: computed(() => card.value?._fastjoin?.budget || card.value?.budget),
    params: ref({ runJoin: { budget_owner: true } })
  })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => budget.value?._fastjoin?.owner || budget.value?.owner)
  })

  const active = ref(-1)

  const { canEdit } = clientCanU({
    subject: org,
    or: true,
    caps: computed(() => [[`orgs:${org.value?._id}`, ['orgAdmin']], [`orgs:${org.value?._id}`, ['WRITE']], ['orgs', 'WRITE']]),
    cap_subjects: computed(() => [org.value._id]),
    login
  })

  const isAllowed = computed(() => {
    if (canEdit.value?.ok) return true;
    const id = person.value?._id;
    if (!id || !card.value || !budget.value || !person.value) return false;
    let allList = [];
    const keys = ['members', 'managers', 'approvers']
    for (let i = 0; i < 3; i++) {
      const cardList = card.value[keys[i]] || [];
      for (let idx = 0; idx < cardList.length; idx++) {
        allList.push(cardList[idx])
      }
      const budgetList = budget.value[keys[i]] || [];
      for (let idx = 0; idx < budgetList.length; idx++) {
        allList.push(budgetList[idx])
      }
    }
    return allList.includes(person.value?._id);
  })


</script>

<style lang="scss" scoped>
  .__bg {
    background: #dedede;
  }
</style>
