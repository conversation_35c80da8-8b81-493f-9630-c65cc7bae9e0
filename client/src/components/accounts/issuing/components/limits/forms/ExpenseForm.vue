<template>
  <div class="_fw">

    <div class="_f_l _f_chip">{{ form?._id ? 'Edit Expense' : 'Add Expense' }}</div>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Expense Name</div>
      <div class="q-pa-sm">
        <q-input dense filled placeholder="Enter Name..." v-model="form.name"
                @update:model-value="autoSave('name')"></q-input>
      </div>
      <div class="_form_label">Budget</div>
      <div class="q-pa-md">
        <budget-picker
            disabled
            dense
            filled
            v-model="form.budget"
            @update:model-value="autoSave('budget')"
            emit-value
        ></budget-picker>
      </div>
      <div class="_form_label">Expense Owner</div>
      <div class="q-pa-sm mw400">
        <limit-owner-form v-model="form.limit_owner" :budget="budget" @update:modelValue="autoSave('limit_owner')"></limit-owner-form>
      </div>

    </div>

    <q-slide-transition>

      <div class="_fw" v-if="form?._id">

        <div class="_form_grid _f_g_r">
          <div class="_form_label">Status</div>
          <div class="q-pa-sm">
            <status-chip
                :picker="form.status !== 'canceled'"
                :model-value="form.status"
                @update:model-value="setStatus"
            ></status-chip>
          </div>
        </div>

        <div class="_f_l _f_chip">Funds</div>
        <div class="q-py-sm">
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Available Funds</div>
            <div class="q-pa-sm">
              <amount-form
                  path="amount"
                  :input-attrs="{ filled: true }"
                  :id="form._id"
                  :store="expenseStore"
                  :model-value="form.amount"
                  :parent-amount="parent_balances.amount"
                  :parent-assigned="parent_balances.assigned_amount"
                  :spent="spent"
              ></amount-form>
            </div>
            <div class="_form_label">Recurring Funds (monthly)</div>
            <div class="q-pa-sm">
              <amount-form
                  path="recurs"
                  :input-attrs="{ filled: true }"
                  :id="form._id"
                  :store="expenseStore"
                  :model-value="form.recurs"
                  :parent-amount="parent_balances.recurs"
                  :parent-assigned="parent_balances.assigned_recurs"
              ></amount-form>
            </div>
          </div>
        </div>
        <div class="_f_l _f_chip">Members</div>
        <div class="q-py-sm">
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Managers</div>
            <div class="q-pa-sm">
              <div class="__cap">Managers manage all spend details</div>

              <budget-members
                  :store="expenseStore"
                  adding
                  path="managers"
                  :model-value="form"
              ></budget-members>
            </div>
            <div class="_form_label">Approvers</div>
            <div class="q-pa-sm">
              <div class="__cap">Who can approve spend that requires approval</div>
              <budget-members
                  :store="expenseStore"
                  adding
                  path="approvers"
                  :model-value="form"
              ></budget-members>
            </div>
            <div class="_form_label">Members</div>
            <div class="q-pa-sm">
              <div class="__cap">Members can use available budget funds</div>
              <budget-members
                  :store="expenseStore"
                  adding
                  path="members"
                  :model-value="form"
              ></budget-members>
            </div>
          </div>
        </div>
        <div class="_f_l _f_chip">Spend Categories</div>
        <div class="q-py-sm">
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Allowed Categories</div>
            <div class="q-pa-sm">
              <mcc-picker medical-option path="mcc_whitelist" :store="expenseStore" :id="form?._id"
                          @update:model-value="autoSave('mcc_whitelist')" v-model="form.mcc_whitelist"></mcc-picker>

            </div>

            <div class="_form_label">Disallowed Categories</div>
            <div class="q-pa-sm">
              <mcc-picker path="mcc_blacklist" allow="Restrict" :id="form?._id" :store="expenseStore"
                          v-model="form.mcc_blacklist"></mcc-picker>
            </div>
          </div>

        </div>
      </div>
    </q-slide-transition>

    <div class="q-pa-md row justify-end" v-if="form && form.name && form.limit_owner && !form._id">
      <q-btn color="accent" push no-caps class="tw-six" label="Create Expense" @click="save"></q-btn>
    </div>

    <common-dialog setting="sm" :model-value="!!cancelDialog" @update:model-value="val => val ? '' : cancelDialog = ''">
      <div class="q-pa-lg _fw">
        <div class="font-1r">Are you sure you want to cancel this expense?</div>
        <div class="q-pa-sm font-3-4r">If you only mean to "pause" or "freeze" the expense, choose "Inactive" instead
        </div>
        <div class="q-pt-md row justify-end">
          <q-btn flat no-caps @click="cancelDialog = ''">
            <q-icon color="primary" name="mdi-chevron-left"></q-icon>
            <span class="q-ml-sm">No, go back</span>
          </q-btn>
          <q-btn flat no-caps @click="setStatus(cancelDialog, true)">
            <q-icon color="red" name="mdi-close"></q-icon>
            <span class="q-ml-sm">Yes, Cancel</span>
          </q-btn>
        </div>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import AmountForm from 'components/accounts/issuing/components/budgets/forms/AmountForm.vue';
  import BudgetPicker from 'components/accounts/issuing/components/budgets/lists/BudgetPicker.vue';
  import LimitOwnerForm from 'components/accounts/issuing/components/limits/forms/LimitOwnerForm.vue';
  import MccPicker from 'components/accounts/issuing/components/budgets/forms/MccPicker.vue';
  import BudgetMembers from 'components/accounts/issuing/components/budgets/forms/BudgetMembers.vue';
  import StatusChip from '../cards/StatusChip.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {HForm, HSave} from 'src/utils/hForm';

  import {budgetTree} from 'components/accounts/issuing/components/budgets/utils/budget-tree';
  import {useBanking} from 'stores/banking';
  import {useExpenses} from 'stores/expenses';

  const expenseStore = useExpenses();
  const bankStore = useBanking();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    budget: { required: true },
    modelValue: { required: true },
    careAccount: { required: true }
  })

  const { item: expense } = idGet({
    store: expenseStore,
    value: computed(() => props.modelValue),
  })
  const { budget } = budgetTree(computed(() => props.budget))


  const spent = computed(() => (expense.value?.spent || 0) + (expense.value?.spent_pending || 0))
  const parent_balances = computed(() => {
    if (budget.value) {
      const { amount = 0, recurs = 0, assigned_amount = 0, assigned_recurs = 0 } = budget.value;
      return { amount, recurs, assigned_amount, assigned_recurs }
    } else return { amount: 0, recurs: 0, assigned_amount: 0, assigned_recurs: 0 }
  })

  const formFn = (defs) => {
    const obj = {};
    if(props.budget) obj.budget = props.budget._id || props.budget;
    return { ...obj, ...defs };
  }

  const { form, save } = HForm({
    store: expenseStore,
    value: expense,
    formFn,
    beforeFn: (val) => {
      if (!val.owner) val.owner = budget.value?.owner
      if (!val.budget) val.budget = budget.value?._id
      if (!val.connect_id) val.connect_id = budget.value?.connect_id
      return val;
    },
    afterFn: (val) => emit('update:model-value', val)
  })

  const { autoSave } = HSave({ form, store: expenseStore, pause: computed(() => !form.value?._id) })

  const cancelDialog = ref('');
  const setStatus = async (val, go) => {
    const oldStatus = form.value.status;
    if (oldStatus !== val) {
      if (!go && val === 'canceled') cancelDialog.value = val;
      else {
        cancelDialog.value = '';
        expenseStore.patchInStore(expense.value._id, { status: val });
        const map = { 'active': 'unsuspend', 'inactive': 'suspend', 'canceled': 'terminate' }
        const updated = await bankStore.get(expense.value.owner, {
          banking: {
            ramp: {
              method: 'limits',
              args: [map[val], expense.value.ramp_limit]
            }
          }
        })
            .catch(err => {
              console.error(`Error setting card status: ${err.message}`);
              return undefined
            })
        if (!updated) expenseStore.patchInStore(expenseStore.value._id, { status: oldStatus })
      }
    }

  }

  watch(budget, (nv) => {
    if (nv && !form.value?.budget) form.value.budget = nv._id
  }, { immediate: true })


</script>

<style lang="scss" scoped>

</style>
