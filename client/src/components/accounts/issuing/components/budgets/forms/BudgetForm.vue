<template>
  <div class="_fw">
    <div class="_f_g">
      <div class="_f_l _f_chip">
        Budget Info
      </div>
      <div class="q-py-sm">
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Budget Name</div>
          <div class="q-pa-sm">
            <q-input placeholder="Enter Name..." v-model="form.name" @update:model-value="autoSave('name')"></q-input>
          </div>
          <template v-if="!parentBudget._id">
            <div class="_form_label">Plan Wallet</div>
            <div class="q-pa-sm">
              <care-account-picker
                  :org="fullOrg"
                  emit-value
                  :model-value="form.careAccount"
                  @update:model-value="setCareAccount"
              ></care-account-picker>
            </div>
          </template>
          <template v-if="!form?.children?.length">
            <div class="_form_label">Parent</div>
            <div class="q-pa-sm">
              <budget-picker
                  :query="form?._id ? {careAccount: caId, _id: { $ne: form._id }} : { careAccount: caId }" dense filled
                  emit-value
                  :model-value="form.parent"
                  @update:model-value="setParent"
              ></budget-picker>
            </div>
          </template>
        </div>
      </div>
    </div>
    <template v-if="!form._id">
      <div class="row justify-end q-pa-lg">
        <q-btn @click="save" class="_p_btn tw-six" no-caps label="Create Budget"
               icon-right="mdi-piggy-bank"></q-btn>
      </div>
    </template>
    <template v-else>
      <div class="_f_l _f_chip">Funds</div>
      <div class="q-py-sm">
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Available Funds</div>
          <div class="q-pa-sm">
            <amount-form
                path="amount"
                :model-value="form?.amount"
                :id="form?._id"
                :store="budgetStore"
                :parent-assigned="parent_balances.assigned_amount"
                :parent-amount="parent_balances.amount"
                :spent="spent"
            ></amount-form>
          </div>
          <div class="_form_label">Recurring Funds (monthly)</div>
          <div class="q-pa-sm">
            <amount-form
                path="recurs"
                :model-value="form?.recurs"
                :id="form?._id"
                :store="budgetStore"
                :parent-assigned="parent_balances.assigned_recurs"
                :parent-amount="parent_balances.amount"
            ></amount-form>
          </div>

        </div>
      </div>
      <template v-if="form._id">
        <div class="_f_l _f_chip">Members</div>
        <div class="q-py-sm">
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Managers</div>
            <div class="q-pa-sm">
              <div class="__cap">Managers manage all budget details</div>

              <budget-members
                  :store="budgetStore"
                  adding
                  path="managers"
                  :model-value="form"
              ></budget-members>
            </div>
            <div class="_form_label">Approvers</div>
            <div class="q-pa-sm">
              <div class="__cap">Approvers approve budget spend that requires approval</div>
              <budget-members
                  :store="budgetStore"
                  adding
                  path="approvers"
                  :model-value="form"
              ></budget-members>
            </div>
            <div class="_form_label">Members</div>
            <div class="q-pa-sm">
              <div class="__cap">Members can use available budget funds</div>
              <budget-members
                  :store="budgetStore"
                  adding
                  path="members"
                  :model-value="form"
              ></budget-members>
            </div>
          </div>
        </div>
        <div class="_f_l _f_chip">Spend Categories</div>
        <div class="q-py-sm">
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Allowed Categories</div>
            <div class="q-pa-sm">
              <mcc-picker medical-option path="mcc_whitelist" :id="form._id" :store="budgetStore"
                          v-model="form.mcc_whitelist"></mcc-picker>

            </div>

            <div class="_form_label">Disallowed Categories</div>
            <div class="q-pa-sm">
              <mcc-picker allow="Restrict" path="mcc_blacklist" :id="form._id" :store="budgetStore"
                          v-model="form.mcc_blacklist"></mcc-picker>
            </div>
          </div>
        </div>
      </template>
    </template>

  </div>
</template>

<script setup>
  import BudgetPicker from 'components/accounts/issuing/components/budgets/lists/BudgetPicker.vue';
  import CareAccountPicker from 'components/care-accounts/lists/CareAccountPicker.vue';
  import MccPicker from 'components/accounts/issuing/components/budgets/forms/MccPicker.vue';
  import BudgetMembers from 'components/accounts/issuing/components/budgets/forms/BudgetMembers.vue';
  import AmountForm from 'components/accounts/issuing/components/budgets/forms/AmountForm.vue';

  import {useBudgets} from 'stores/budgets';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useCareAccounts} from 'stores/care-accounts';
  import {walletBalance} from 'src/components/accounts/treasury/utils';

  const budgetStore = useBudgets()
  const caStore = useCareAccounts();

  const props = defineProps({
    modelValue: { required: false },
    org: { required: true },
    parent: { required: false },
    careAccountId: { required: true }
  })

  const { item: budget } = idGet({
    store: budgetStore,
    value: computed(() => props.modelValue)
  })
  const fullOrg = computed(() => props.org)

  const spent = computed(() => {
    if (!budget.value) return 0;
    return (budget.value.spent || 0) + (budget.value.spent_pending || 0) + (budget.value.spent_pending_sub || 0) + (budget.value.spent_sub || 0)
  })
  const { form, save } = HForm({
    store: budgetStore,
    formFn: (defs) => {
      const obj = {};
      if (props.parent) obj.parent = props.parent._id || props.parent;
      return { ...obj, ...defs }
    },
    value: computed(() => budget.value),
    validate: true,
    beforeFn: (val) => {
      if (!val.owner) val.owner = fullOrg.value._id;
      if (!val.moov_id) val.moov_id = parentBudget.value?.moov_id || ca.value.moov_id;
      if (props.parent && !val.parent) val.parent = props.parent._id || props.parent
      return val;
    },
    vFn: (frm) => {
      const obj = {
        'name': { name: 'Name', v: ['notEmpty'] }
      }
      if (!frm.parent && !frm.careAccount) obj['parent'] = { name: 'Parent or Plan Wallet', v: ['notEmpty'] }
      return obj;
    }
  })
  const { autoSave, setForm } = HSave({ form, store: budgetStore, pause: computed(() => !form.value?._id) })

  const caId = computed(() => budget.value.careAccount || props.careAccountId)

  const { item:parentBudget } = idGet({
    store: budgetStore,
    value: computed(() => form.value.parent)
  })
  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => form.value.careAccount),
    params: ref({ runJoin: { with_wallet: true } }),
    refreshFn: (val) => !val?._fastjoin?.wallet
  })
  const wallet = computed(() => ca.value?._fastjoin?.wallet)

  const parent_balances = computed(() => {
    if (parentBudget.value) {
      const { amount = 0, recurs = 0, assigned_amount = 0, assigned_recurs = 0 } = parentBudget.value;
      return { amount, recurs, assigned_amount, assigned_recurs }
    } else if (ca.value) {
      const { amount = 0, recurs = 0, assigned_amount = 0, assigned_recurs = 0 } = ca.value;
      const bal = walletBalance(wallet.value)
      return { amount: bal || amount || 0, recurs, assigned_amount, assigned_recurs }
    } else return { amount: 0, recurs: 0, assigned_amount: 0, assigned_recurs: 0 }
  })

  const caIds = computed(() => fullOrg.value?.careAccounts || []);
  watch(caIds, (nv) => {
    if (parentBudget.value._id) form.value.parent = parentBudget.value._id;
    else if (nv?.length === 1 && !form.value.careAccount) form.value.careAccount = nv[0];
  }, { immediate: true })


  const setParent = (val) => {
    if (val) {
      setForm('parent', val);
      if (form.value.careAccount) setForm('$unset', { careAccount: '' })
    }
  }
  const setCareAccount = (val) => {
    if (val) {
      setForm('careAccount', val);
      if (form.value.parent) setForm('$unset', { parent: '' })
    }
  }
</script>

<style lang="scss" scoped>
  .__cap {
    font-style: italic;
    font-size: .8rem;
    color: #999;
  }
</style>
