<template>
  <q-chip v-bind="{ clickable: true, color: 'ir-grey-2', ...$attrs}">
    <slot name="prepend">
      <q-icon color="ir-mid" name="mdi-piggy-bank"></q-icon>
    </slot>
    <slot name="default">
      <span class="q-ml-sm">{{multiple ? 'Select Budget' : budget?.name || picker ? 'Select Budget' : 'No Budget'}}</span>
    </slot>
    <slot name="append"></slot>

    <q-popup-proxy v-if="picker" v-model="open">
      <div class="w300 mw100 q-pa-sm">
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator dense>
          <q-item v-for="(b, i) in b$.data" :key="`b-${i}`" clickable @click="emitUp(b)">
            <slot name="avatar">
              <q-item-section avatar v-bind="b">
                <q-icon color="p2" name="mdi-piggy-bank"></q-icon>
              </q-item-section>
            </slot>
            <q-item-section>
              <q-item-label>{{b.name}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
  <template v-if="multiple">
    <q-chip v-for="(b, i) in h$.data" :key="`op-${i}`" v-bind="{ color: 'transparent', ...exAttrs }">
      <slot name="exPrepend" v-bind="b">
        <q-icon color="ir-mid" name="mdi-piggy-bank"></q-icon>
      </slot>
      <span class="q-ml-sm">{{b.name}}</span>
      <q-btn v-if="removable" size="xs" dense flat icon="mdi-close" color="red" @click="emitUp(b)"></q-btn>
    </q-chip>
  </template>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useBudgets} from 'stores/budgets';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';

  const budgetStore = useBudgets();
  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    exAttrs: Object,
    modelValue: { required: true },
    limit: { default: 5 },
    picker: Boolean,
    emitValue: Boolean,
    multiple: Boolean,
    removable: Boolean
  })

  const open = ref(false)
  const { item:budget } = idGet({
    store: budgetStore,
    value: computed(() => props.modelValue)
  })

  const { search, searchQ } = HQuery({})
  const { h$:b$ } = HFind({
    store: budgetStore,
    pause: computed(() => !props.picker),
    limit: computed(() => props.limit),
    params: computed(() => {
     return {
       ...props.params,
       query: {
         ...props.query,
         ...searchQ.value,
         ...props.params?.query
       },
     }
    })
  })

  const { h$ } = HFind({
    store: budgetStore,
    pause: computed(() => !props.multiple),
    limit: computed(() => props.limit),
    params: computed(() => {
      return {
        query: {
          _id: { $in: props.multiple ? (props.modelValue || []).map(a => props.emitValue ? a : a._id) : [] }
        }
      }
    })
  })

  const emitUp = (val) => {
    if(props.multiple){
      if(props.emitValue){
        const idx = (props.modelValue || []).indexOf(val._id)
        if(idx > -1){
          const arr = [...props.modelValue]
          arr.splice(idx, 1);
          emit('update:model-value', arr)
        } else emit('update:model-value', [...props.modelValue || [], val._id])
      } else {
        const idx = (props.modelValue || []).map(a => a._id).indexOf(val._id)
        if(idx > -1){
          const arr = [...props.modelValue]
          arr.splice(idx, 1);
          emit('update:model-value', arr)
        } else emit('update:model-value', [...props.modelValue || [], val])
      }
    } else {
      if(props.emitValue) emit('update:model-value', val._id);
      else emit('update:model-value', val)
    }
    open.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
