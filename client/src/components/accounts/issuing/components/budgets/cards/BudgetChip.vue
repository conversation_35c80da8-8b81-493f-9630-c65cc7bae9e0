<template>
  <q-chip v-bind="{ clickable: true, color: 'ir-grey-2', ...$attrs}">
    <slot name="prepend">
      <q-icon class="q-mr-sm" color="ir-mid" name="mdi-piggy-bank"></q-icon>
    </slot>
    <slot name="default">
      <span>{{  modelValue ? budget.name : picker ? 'Select Budget' : emptyLabel }}</span>
    </slot>
    <slot name="append">
      <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
    </slot>

    <q-popup-proxy v-if="picker" v-model="open">
      <div class="w300 mw100 q-pa-sm">
        <slot name="top" :budget="budget"></slot>
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator dense>
          <q-item v-if="!opts.length">
            <q-item-label header>No budgets found...</q-item-label>
          </q-item>
          <q-item v-for="(b, i) in opts" :key="`b-${i}`" clickable @click="emitUp(b)">
            <slot name="avatar">
              <q-item-section avatar v-bind="b">
                <q-icon color="p2" name="mdi-piggy-bank"></q-icon>
              </q-item-section>
            </slot>
            <q-item-section>
              <q-item-label>{{ b.name }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useBudgets} from 'stores/budgets';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {fakeId} from 'src/utils/global-methods';

  const budgetStore = useBudgets();
  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    emptyLabel: { default: 'No Budget' },
    exAttrs: Object,
    modelValue: { required: true },
    limit: { default: 5 },
    picker: Boolean,
    emitValue: Boolean,
    removable: Boolean,
    careAccount: { type: Object },
    query: Object,
    options: Array
  })

  const open = ref(false)
  const { item: budget } = idGet({
    store: budgetStore,
    value: computed(() => props.modelValue)
  })

  const { search, searchQ } = HQuery({})
  const { h$: b$ } = HFind({
    store: budgetStore,
    pause: computed(() => props.options || !props.picker),
    limit: computed(() => props.limit),
    params: computed(() => {
      const query = {
        ...props.query,
        ...searchQ.value,
        ...props.params?.query
      }
      if(props.careAccount) query.careAccount = props.careAccount
      else if(!props.query) query.careAccount = fakeId
      return {
        ...props.params,
        query
      }
    })
  })

  const opts = computed(() => props.options || b$.data)

  const emitUp = (val) => {
    if (props.emitValue) emit('update:model-value', val._id);
    else emit('update:model-value', val)
    open.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
