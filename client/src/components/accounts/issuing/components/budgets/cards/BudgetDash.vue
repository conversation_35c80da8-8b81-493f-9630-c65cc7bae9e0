<template>
  <div class="__bp">

    <div class="row q-col-gutter-sm">
      <div class="col-12 col-md-6  _fh">
        <div class="__c">
          <div class="q-pb-md tw-six font-1r text-grey-7">Funding Source</div>
          <q-tree node-key="_id" default-expand-all v-bind="{ nodes, expanded }"></q-tree>

          <div class="__mcc">
            <div class="tw-six font-1r text-grey-7">Allowable Merchant Categories</div>
            <div v-if="!budget?.mcc_whitelist?.length" class="q-pa-sm text-italic">None specified - all allowed unless
              restricted
            </div>
            <mcc-list :model-value="budget?.mcc_whitelist"></mcc-list>

          </div>
          <q-separator class="q-my-sm"></q-separator>
          <div class="__mcc __s">
            <div class="tw-six font-1r text-grey-7">Restricted Merchant Categories</div>
            <div v-if="!budget?.mcc_blacklist?.length" class="q-pa-sm text-italic">None specified</div>
            <mcc-list :model-value="budget?.mcc_blacklist"></mcc-list>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-6 _fh">

        <div class="__c">
          <div class="tw-six q-pb-md font-1r text-grey-7">People</div>

          <q-badge text-color="grey-7" color="transparent" class="tw-six text-uppercase">Managers ({{ budget?.managers?.length || 0 }})</q-badge>
          <budget-members :store="store" path="managers" :model-value="budget" :size="35"></budget-members>

          <q-separator class="q-my-sm"></q-separator>
          <q-badge text-color="grey-7" color="transparent"  class="tw-six text-uppercase">Approvers ({{ budget?.approvers?.length || 0 }})</q-badge>
          <budget-members :store="store" path="approvers" :model-value="budget" :size="35"></budget-members>

          <q-separator class="q-my-sm"></q-separator>

          <q-badge text-color="grey-7" color="transparent" class="tw-six text-uppercase">Members ({{ budget?.members?.length || 0 }})</q-badge>
          <budget-members :store="store" path="members" :model-value="budget" :size="35"></budget-members>
        </div>
      </div>

    </div>


  </div>
</template>

<script setup>
  import MccList from 'components/accounts/issuing/components/budgets/lists/MccList.vue';
  import BudgetMembers from 'components/accounts/issuing/components/budgets/forms/BudgetMembers.vue';

  import icon from 'src/assets/common_cent_grey.svg'
  import {idGet} from 'src/utils/id-get';
  import {useBudgets} from 'stores/budgets';
  import {useCareAccounts} from 'stores/care-accounts';
  import {computed} from 'vue';


  const store = useBudgets();
  const caStore = useCareAccounts();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: budget } = idGet({
    store,
    value: computed(() => props.modelValue)
  })

  const { item: parent } = idGet({
    store,
    value: computed(() => budget.value?.parent)
  })

  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => budget.value?.careAccount || parent.value?.careAccount)
  })

  const expanded = computed(() => {
    if (parent.value?._id) return ['careAccount', 'parent', 'budget'];
    else return ['careAccount', 'budget']
  })

  const nodes = computed(() => {
    const finalChild = {
      _id: 'budget',
      label: budget.value?.name,
      icon: 'mdi-piggy-bank'
    }
    const arr = [
      {
        _id: 'careAccount',
        label: ca.value?.name || 'Account',
        avatar: icon
      }
    ]
    if (parent.value?._id) {
      arr[0].children = [
        { _id: 'parent', label: parent.value.name, icon: 'mdi-piggy-bank', children: [finalChild] }
      ]
    } else arr[0].children = [finalChild]
    return arr;
  })



</script>

<style lang="scss" scoped>
  .__bp {
    width: 100%;
    //padding: 10px min(20px, 2vw);
    background: #f9f9f9;
  }

  .__c {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px -7px #999;
    padding: 30px 20px;
    //height: 100%;
  }


  .__mcc {
    padding: 15px;
    //border: solid 2px #dedede;
    background: #f9f9f9;
    border-radius: 8px;
    margin: 15px 0;
  }

  .__s {
    border-color: var(--q-secondary);
    margin-bottom: 0;
  }
</style>
