<template>
  <div class="_fw pw2 pd2">
    <div class="__pd">
      <div class="row items-center">
        <q-chip dense clickable @click="go('')" color="transparent">
          <span>Budgets</span>
        </q-chip>
        <template v-if="parent._id">
          <q-icon name="mdi-chevron-right" size="16px"></q-icon>
          <q-chip dense clickable color="transparent" class="tw-six text-primary" @click="go(parent._id)">
            <span>{{ parent.name }}</span>
          </q-chip>
        </template>
        <q-icon name="mdi-chevron-right" size="16px"></q-icon>
        <q-chip dense clickable color="transparent" class="tw-six text-primary">
          <span>{{ budget?.name }}</span>
        </q-chip>
      </div>
      <q-separator class="q-my-sm"></q-separator>
      <div class="row justify-end">
        <q-btn class="q-ml-md" dense flat icon="mdi-pencil" @click="editing = true"></q-btn>
      </div>
      <div class="row items-start">
        <div class="col-12 col-md-5">
          <div class="row items-center">
            <div class="q-pa-md tw-six font-1-1-2r">{{ budget?.name }}</div>
          </div>
        </div>
        <div class="col-12 col-md-7">
          <budget-funds :model-value="budget"></budget-funds>
        </div>
      </div>
      <q-separator class="q-my-sm"></q-separator>

    </div>
    <div class="__tabs">
      <q-tabs inline-label no-caps v-model="tab" @update:model-value="setTab" align="left">
        <q-tab :icon="tabs[k].icon" v-for="(k, i) in Object.keys(tabs)" :key="`tab-${i}`" :name="k">
          <span class="tw-six q-ml-sm">{{ tabs[k].label }}</span>
        </q-tab>
      </q-tabs>
    </div>
    <q-tab-panels class="_panel mnh50" v-model="tab" animated>
      <q-tab-panel class="panel" v-for="(k, i) in Object.keys(tabs)" :key="`panel-${i}`" :name="k">
        <component :is="tabs[k].component" v-bind="tabs[k].attrs"></component>
      </q-tab-panel>
    </q-tab-panels>

    <common-dialog setting="right" v-model="editing">
      <div class="_fw bg-white q-pa-md">
        <budget-form :care-account-id="budget.careAccount" :model-value="budget" :org="budget?.owner"></budget-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import BudgetDash from 'components/accounts/issuing/components/budgets/cards/BudgetDash.vue';
  import SubBudgets from 'components/accounts/issuing/components/budgets/cards/SubBudgets.vue';
  import BudgetExpenses from 'components/accounts/issuing/components/limits/cards/BudgetExpenses.vue';
  import BudgetForm from 'components/accounts/issuing/components/budgets/forms/BudgetForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import BudgetFunds from 'components/accounts/issuing/components/budgets/cards/BudgetFunds.vue';

  import {useRoute, useRouter} from 'vue-router';
  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useBudgets} from 'stores/budgets';


  const budgetStore = useBudgets();
  const route = useRoute();
  const router = useRouter();

  const tab = ref('funds');
  const editing = ref(false);

  const budgetId = computed(() => route.params.budgetId)

  const budget = ref({});
  watch(budgetId, async (nv, ov) => {
    if(nv && nv !== ov){
      let b = budgetStore.getFromStore(nv);
      if(!b.value){
        const fb = await budgetStore.get(nv);
        b.value = fb;
      }
      if(b.value) budget.value = b.value;
    }
  }, { immediate: true })

  const { item: parent } = idGet({
    store: budgetStore,
    value: computed(() => budget.value?.parent),
    resetOnBlank: true
  })

  const go = (id) => {
    router.push({ name: id ? 'org-budget' : 'org-budgets', params: { budgetId: id || '' } })
  }

  const tabs = computed(() => {
    const obj = {
      'funds': {
        icon: 'mdi-cash',
        label: 'Funds',
        component: BudgetDash,
        attrs: {
          modelValue: budget.value
        }
      },
      'expenses': {
        icon: 'mdi-credit-card',
        label: 'Expenses',
        component: BudgetExpenses,
        attrs: {
          budget: budget.value
        }
      }
    }
    if (budget.value && !budget.value.parent) {
      obj['budgets'] = {
        icon: 'mdi-file-tree',
        label: 'Sub-Budgets',
        component: SubBudgets,
        attrs: {
          parent: budget.value
        }
      }
    }
    return obj
  })

  const setTab = (val) => {
    router.push({ ...route, params: { ...route.params, tab: val } })
  }
  onMounted(() => {
    setTimeout(() => {
      if (route.params.tab && tabs.value[route.params.tab]?.label) tab.value = route.params.tab
      else tab.value = 'funds';
    }, 100)
  })
</script>

<style lang="scss" scoped>
  .__pd {
    //padding: 2vh 3vw 0 3vw
  }

  .__tabs {
    color: #424242;
    border-radius: 6px;
    //padding: 0 3vw;
    //background: linear-gradient(180deg, var(--q-p9), var(--q-p7));
    background: linear-gradient(180deg, #fefefe, white);
  }
</style>
