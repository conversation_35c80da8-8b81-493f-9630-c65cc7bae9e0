<template>
  <div class="_fw">
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Account Nickname</div>
      <div class="q-pa-sm">
        <q-input v-model="form.nickname" @blur="patch" @update:model-value="autoSave('nickname')"
                 placeholder="Enter Account Nickname..."></q-input>
      </div>
    </div>
    <div class="_f_g">
      <div class="_f_l _f_chip">Business Details</div>

      <org-account-details :model-value="fullOrg"></org-account-details>

      <div class="_f_l _f_chip">Account Details</div>
      <div class="_form_grid _f_g_r">

        <!--      ROUTING-->
        <div class="_form_label">Routing Number</div>
        <div class="q-pa-sm">
          <routing-input @update:model-value="autoSave('routingNumber')" @bank-name="form.bankName = $event"
                         v-model="form.routingNumber"></routing-input>
        </div>
        <template v-if="form?.routingNumber && !form?.bankName">
          <!--        BANK NAME-->
          <div class="_form_label">Bank Name</div>
          <div class="q-pa-sm">
            <q-input v-model="form.bankName" @update:model-value="autoSave('bankName')"
                     placeholder="Enter Bank Name"></q-input>
          </div>
        </template>
        <div class="_form_label">Account Number</div>
        <div class="q-pa-sm">
          <account-double-input @update:model-value="autoSave('accountNumber')" :last4="account?.last4"
                                v-model="form.accountNumber"></account-double-input>
        </div>

      </div>

      <div class="_f_l _f_chip">Banking Features</div>
      <div class="_form_grid _f_g_r" v-if="account?._id">
        <template v-if="!account?.verification?.verified">
        <div class="_form_label">Verification</div>
        <div class="q-pa-sm">
          <account-status
              @update:start="emit('update:status')"
              @update:confirm="emit('update:status')"
              :model-value="account"></account-status>
        </div>
        </template>
        <div class="_form_label">Remove Account</div>
        <div class="q-pa-md" v-if="account?._id">
          <template v-if="!removing">
            <remove-proxy-btn two-step name="Account" @remove="remove"></remove-proxy-btn>
          </template>
          <q-slide-transition>
            <div class="_fw" v-if="removing">
              <div class="q-pa-sm br5 bg-ir-grey-2 font-1r tw-six text-a9">{{ phrase }}</div>
              <q-input label="Enter Phrase" v-model="phraseInput" input-class="tw-six font-1r"></q-input>
              <div class="row justify-end q-pt-sm">
                <q-btn no-caps class="q-mr-sm" flat @click="removing = false">
                  <span class="q-mr-xs">Cancel</span>
                  <q-icon name="mdi-close" color="red"></q-icon>
                </q-btn>
                <q-btn @click="remove(true)" v-if="phraseInput === phrase" glossy color="primary" no-caps>
                  <span class="q-mr-xs">Remove</span>
                  <q-icon name="mdi-check-circle"></q-icon>
                </q-btn>
              </div>
            </div>
          </q-slide-transition>
        </div>
      </div>
    </div>


    <div class="row justify-end q-pt-md q-pb-sm">
      <mandate-chip v-model="form.mandate" @update:model-value="patch('mandate', $event)"></mandate-chip>
    </div>
    <div
        class="row justify-end q-pb-md"
        v-if="account?._id || form?.accountNumber">
      <q-btn
          :disable="!form.mandate?.acceptedAt || (patching && !form._id)"
          class="bg-primary tw-six text-white"
          push
          no-caps
          @click="patch"
          label="Save Account"
          icon-right="mdi-content-save"
      ></q-btn>
    </div>

  </div>
</template>

<script setup>
  import RoutingInput from 'components/accounts/forms/RoutingInput.vue';
  import OrgAccountDetails from 'components/accounts/forms/OrgAccountDetails.vue';
  import AccountDoubleInput from 'components/accounts/forms/AccountDoubleInput.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import MandateChip from 'components/accounts/cards/MandateChip.vue';
  import AccountStatus from 'components/accounts/cards/AccountStatus.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {useBankAccounts} from 'stores/bank-accounts';
  import {HForm, HSave} from 'src/utils/hForm';
  import {_get} from 'symbol-syntax-utils';
  import {$errNotify} from 'src/utils/global-methods';
  import {genPhrase} from 'src/utils/global-methods';

  const orgStore = useOrgs();
  const accountStore = useBankAccounts();

  const emit = defineEmits(['update:model-value', 'update:status']);
  const props = defineProps({
    modelValue: { required: false },
    org: { required: true }
  })

  const { item: fullOrg } = idGet({
    store: orgStore,
    value: computed(() => props.org)
  })

  const { item: account } = idGet({
    store: accountStore,
    value: computed(() => props.modelValue)
  })


  const { form, save } = HForm({
    value: account,
    store: accountStore,
    validate: true,
    vOpts: ref({
      routingNumber: { name: 'Routing Number', v: ['exactLength:9'] },
      accountNumber: { name: 'Account Number', v: ['notEmpty'] }
    }),
    afterFn: (val) => {
      emit('update:model-value', val);
    },
    beforeFn: (val) => {
      if (!val.owner) val.owner = fullOrg.value?._id;
      if (!val.type) val.type = 'business'
      return val
    }
  })

  const { patchObj, autoSave } = HSave({ form, store: accountStore, pause: ref(true) })

  const patching = ref(false);
  const patch = async (key, val) => {
    patching.value = true;
    if (!form.value._id) save();
    else {
      if (key && val) patchObj.value[key] = val;
      const patched = await accountStore.patch(form.value._id, patchObj.value)
          .catch(e => {
            $errNotify(`Error saving: ${e.message}`)
          })
      if (patched._id) patchObj.value = {};
    }
  }

  const nickname = ref('');

  watch(() => props.modelValue, (nv, ov) => {
    if (nv && (nv._id || nv) !== (ov?._id || ov)) nickname.value = _get(fullOrg.value, `accounts.${nv._id || nv}.name`)
  }, { immediate: true })


  const removing = ref(false);
  const phrase = ref();
  const phraseInput = ref();
  const remove = async (confirm) => {
    if (confirm) {
      if (phrase.value === phraseInput.value) {
        await accountStore.remove(account.value._id)
        emit('update:model-value', undefined);
      } else $errNotify('Phrase does not match')
    } else {
      removing.value = true;
      phrase.value = genPhrase();
    }
  }
</script>

<style lang="scss" scoped>

</style>
