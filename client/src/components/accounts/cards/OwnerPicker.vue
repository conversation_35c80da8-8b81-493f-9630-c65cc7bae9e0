<template>
  <q-item-label header>Add New</q-item-label>

  <div class="row">
    <div class="col-5 q-pa-xs">
      <q-input dense filled label="First Name" v-model="addForm.firstName" @update:model-value="research"></q-input>
    </div>
    <div class="col-5 q-pa-xs">
      <q-input dense filled label="Last Name" v-model="addForm.lastName" @update:model-value="research"></q-input>
    </div>
    <div class="col-2 q-pa-xs">
      <q-btn v-if="addForm.firstName && addForm.lastName" push dense color="primary" glossy icon="mdi-plus"
             @click="addNew">
        <q-spinner color="white" v-if="loading"></q-spinner>
      </q-btn>
    </div>
  </div>
  <q-list separator dense class="_fw">

    <default-item v-for="(opt, i) in options.data" :key="`opt-${i}`" :model-value="opt" :use-atc-store="useAtcStore" @click="emit('update:model-value', opt)"></default-item>
  </q-list>
  <q-list separator dense>
    <q-item-label header>Select {{title}}</q-item-label>
    <default-item
        :item-attrs="{ dense: true }"
        v-for="(prsn, i) in people"
        :key="`op-${i}`"
        :model-value="prsn"
        :use-atc-store="useAtcStore"
        @click="emit('update:model-value', prsn)"
    ></default-item>
  </q-list>
  <div v-if="!people.length" class="q-px-lg text-italic text-ir-deep q-pb-md">{{emptyLabel}}</div>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';

  import {ref} from 'vue';
  import {$errNotify, $limitStr, fakeId} from 'src/utils/global-methods';
  import {usePpls} from 'stores/ppls';
  import {useOrgs} from 'stores/orgs';
  import {useAtcStore} from 'stores/atc-store';
  import {getRegexQuery} from 'src/utils/hQuery';

  const pplStore = usePpls();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    emptyLabel: { default: 'Nobody found' },
    people: Array,
    org: { required: true },
    title: { default: 'Owner' },
    existing: Array,
    existingPath: { default: 'email' },
  })

  const loading = ref(false);
  const addForm = ref({ firstName: '', lastName: '' });
  const addNew = async () => {
    loading.value = true;
    const person = await pplStore.create({ ...addForm.value })
        .catch(err => {
          $errNotify(`Error adding person: ${$limitStr(err.message, 50)}`);
          console.error(err.message);
        })
    addForm.value = { firstName: '', lastName: ''};
    await orgStore.patch(props.org?._id || props.org, { $addToSet: { 'owners': { id: person._id, idService: 'ppls' }}})
        .catch(err => {
          $errNotify(`Error adding person to org: ${$limitStr(err.message, 50)}`);
          console.error(err.message);
        })
    emit('update:model-value', person)
  }
  const to = ref();
  const options = ref({ data: [] });
  const runSearch = async () => {
    loading.value = true;
    const query = { $or: [{_id: { $in: (props.org?.owners || []).map(a => a.id)} }, { inOrgs: { $in: [props.org?._id || fakeId]}}]};
    if(props.existingPath && props.existing?.length) query[props.existingPath] = { $nin: props.existing }
    if(addForm.value.firstName) query.firstName = getRegexQuery(addForm.value.firstName);
    if(addForm.value.lastName) query.lastName = getRegexQuery(addForm.value.lastName);
    options.value = await pplStore.find({ query })
  }
  const research = () => {
    clearTimeout(to.value);
    to.value = setTimeout(() => runSearch(), 1000)
  }
</script>

<style lang="scss" scoped>

</style>
