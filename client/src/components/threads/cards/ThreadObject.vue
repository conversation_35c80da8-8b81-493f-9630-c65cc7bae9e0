<template>
  <div class="_fw">
    <q-tab-panels :model-value="activeId ? 'thread' : 'list'" class="_panel" animated transition-next="jump-up" transition-prev="jump-down">
      <q-tab-panel name="list" class="_panel">
        <q-list separator>
          <q-item clickable v-for="k in Object.keys(modelValue || {})" :key="k" @click="activeId = k">
            <q-item-section>
              <q-item-label>{{modelValue[k].name || 'Untitled'}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-tab-panel>
      <q-tab-panel name="thread" class="_panel">
        <div class="row">
          <q-btn size="sm" dense flat icon="mdi-chevron-left" color="primary" @click="activeId = undefined"></q-btn>
        </div>
        <show-threads></show-threads>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useThreads} from 'stores/threads';
  import ShowThreads from 'components/threads/cards/ShowThreads.vue';

  const store = useThreads();
  const props = defineProps({
    modelValue: { required: true }
  })

  const activeId = ref()
  const { item:parent } = idGet({
    store,
    value: computed(() => activeId.value)
  })
</script>

<style lang="scss" scoped>

</style>
