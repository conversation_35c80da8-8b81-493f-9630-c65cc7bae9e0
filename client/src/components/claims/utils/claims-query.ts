import {computed, ComputedRef, ref, Ref} from 'vue';
import {LocalStorage} from 'symbol-auth-client';
import {idGet} from 'src/utils/id-get';
import {useHouseholds} from 'stores/households';
import {useEnvStore} from 'stores/env';
import {useAtcStore} from 'stores/atc-store';

declare type ValRef = Ref<any> | ComputedRef<any>

type Options = {
    mode: 'provider'|'plan'|'patient',
    person?:ValRef
}
export const claimsQuery = ({ mode, person }:Options) => {
    const hhStore = useHouseholds();
    const envStore = useEnvStore();
    const {item:hh} = idGet({
        store: hhStore as any,
        value: computed(() => person?.value?.household),
        useAtcStore
    })
    const subQ = computed(() => {
        const q:any = {};
        if(mode === 'provider') q.provider = LocalStorage.getItem('provider_id')
        if(mode === 'plan') q.plan = envStore.getPlanId
        else q.patient = { $in: Object.keys(hh.value?.members || {}) }
        return q;
    })
    const sort = ref({
        direction: -1,
        first: 'due'
    })
    const fullQ = computed(() => {
        const q = {...subQ.value}
        const { first, direction } = sort.value || { direction: -1 }
        if(first === 'due') q.$sort = { due: direction, createdAt: direction };
        else q.$sort = { createdAt: direction, due: direction }
        return q;
    })
    return {
        subQ,
        fullQ,
        sort,
        hhStore,
        hh
    }
}
