<template>
  <q-page class="_bg_ow">
    <div class="row justify-center">
      <div class="_sent pd5 pw2">
        <div class="__c">
          <div class="__title">Payment Details</div>
          <pay-claim :claim="claim" :visit="visit"></pay-claim>
        </div>
        <div class="__c">
          <div class="__title">Line Item</div>
          <claims-card :model-value="claim"></claims-card>
        </div>
        <div class="__c">
          <div class="__title">Bill</div>
          <claims-table @select="setClaim" :visit="visit"></claims-table>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import ClaimsCard from 'components/claims/cards/ClaimsCard.vue';
  import ClaimsTable from 'components/claims/lists/ClaimsTable.vue';
  import PayClaim from 'components/claims/claim-requests/forms/PayClaim.vue';

  import {idGet} from 'src/utils/id-get';
  import {useClaims} from 'stores/claims';
  import {computed} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import {useVisits} from 'stores/visits';


  const route = useRoute();
  const router = useRouter();
  const claimStore = useClaims();
  const visitStore = useVisits();

  const { item: claim } = idGet({
    store: claimStore,
    value: computed(() => route.params.claimId)
  })
  const { item: visit } = idGet({
    store: visitStore,
    value: computed(() => claim.value?.visit)
  })

  const setClaim = (val) => router.push({ ...route, params: { claimId: val._id}})
</script>

<style lang="scss" scoped>
  .__c {
    padding: 30px min(15px, 2vw);
    border-radius: 20px;
    box-shadow: 0 2px 18px -6px #999;
    background: white;
    margin: 30px 0;
    position: relative;

    .__title {
      letter-spacing: .1rem;
      //text-transform: uppercase;
      position: absolute;
      top: 0;
      left: 5%;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: linear-gradient(180deg, var(--q-p2),var(--q-p0));
      color: var(--q-p12);
      font-weight: 600;
      padding: 6px 8px;
      font-size: 1rem;
    }
  }
</style>
