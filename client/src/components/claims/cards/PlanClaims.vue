<template>
  <div class="_fw">
    <div class="row items-center">
      <care-filters set="claims" v-model="query" :plan="fullPlan">
      </care-filters>

      <q-chip color="transparent" clickable>
        <q-icon color="primary" name="mdi-calendar"></q-icon>
        <span class="q-mx-sm font-3-4r">{{dateLabel}}</span>
        <q-icon name="mdi-menu-down"></q-icon>
        <q-menu>
          <div class="w400 mw100 bg-white q-pa-sm">
            <div class="row items-center no-wrap">
              <inline-date
                  class="q-mx-xs"
                  :input-attrs="{ filled: true, dense: true, label: 'From Date' }"
                  v-model="dates.from"
              >
                <template v-slot:append>
                  <q-btn dense flat size="sm" v-if="dates.from" color="red" icon="mdi-close" @click="dates.from = undefined"></q-btn>
                </template>
              </inline-date>
              <inline-date
                  class="q-mx-xs"
                  :input-attrs="{ filled: true, dense: true, label: 'To Date' }"
                  v-model="dates.to"
              >
                <template v-slot:append>
                  <q-btn dense flat size="sm" v-if="dates.to" color="red" icon="mdi-close" @click="dates.to = undefined"></q-btn>
                </template>
              </inline-date>
            </div>
          </div>
        </q-menu>
      </q-chip>
      <q-chip color="transparent" clickable @click="sort = sort * -1">
        <q-icon color="accent" :name="`mdi-sort-calendar-${sort === 1 ? 'descending' : 'ascending'}`"></q-icon>
        <span class="q-ml-sm font-3-4r">{{sort === 1 ? 'Oldest' : 'Newest'}}</span>
      </q-chip>
    </div>

    <q-table
        flat
        :rows="c$.data"
        :columns="cols"
    >
      <template v-slot:no-data>
        <div class="q-pa-sm text-italic">No claims</div>
      </template>
      <template v-slot:header="scope">
        <!--        <q-th auto-width></q-th>-->
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope">
          <template v-if="!loading">
            <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
              <component
                  v-if="col.component"
                  :is="col.component"
                  v-bind="col.attrs(scope.row)"
              ></component>
              <div v-else>{{ col.value }}</div>
            </q-td>
          </template>
          <q-td v-else class="q-pa-md">
            <q-spinner color="grey-3" size="20px"></q-spinner>
          </q-td>
        </q-tr>
      </template>
      <template v-slot:bottom>
        <div class="row items-center _fw">
          <div class="font-7-8r text-grey-8">{{ c$.data?.length ? 1 : 0 }} - {{ c$.data?.length || 0 }} of
            {{ c$.total }}
          </div>
          <q-space></q-space>
          <q-pagination
              @update:model-value="c$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup>
  import CareFilters from 'components/care/utils/CareFilters.vue';
  import ObscureChip from 'components/households/cards/ObscureChip.vue';
  import StatusChip from 'components/care/cards/StatusChip.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import ProcedureOrMed from 'components/claims/cards/ProcedureOrMed.vue';
  import ClaimBalance from 'components/claims/cards/ClaimBalance.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';

  import {computed, onMounted, ref} from 'vue';
  import {useAtcStore} from 'stores/atc-store';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {_flatten} from 'symbol-syntax-utils';
  import {useProviders} from 'stores/providers';
  import {useClaims} from 'stores/claims';

  import {formatDate} from 'src/utils/date-utils';

  const planStore = usePlans();
  const providerStore = useProviders();
  const claimStore = useClaims();

  const props = defineProps({
    plan: { required: true },
    query: { required: false }
  })

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  })

  const sort = ref(-1);
  const query = ref({});
  const dates = ref({
    from: '',
    to: ''
  })
  const dateLabel = computed(() => {
    let str = '';
    if(dates.value.from) str += formatDate(dates.value.from, 'MM-DD-YYYY');
    if(dates.value.to) str += ` - ${formatDate(dates.value.from, 'MM-DD-YYYY')}`;
    return str;
  })

  const qry = computed(() => {
    const q = {};
    if(dates.value.from) q.date = { $gte: new Date(dates.value.from) }
    if(dates.value.to) q.date = { ...q.date, $lt: new Date(dates.value.to) }
    return q;
  })

  const { h$: c$, pagination } = HFind({
    store: claimStore,
    limit: ref(10),
    pause: computed(() => !fullPlan.value),
    params: computed(() => {
      const q = {
        ...query.value,
        $sort: { date: sort.value },
        plan: fullPlan.value?._id,
        ...qry.value
      }
      return {
        query: q
      }
    })
  })

  const { h$: pv$ } = HFind({
    store: providerStore,
    pause: computed(() => !c$.data?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: _flatten((c$.data || []).map(a => a.providers)) }
        }
      }
    })
  })

  const cols = computed(() => {
    return [
      {
        name: 'Treatment',
        component: ProcedureOrMed,
        attrs: (row) => {
          return {
            limit: 25,
            claim: row
          }
        }
      },
      {
        name: 'Provider',
        component: DefaultChip,
        attrs: (row) => {
          return {
            limit: 30,
            chipAttrs: { size: 'sm', color: 'transparent' },
            store: providerStore,
            modelValue: row.provider,
            useAtcStore: useAtcStore
          }
        }
      },
      {
        name: 'Status',
        component: StatusChip,
        attrs: (row) => {
          return {
            label: '',
            size: 'sm',
            modelValue: row.status
          }
        }
      },
      {
        name: 'Balance',
        component: ClaimBalance,
        attrs: (row) => {
          return {
            modelValue: row,
            dense: true
          }
        }
      },
      {
        name: 'Participant',
        component: ObscureChip,
        attrs: (row) => {
          return {
            modelValue: row.patient
          }
        }
      },
      {
        name: 'Date',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: formatDate(row.date, 'MM-DD-YYYY')
            }
          }
        }
      }
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })

  const loading = ref(true);
  const unload = (tries = 0) => {
    if (pv$.total) loading.value = false;
    if (tries < 5) {
      setTimeout(() => {
        unload(tries + 1)
      }, 200)
    } else loading.value = false
  }
  onMounted(() => {
    unload()
  })

</script>

<style lang="scss" scoped>

</style>
